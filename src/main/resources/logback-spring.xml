<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="10 seconds">

	<!--配置规则类的位置 -->
	<!--<conversionRule conversionWord="ip"-->
	<!--converterClass="com.eastmoney.route.config.IPLogConfig"/>-->
	<!-- 配置logstash不同环境参数 -->
	<springProfile name="dev">
		<include resource="org/springframework/boot/logging/logback/base.xml"/>
		<property name="appsuffix" value="fortuneservice_dev"/>
		<property name="logstash_home" value="**************"/>
		<property name="logstash_port" value="5690"/>
	</springProfile>

	<springProfile name="test">
		<property name="appsuffix" value="fortuneservice_test"/>
		<property name="logstash_home" value="ls4.elk.yunwei"/>
		<property name="logstash_port" value="5727"/>
	</springProfile>

	<springProfile name="uat">
		<property name="appsuffix" value="fortuneservice_uat"/>
		<property name="logstash_home" value="ls4.elk.yunwei"/>
		<property name="logstash_port" value="5727"/>
	</springProfile>

	<springProfile name="prod">
		<property name="appsuffix" value="fortuneservice_prod"/>
		<property name="logstash_home" value="ls4.elk.yunwei"/>
		<property name="logstash_port" value="5727"/>
	</springProfile>

	<conversionRule conversionWord="tid"
					converterClass="org.apache.skywalking.apm.toolkit.log.logback.v1.x.LogbackPatternConverter"/>

	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		    <!-- encoders are assigned the type          ch.qos.logback.classic.encoder.PatternLayoutEncoder
			by default -->
		   
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
			</pattern>
		</encoder>
		 
	</appender>

	<appender name="INFO_FILE"
			  class="ch.qos.logback.core.rolling.RollingFileAppender">
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>ERROR</level>
			<onMatch>DENY</onMatch>
			<onMismatch>ACCEPT</onMismatch>
		</filter>
		<File>${LOG_PATH}/info.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_PATH}/info-%d{yyyyMMdd}.log.%i
			</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy
					class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>500MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
			<maxHistory>3</maxHistory>
			<totalSizeCap>5GB</totalSizeCap>
			<cleanHistoryOnStart>true</cleanHistoryOnStart>
		</rollingPolicy>
		<layout class="ch.qos.logback.classic.PatternLayout">
			<Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} -%msg%n
			</Pattern>
		</layout>
	</appender>

	<appender name="ERROR_FILE"
			  class="ch.qos.logback.core.rolling.RollingFileAppender">
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>ERROR</level>
		</filter>
		<File>${LOG_PATH}/error.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_PATH}/error-%d{yyyyMMdd}.log.%i
			</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy
					class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>500MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
			<maxHistory>3</maxHistory>
			<totalSizeCap>5GB</totalSizeCap>
			<cleanHistoryOnStart>true</cleanHistoryOnStart>
		</rollingPolicy>
		<layout class="ch.qos.logback.classic.PatternLayout">
			<Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} -%msg%n
			</Pattern>
		</layout>
	</appender>

	<!-- 配置logstash -->
	<appender name="info_stash"
			  class="net.logstash.logback.appender.LogstashUdpSocketAppender">
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>ERROR</level>
			<onMatch>DENY</onMatch>
			<onMismatch>ACCEPT</onMismatch>
		</filter>
		<!--logstash服务器IP -->
		<host>${logstash_home}</host>
		<!-- port is optional (default value shown) -->
		<port>${logstash_port}</port>
		<layout class="net.logstash.logback.layout.LoggingEventCompositeJsonLayout">
			<providers>
				<pattern>
					<pattern>
						{
						"level": "%level",
						"Trace": "%tid",
						"thread_name": "%thread",
						"logger_name": "%logger",
						"message": "%message",
						"stackTrace": "%exception{10}",
						"appname":"${appsuffix}",
						"traceid":"%X{traceid}"
						}
					</pattern>
				</pattern>
			</providers>
		</layout>
	</appender>


	<!-- 配置logstash -->
	<appender name="error_stash"
			  class="net.logstash.logback.appender.LogstashUdpSocketAppender">

		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>ERROR</level>
		</filter>
		<!--logstash服务器IP -->
		<host>${logstash_home}</host>
		<!-- port is optional (default value shown) -->
		<port>${logstash_port}</port>
		<layout class="net.logstash.logback.layout.LoggingEventCompositeJsonLayout">
			<providers>
				<pattern>
					<pattern>
						{
						"level": "%level",
						"Trace": "%tid",
						"thread_name": "%thread",
						"logger_name": "%logger",
						"message": "%message",
						"stackTrace": "%exception{10}",
						"appname":"${appsuffix}",
						"traceid":"%X{traceid}"
						}
					</pattern>
				</pattern>
			</providers>
		</layout>
	</appender>

	<springProfile name="dev">
		<root level="INFO">
			<!-- <appender-ref ref="STDOUT" /> -->
			<appender-ref ref="INFO_FILE"/>
			<appender-ref ref="ERROR_FILE"/>
			<appender-ref ref="info_stash"/>
			<appender-ref ref="error_stash"/>
		</root>
	</springProfile>

	<springProfile name="test">
		<root level="INFO">
			<!-- <appender-ref ref="STDOUT" /> -->
			<appender-ref ref="INFO_FILE"/>
			<appender-ref ref="ERROR_FILE"/>
			<appender-ref ref="info_stash"/>
			<appender-ref ref="error_stash"/>
		</root>
	</springProfile>
	<springProfile name="uat">
		<root level="INFO">
			<!-- <appender-ref ref="STDOUT" /> -->
			<appender-ref ref="INFO_FILE"/>
			<appender-ref ref="ERROR_FILE"/>
			<appender-ref ref="info_stash"/>
			<appender-ref ref="error_stash"/>
		</root>
	</springProfile>
	<springProfile name="uatpj">
		<root level="INFO">
			<!-- <appender-ref ref="STDOUT" /> -->
			<appender-ref ref="INFO_FILE"/>
			<appender-ref ref="ERROR_FILE"/>
			<appender-ref ref="info_stash"/>
			<appender-ref ref="error_stash"/>
		</root>
	</springProfile>
	<springProfile name="prod">
		<root level="INFO">
			<!-- <appender-ref ref="STDOUT" /> -->
			<appender-ref ref="INFO_FILE"/>
			<appender-ref ref="ERROR_FILE"/>
			<appender-ref ref="info_stash"/>
			<appender-ref ref="error_stash"/>
		</root>
	</springProfile>
	<springProfile name="prodpj">
		<root level="INFO">
			<appender-ref ref="INFO_FILE"/>
			<appender-ref ref="ERROR_FILE"/>
			<appender-ref ref="info_stash"/>
			<appender-ref ref="error_stash"/>
		</root>
	</springProfile>

	<!-- 打印结果集 <logger name="druid.sql.ResultSet" level="DEBUG"> <appender-ref
        ref="STDOUT" /> </logger> -->

	<!-- 打印sql语句 -->
	<!-- <logger name="druid.sql.Statement" level="DEBUG"> <appender-ref ref="STDOUT"
        /> </logger> -->

</configuration>
