app:
  id: ttfund.web.service.fortune
apollo:
  cluster: test
  cacheDir: /vdb/opt/data/test/
  meta: http://*************:8080
  bootstrap:
    namespaces: application,web.core.common
    enabled: true
    eagerLoad:
      enabled: true

xxl:
  job:
    executor:
      appname: fund-fortune-service
      ip:
      port: 9817
      logpath: /vdb/apilog/xxl/fund-fortune-service
    accesstoken:
logging:
  level:
    ttfund.web.fortuneservice.dao.sqlserver: DEBUG