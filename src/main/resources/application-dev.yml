app:
  id: ttfund.web.service.fortune
apollo:
  cluster: default
  cacheDir: /vdb/opt/data/dev/
  meta: http://**************:8080/
  bootstrap:
    namespaces: application,KF.core.common
    enabled: true
    eagerLoad:
      enabled: true

xxl:
  job:
    executor:
      appname: fund-fortune-service
      ip:
      port: 9817
      logpath: /vdb/apilog/xxl/fund-fortune-service
    accesstoken:
logging:
  level:
    ttfund.web.fortuneservice.dao.sqlserver: DEBUG
