<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.fortuneservice.dao.sqlserver.VIPMeetingMapper">
    <resultMap id="BaseResultMap" type="ttfund.web.fortuneservice.model.entity.VIPMeeting">
        <id column="id" jdbcType="NVARCHAR" property="id"/>
        <result column="cfh_id" jdbcType="NVARCHAR" property="cfhId"/>
        <result column="min_vip_level" jdbcType="INTEGER" property="minVipLevel"/>
        <result column="meeting_start_time" jdbcType="TIMESTAMP" property="meetingStartTime"/>
        <result column="meeting_end_time" jdbcType="TIMESTAMP" property="meetingEndTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="type_id" jdbcType="NVARCHAR" property="typeId"/>
        <result column="deleted" jdbcType="INTEGER" property="deleted"/>
        <result column="ai_edit" jdbcType="INTEGER" property="aiEdit"/>
        <result column="status" jdbcType="NVARCHAR" property="status"/>
        <result column="ai_status" jdbcType="INTEGER" property="aiStatus"/>
        <result column="extend" jdbcType="NVARCHAR" property="extend"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , cfh_id, min_vip_level, meeting_start_time, meeting_end_time, create_time, update_time,
    type_id, deleted, ai_edit, status, ai_status, extend
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from TB_VIPMeeting
        where id = #{id,jdbcType=NVARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from TB_VIPMeeting
        where id = #{id,jdbcType=NVARCHAR}
    </delete>
    <insert id="insert" parameterType="ttfund.web.fortuneservice.model.entity.VIPMeeting">
        insert into TB_VIPMeeting (id, cfh_id, min_vip_level,
                                   meeting_start_time, meeting_end_time, create_time,
                                   update_time, type_id, deleted,
                                   ai_edit, status, ai_status,
                                   extend)
        values (#{id,jdbcType=NVARCHAR}, #{cfhId,jdbcType=NVARCHAR}, #{minVipLevel,jdbcType=INTEGER},
                #{meetingStartTime,jdbcType=TIMESTAMP}, #{meetingEndTime,jdbcType=TIMESTAMP},
                #{createTime,jdbcType=TIMESTAMP},
                #{updateTime,jdbcType=TIMESTAMP}, #{typeId,jdbcType=NVARCHAR}, #{deleted,jdbcType=INTEGER},
                #{aiEdit,jdbcType=INTEGER}, #{status,jdbcType=NVARCHAR}, #{aiStatus,jdbcType=INTEGER},
                #{extend,jdbcType=NVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="ttfund.web.fortuneservice.model.entity.VIPMeeting">
        insert into TB_VIPMeeting
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="cfhId != null">
                cfh_id,
            </if>
            <if test="minVipLevel != null">
                min_vip_level,
            </if>
            <if test="meetingStartTime != null">
                meeting_start_time,
            </if>
            <if test="meetingEndTime != null">
                meeting_end_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="typeId != null">
                type_id,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="aiEdit != null">
                ai_edit,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="aiStatus != null">
                ai_status,
            </if>
            <if test="extend != null">
                extend,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=NVARCHAR},
            </if>
            <if test="cfhId != null">
                #{cfhId,jdbcType=NVARCHAR},
            </if>
            <if test="minVipLevel != null">
                #{minVipLevel,jdbcType=INTEGER},
            </if>
            <if test="meetingStartTime != null">
                #{meetingStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="meetingEndTime != null">
                #{meetingEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="typeId != null">
                #{typeId,jdbcType=NVARCHAR},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=INTEGER},
            </if>
            <if test="aiEdit != null">
                #{aiEdit,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=NVARCHAR},
            </if>
            <if test="aiStatus != null">
                #{aiStatus,jdbcType=INTEGER},
            </if>
            <if test="extend != null">
                #{extend,jdbcType=NVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="ttfund.web.fortuneservice.model.entity.VIPMeeting">
        update TB_VIPMeeting
        <set>
            <if test="cfhId != null">
                cfh_id = #{cfhId,jdbcType=NVARCHAR},
            </if>
            <if test="minVipLevel != null">
                min_vip_level = #{minVipLevel,jdbcType=INTEGER},
            </if>
            <if test="meetingStartTime != null">
                meeting_start_time = #{meetingStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="meetingEndTime != null">
                meeting_end_time = #{meetingEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="typeId != null">
                type_id = #{typeId,jdbcType=NVARCHAR},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=INTEGER},
            </if>
            <if test="aiEdit != null">
                ai_edit = #{aiEdit,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=NVARCHAR},
            </if>
            <if test="aiStatus != null">
                ai_status = #{aiStatus,jdbcType=INTEGER},
            </if>
            <if test="extend != null">
                extend = #{extend,jdbcType=NVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=NVARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="ttfund.web.fortuneservice.model.entity.VIPMeeting">
        update TB_VIPMeeting
        set cfh_id             = #{cfhId,jdbcType=NVARCHAR},
            min_vip_level      = #{minVipLevel,jdbcType=INTEGER},
            meeting_start_time = #{meetingStartTime,jdbcType=TIMESTAMP},
            meeting_end_time   = #{meetingEndTime,jdbcType=TIMESTAMP},
            create_time        = #{createTime,jdbcType=TIMESTAMP},
            update_time        = #{updateTime,jdbcType=TIMESTAMP},
            type_id            = #{typeId,jdbcType=NVARCHAR},
            deleted            = #{deleted,jdbcType=INTEGER},
            ai_edit            = #{aiEdit,jdbcType=INTEGER},
            status             = #{status,jdbcType=NVARCHAR},
            ai_status          = #{aiStatus,jdbcType=INTEGER},
            extend             = #{extend,jdbcType=NVARCHAR}
        where id = #{id,jdbcType=NVARCHAR}
    </update>

    <select id="selectSuccessById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from TB_VIPMeeting
        where id = #{id,jdbcType=NVARCHAR} and status = 'MINUTES_REVIEW_SUCCESS' and deleted = 0
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from TB_VIPMeeting
        where status = 'MINUTES_REVIEW_SUCCESS' and deleted = 0 and ai_status != 1 and meeting_end_time >= #{date}
    </select>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from TB_VIPMeeting
        where id = #{id,jdbcType=NVARCHAR} and deleted = 0
    </select>
    <select id="selectSummaryList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from TB_VIPMeeting
        where status = 'MINUTES_REVIEW_SUCCESS' and deleted = 0 and ai_status = 0
    </select>
</mapper>