<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.fortuneservice.dao.sqlserver.VIPAIReportDetailMapper">
    <resultMap id="BaseResultMap" type="ttfund.web.fortuneservice.model.entity.VIPAIReportDetail">
        <id column="ID" jdbcType="NVARCHAR" property="id"/>
        <result column="ReportID" jdbcType="NVARCHAR" property="reportId"/>
        <result column="Title" jdbcType="NVARCHAR" property="title"/>
        <result column="Content" jdbcType="NVARCHAR" property="content"/>
        <result column="References" jdbcType="NVARCHAR" property="references"/>
        <result column="ReportType" jdbcType="NVARCHAR" property="reportType"/>
        <result column="Type" jdbcType="NVARCHAR" property="type"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UpdateTime" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, ReportID, Title, Content, [References], ReportType, Type, CreateTime, UpdateTime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM Tb_VIPAIReportDetail
        WHERE ID = #{id,jdbcType=NVARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        DELETE FROM Tb_VIPAIReportDetail
        WHERE ID = #{id,jdbcType=NVARCHAR}
    </delete>

    <insert id="insert" parameterType="ttfund.web.fortuneservice.model.entity.VIPAIReportDetail">
        INSERT INTO Tb_VIPAIReportDetail (ID, ReportID, Title, Content, [References], ReportType, Type, CreateTime, UpdateTime)
        VALUES (#{id,jdbcType=NVARCHAR}, #{reportId,jdbcType=NVARCHAR}, #{title,jdbcType=NVARCHAR},
                #{content,jdbcType=NVARCHAR}, #{references,jdbcType=NVARCHAR}, #{reportType,jdbcType=NVARCHAR},
                #{type,jdbcType=NVARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>

    <insert id="insertSelective" parameterType="ttfund.web.fortuneservice.model.entity.VIPAIReportDetail">
        INSERT INTO Tb_VIPAIReportDetail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="reportId != null">
                ReportID,
            </if>
            <if test="title != null">
                Title,
            </if>
            <if test="content != null">
                Content,
            </if>
            <if test="references != null">
                [References],
            </if>
            <if test="reportType != null">
                ReportType,
            </if>
            <if test="type != null">
                Type,
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="updateTime != null">
                UpdateTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=NVARCHAR},
            </if>
            <if test="reportId != null">
                #{reportId,jdbcType=NVARCHAR},
            </if>
            <if test="title != null">
                #{title,jdbcType=NVARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=NVARCHAR},
            </if>
            <if test="references != null">
                #{references,jdbcType=NVARCHAR},
            </if>
            <if test="reportType != null">
                #{reportType,jdbcType=NVARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=NVARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="ttfund.web.fortuneservice.model.entity.VIPAIReportDetail">
        UPDATE Tb_VIPAIReportDetail
        <set>
            <if test="reportId != null">
                ReportID = #{reportId,jdbcType=NVARCHAR},
            </if>
            <if test="title != null">
                Title = #{title,jdbcType=NVARCHAR},
            </if>
            <if test="content != null">
                Content = #{content,jdbcType=NVARCHAR},
            </if>
            <if test="references != null">
                [References] = #{references,jdbcType=NVARCHAR},
            </if>
            <if test="reportType != null">
                ReportType = #{reportType,jdbcType=NVARCHAR},
            </if>
            <if test="type != null">
                Type = #{type,jdbcType=NVARCHAR},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UpdateTime = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        WHERE ID = #{id,jdbcType=NVARCHAR}
    </update>

    <update id="updateByPrimaryKey" parameterType="ttfund.web.fortuneservice.model.entity.VIPAIReportDetail">
        UPDATE Tb_VIPAIReportDetail
        SET ReportID = #{reportId,jdbcType=NVARCHAR},
            Title = #{title,jdbcType=NVARCHAR},
            Content = #{content,jdbcType=NVARCHAR},
            [References] = #{references,jdbcType=NVARCHAR},
            ReportType = #{reportType,jdbcType=NVARCHAR},
            Type = #{type,jdbcType=NVARCHAR},
            CreateTime = #{createTime,jdbcType=TIMESTAMP},
            UpdateTime = #{updateTime,jdbcType=TIMESTAMP}
        WHERE ID = #{id,jdbcType=NVARCHAR}
    </update>

    <select id="selectByReportId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM Tb_VIPAIReportDetail
        WHERE ReportID = #{reportId,jdbcType=NVARCHAR}
    </select>

    <select id="selectByReportIdAndType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM Tb_VIPAIReportDetail
        WHERE ReportID = #{reportId,jdbcType=NVARCHAR}
          AND ReportType = #{reportType,jdbcType=NVARCHAR}
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO Tb_VIPAIReportDetail (ID, ReportID, Title, Content, [References], ReportType, Type, CreateTime, UpdateTime)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=NVARCHAR}, #{item.reportId,jdbcType=NVARCHAR}, #{item.title,jdbcType=NVARCHAR},
             #{item.content,jdbcType=NVARCHAR}, #{item.references,jdbcType=NVARCHAR}, #{item.reportType,jdbcType=NVARCHAR},
             #{item.type,jdbcType=NVARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
</mapper>
