<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.fortuneservice.dao.sqlserver.VIPAIReportMapper">
    <resultMap id="BaseResultMap" type="ttfund.web.fortuneservice.model.entity.VIPAIReport">
        <id column="ID" jdbcType="NVARCHAR" property="id"/>
        <result column="CFHID" jdbcType="NVARCHAR" property="cfhid"/>
        <result column="TaskConfigID" jdbcType="NVARCHAR" property="taskConfigId"/>
        <result column="TaskCycleMark" jdbcType="NVARCHAR" property="taskCycleMark"/>
        <result column="ReportDate" jdbcType="NVARCHAR" property="reportDate"/>
        <result column="Status" jdbcType="NVARCHAR" property="status"/>
        <result column="Index" jdbcType="INTEGER" property="index"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UpdateTime" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, CFHID, TaskConfigID, TaskCycleMark, ReportDate, Status, [Index], CreateTime, UpdateTime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM Tb_VIPAIReport
        WHERE ID = #{id,jdbcType=NVARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        DELETE FROM Tb_VIPAIReport
        WHERE ID = #{id,jdbcType=NVARCHAR}
    </delete>

    <insert id="insert" parameterType="ttfund.web.fortuneservice.model.entity.VIPAIReport">
        INSERT INTO Tb_VIPAIReport (ID, CFHID, TaskConfigID, TaskCycleMark, ReportDate, Status, [Index], CreateTime, UpdateTime)
        VALUES (#{id,jdbcType=NVARCHAR}, #{cfhid,jdbcType=NVARCHAR}, #{taskConfigId,jdbcType=NVARCHAR},
                #{taskCycleMark,jdbcType=NVARCHAR}, #{reportDate,jdbcType=NVARCHAR}, #{status,jdbcType=NVARCHAR},
                #{index,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>

    <insert id="insertSelective" parameterType="ttfund.web.fortuneservice.model.entity.VIPAIReport">
        INSERT INTO Tb_VIPAIReport
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="cfhid != null">
                CFHID,
            </if>
            <if test="taskConfigId != null">
                TaskConfigID,
            </if>
            <if test="taskCycleMark != null">
                TaskCycleMark,
            </if>
            <if test="reportDate != null">
                ReportDate,
            </if>
            <if test="status != null">
                Status,
            </if>
            <if test="index != null">
                [Index],
            </if>
            <if test="createTime != null">
                CreateTime,
            </if>
            <if test="updateTime != null">
                UpdateTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=NVARCHAR},
            </if>
            <if test="cfhid != null">
                #{cfhid,jdbcType=NVARCHAR},
            </if>
            <if test="taskConfigId != null">
                #{taskConfigId,jdbcType=NVARCHAR},
            </if>
            <if test="taskCycleMark != null">
                #{taskCycleMark,jdbcType=NVARCHAR},
            </if>
            <if test="reportDate != null">
                #{reportDate,jdbcType=NVARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=NVARCHAR},
            </if>
            <if test="index != null">
                #{index,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="ttfund.web.fortuneservice.model.entity.VIPAIReport">
        UPDATE Tb_VIPAIReport
        <set>
            <if test="cfhid != null">
                CFHID = #{cfhid,jdbcType=NVARCHAR},
            </if>
            <if test="taskConfigId != null">
                TaskConfigID = #{taskConfigId,jdbcType=NVARCHAR},
            </if>
            <if test="taskCycleMark != null">
                TaskCycleMark = #{taskCycleMark,jdbcType=NVARCHAR},
            </if>
            <if test="reportDate != null">
                ReportDate = #{reportDate,jdbcType=NVARCHAR},
            </if>
            <if test="status != null">
                Status = #{status,jdbcType=NVARCHAR},
            </if>
            <if test="index != null">
                [Index] = #{index,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                CreateTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UpdateTime = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        WHERE ID = #{id,jdbcType=NVARCHAR}
    </update>

    <update id="updateByPrimaryKey" parameterType="ttfund.web.fortuneservice.model.entity.VIPAIReport">
        UPDATE Tb_VIPAIReport
        SET CFHID = #{cfhid,jdbcType=NVARCHAR},
            TaskConfigID = #{taskConfigId,jdbcType=NVARCHAR},
            TaskCycleMark = #{taskCycleMark,jdbcType=NVARCHAR},
            ReportDate = #{reportDate,jdbcType=NVARCHAR},
            Status = #{status,jdbcType=NVARCHAR},
            [Index] = #{index,jdbcType=INTEGER},
            CreateTime = #{createTime,jdbcType=TIMESTAMP},
            UpdateTime = #{updateTime,jdbcType=TIMESTAMP}
        WHERE ID = #{id,jdbcType=NVARCHAR}
    </update>

    <select id="selectByCfhIdAndReportDate" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM Tb_VIPAIReport
        WHERE CFHID = #{cfhid,jdbcType=NVARCHAR}
          AND ReportDate = #{reportDate,jdbcType=NVARCHAR}
    </select>

    <select id="selectByTaskConfigIdAndReportDate" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM Tb_VIPAIReport
        WHERE TaskConfigID = #{taskConfigId,jdbcType=NVARCHAR}
          AND ReportDate = #{reportDate,jdbcType=NVARCHAR}
    </select>
</mapper>
