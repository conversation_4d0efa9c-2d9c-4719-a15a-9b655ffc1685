<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ttfund.web.fortuneservice.dao.sqlserver.VIPMeetingLibMapper">
    <resultMap id="BaseResultMap" type="ttfund.web.fortuneservice.model.entity.VIPMeetingLib">
        <id column="id" jdbcType="NVARCHAR" property="id"/>
        <result column="funds" jdbcType="NVARCHAR" property="funds"/>
        <result column="indexs" jdbcType="NVARCHAR" property="indexs"/>
        <result column="sectors" jdbcType="NVARCHAR" property="sectors"/>
        <result column="topic" jdbcType="NVARCHAR" property="topic"/>
        <result column="viewpoint" jdbcType="NVARCHAR" property="viewpoint"/>
        <result column="meetingId" jdbcType="NVARCHAR" property="meetingid"/>
        <result column="deleted" jdbcType="INTEGER" property="deleted"/>
        <result column="creat_time" jdbcType="TIMESTAMP" property="creatTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , funds, indexs, sectors, topic, viewpoint, meetingId, deleted, creat_time, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from TB_VIPMeetingLib
        where id = #{id,jdbcType=NVARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from TB_VIPMeetingLib
        where id = #{id,jdbcType=NVARCHAR}
    </delete>
    <insert id="insert" parameterType="ttfund.web.fortuneservice.model.entity.VIPMeetingLib">
        insert into TB_VIPMeetingLib (id, funds, indexs,
                                      sectors, topic, viewpoint,
                                      meetingId, deleted, creat_time,
                                      update_time)
        values (#{id,jdbcType=NVARCHAR}, #{funds,jdbcType=NVARCHAR}, #{indexs,jdbcType=NVARCHAR},
                #{sectors,jdbcType=NVARCHAR}, #{topic,jdbcType=NVARCHAR}, #{viewpoint,jdbcType=NVARCHAR},
                #{meetingid,jdbcType=NVARCHAR}, #{deleted,jdbcType=INTEGER}, #{creatTime,jdbcType=TIMESTAMP},
                #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="ttfund.web.fortuneservice.model.entity.VIPMeetingLib">
        insert into TB_VIPMeetingLib
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="funds != null">
                funds,
            </if>
            <if test="indexs != null">
                indexs,
            </if>
            <if test="sectors != null">
                sectors,
            </if>
            <if test="topic != null">
                topic,
            </if>
            <if test="viewpoint != null">
                viewpoint,
            </if>
            <if test="meetingid != null">
                meetingId,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="creatTime != null">
                creat_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=NVARCHAR},
            </if>
            <if test="funds != null">
                #{funds,jdbcType=NVARCHAR},
            </if>
            <if test="indexs != null">
                #{indexs,jdbcType=NVARCHAR},
            </if>
            <if test="sectors != null">
                #{sectors,jdbcType=NVARCHAR},
            </if>
            <if test="topic != null">
                #{topic,jdbcType=NVARCHAR},
            </if>
            <if test="viewpoint != null">
                #{viewpoint,jdbcType=NVARCHAR},
            </if>
            <if test="meetingid != null">
                #{meetingid,jdbcType=NVARCHAR},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=INTEGER},
            </if>
            <if test="creatTime != null">
                #{creatTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="ttfund.web.fortuneservice.model.entity.VIPMeetingLib">
        update TB_VIPMeetingLib
        <set>
            <if test="funds != null">
                funds = #{funds,jdbcType=NVARCHAR},
            </if>
            <if test="indexs != null">
                indexs = #{indexs,jdbcType=NVARCHAR},
            </if>
            <if test="sectors != null">
                sectors = #{sectors,jdbcType=NVARCHAR},
            </if>
            <if test="topic != null">
                topic = #{topic,jdbcType=NVARCHAR},
            </if>
            <if test="viewpoint != null">
                viewpoint = #{viewpoint,jdbcType=NVARCHAR},
            </if>
            <if test="meetingid != null">
                meetingId = #{meetingid,jdbcType=NVARCHAR},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=INTEGER},
            </if>
            <if test="creatTime != null">
                creat_time = #{creatTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=NVARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="ttfund.web.fortuneservice.model.entity.VIPMeetingLib">
        update TB_VIPMeetingLib
        set funds       = #{funds,jdbcType=NVARCHAR},
            indexs      = #{indexs,jdbcType=NVARCHAR},
            sectors     = #{sectors,jdbcType=NVARCHAR},
            topic       = #{topic,jdbcType=NVARCHAR},
            viewpoint   = #{viewpoint,jdbcType=NVARCHAR},
            meetingId   = #{meetingid,jdbcType=NVARCHAR},
            deleted     = #{deleted,jdbcType=INTEGER},
            creat_time  = #{creatTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=NVARCHAR}
    </update>

    <select id="selectByMeetingId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from TB_VIPMeetingLib
        where meetingId = #{meetingId,jdbcType=NVARCHAR}
    </select>
</mapper>