package ttfund.web.fortuneservice.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.CFHDataToMongoService;

import javax.annotation.Resource;

/**
 * 更新财富号信息到mongo 2min/次
 * <AUTHOR>
 * @version 1.0.0
 * @className SetCFHDataToMongoJob
 * @date 2023/4/14 15:33
 */
@JobHandler(value="SetCFHDataToMongo")
@Component
public class SetCFHDataToMongoJob extends IJobHandler {

    @Resource
    private CFHDataToMongoService service;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        service.handCFHData();
        return ReturnT.SUCCESS;
    }
}
