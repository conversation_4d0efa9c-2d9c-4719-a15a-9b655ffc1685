package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.VIPMultiMonthlyReportSummaryService;

/**
 * VIP多机构月度报告总结定时任务
 * 查询数据库中的VIP任务数据，调用AI接口生成多机构月度报告总结
 */
@JobHandler("VIPMultiMonthlyReportSummaryJob")
@Component
@Slf4j
public class VIPMultiMonthlyReportSummaryJob extends IJobHandler {

    @Autowired
    private VIPMultiMonthlyReportSummaryService vipMultiMonthlyReportSummaryService;

    @Override
    public ReturnT<String> execute(String param) {
        String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log("开始执行VIP多机构月度报告总结任务（独立执行模式），traceId：{}", traceId);
        XxlJobLogger.log("注意：建议使用VIPMonthlyReportSummaryJob统一执行单机构+多机构任务");

        try {
            boolean success = vipMultiMonthlyReportSummaryService.generateMultiMonthlyReportSummary(param);
            if (success) {
                XxlJobLogger.log("VIP多机构月度报告总结任务执行成功");
                return ReturnT.SUCCESS;
            } else {
                XxlJobLogger.log("VIP多机构月度报告总结任务执行失败");
                return ReturnT.FAIL;
            }
        } catch (Exception e) {
            log.error("VIP多机构月度报告总结任务执行异常", e);
            XxlJobLogger.log("VIP多机构月度报告总结任务执行异常：{}", e.getMessage());
            return new ReturnT<>(ReturnT.FAIL_CODE, e.getMessage());
        }
    }
}
