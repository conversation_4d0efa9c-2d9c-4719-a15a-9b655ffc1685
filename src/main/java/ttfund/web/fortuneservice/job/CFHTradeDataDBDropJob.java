package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.impl.CFHTradeDataDBDropService;

import javax.annotation.Resource;

/**
 * <AUTHOR>       ,gzy
 * @date 2023/2/23 13:29,2024年5月28日迁移
 * 删除历史财富号交易数据表格 一天执行一次
 */
@Component
@JobHandler("CFHTradeDataDBDropJob")
public class CFHTradeDataDBDropJob extends IJobHandler {
    private static Logger logger = LoggerFactory.getLogger(CFHTradeDataDBDropJob.class);

    @Resource
    CFHTradeDataDBDropService cfhTradeDataDBDropService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        //停服，转移到dba脚本
        return SUCCESS;
        /*String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log("开始执行【删除历史财富号交易数据表格】任务，traceId：{}", traceId);
        try {
            return cfhTradeDataDBDropService.execute();
        }catch (Exception e){
            logger.error("财富号交易数据mongodb表删除出现异常：{},{}", e.getMessage(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, e.getMessage());
        }*/
    }
}
