package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.UpdateCfhFundThemeService;

import javax.annotation.Resource;

@JobHandler(value="UpdateFundTheme")
@Component
public class UpdateFundTheme extends IJobHandler {

    @Resource
    private UpdateCfhFundThemeService service;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        service.handCFHFundTheme();
        String traceId = MDC.get(CoreConstant.logtraceid);
        return new ReturnT<>(traceId);
    }
}
