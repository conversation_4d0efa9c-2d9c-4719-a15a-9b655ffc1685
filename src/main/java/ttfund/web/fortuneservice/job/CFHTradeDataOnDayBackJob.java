package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.impl.CFHTradeDataOnDayRelocateService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024年6月7日
 * 财富号交易日数据迁移job 兜底-补录，不自动执行
 */
@Component
@JobHandler(value = "CFHTradeDataOnDayBackJob")
public class CFHTradeDataOnDayBackJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(CFHTradeDataOnDayBackJob.class);

    @Resource
    CFHTradeDataOnDayRelocateService cfhTradeDataOnDayRelocateService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log("start CFHTradeDataOnDayBackJob，traceId：{}", traceId);
        try {
            return cfhTradeDataOnDayRelocateService.syncBack(param);
        }catch (Exception e){
            logger.error("财富号全天交易数据补录同步代码异常：{}，{}",e.getMessage(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, e.getMessage());
        }
    }
}
