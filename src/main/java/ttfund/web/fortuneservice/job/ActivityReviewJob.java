package ttfund.web.fortuneservice.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.HotLabelService;

import javax.annotation.Resource;

@JobHandler(value="ActivityReviewJob")
@Component
public class ActivityReviewJob extends IJobHandler {
    @Resource
    private HotLabelService hotlabelService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        hotlabelService.activityLabelHandler();
        return ReturnT.SUCCESS;
    }
}
