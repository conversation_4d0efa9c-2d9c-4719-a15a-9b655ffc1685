package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.CommunityDataService;

/**
 * 社群数据job 每天同步一次
 */
@JobHandler(value="CommunityDataJob")
@Component
public class CommunityDataJob extends IJobHandler {

    @Autowired
    CommunityDataService service;
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log("start community data job，traceId：{}", traceId);
        service.handlerData(param);
        return new ReturnT<>(traceId);
    }
}
