package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.impl.CFHTradeDataOnDayRelocateService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/2/7 11:26,2024年5月28日迁移
 * 财富号交易日数据迁移job 每个交易日的16:00-23：00之间1h执行一次，执行成功之后，后续的服务不会再进行数据迁移
 */
@Component
@JobHandler(value = "CFHTradeDataOnDayRelocateJob")
public class CFHTradeDataOnDayRelocateJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(CFHTradeDataOnDayRelocateJob.class);

    @Resource
    CFHTradeDataOnDayRelocateService cfhTradeDataOnDayRelocateService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        //停服，改为Kafka消费
        return SUCCESS;
        /*String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log("start CFHTradeDataOnDayRelocateJob，traceId：{}", traceId);
        try {
            return cfhTradeDataOnDayRelocateService.sync();
        }catch (Exception e){
            logger.error("财富号全天交易数据同步代码异常：{}，{}",e.getMessage(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, e.getMessage());
        }*/
    }
}
