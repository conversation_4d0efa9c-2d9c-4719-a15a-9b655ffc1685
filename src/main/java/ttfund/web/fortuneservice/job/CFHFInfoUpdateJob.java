package ttfund.web.fortuneservice.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.UpdateCFHFInfoService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 财富号信息更新  - 调第三方接口获取cfh：关注数、浏览数等信息更新到Tb_CFHList中
 * @date 2023/8/3 10:41
 */

@JobHandler(value = "CFHFInfoUpdateJob")
@Component
public class CFHFInfoUpdateJob extends I<PERSON>ob<PERSON>andler {
    @Resource
    private UpdateCFHFInfoService service;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        if (service.updateCFHFInfo(param)) {
            return ReturnT.SUCCESS;
        }
        return ReturnT.FAIL;
    }
}
