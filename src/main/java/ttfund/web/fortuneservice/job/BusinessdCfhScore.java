package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.CFHScoreService;
import ttfund.web.fortuneservice.service.impl.TradeStatServiceImpl;


/**
 * 每天3点03,避免整点
 * 财富号评分
 */
@JobHandler("BusinessdCfhScore")
@Component
@Slf4j
public class BusinessdCfhScore extends IJobHandler {


    @Autowired
    private CFHScoreService service;
    @Override
    public ReturnT<String> execute(String executeImmediately) {
        String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log(traceId);
        service.syncCFHScore(executeImmediately);
        return ReturnT.SUCCESS;
    }

}
