package ttfund.web.fortuneservice.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.UpdateCfhArticleService;

import javax.annotation.Resource;

/**
 * 更新是否财富号文章mongo 10min/次
 * <AUTHOR>
 * @version 1.0.0
 * @className SetIsCFHArticleToMongoJob
 * @date 2023/4/14 15:52
 */
@JobHandler(value="SetIsCFHArticleToMongoJob")
@Component
public class SetIsCFHArticleToMongoJob extends IJobHandler {

    @Resource
    private UpdateCfhArticleService service;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        service.handCFHArticle();
        return ReturnT.SUCCESS;
    }
}
