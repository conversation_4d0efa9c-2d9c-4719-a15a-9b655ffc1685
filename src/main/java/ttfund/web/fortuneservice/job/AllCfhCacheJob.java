package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.service.CFHScoreService;


/**
 * 所有财富号缓存任务
 */
@JobHandler("AllCfhCacheJob")
@Component
@Slf4j
public class AllCfhCacheJob extends IJobHandler {


    @Autowired
    private CfhMongodbDao cfhMongodbDao;
    @Override
    public ReturnT<String> execute(String executeImmediately) {
        String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log(traceId);
        cfhMongodbDao.getAllCfhAndSetCache();
        return ReturnT.SUCCESS;
    }

}
