package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.TaskAutoGenerateService;

import javax.annotation.Resource;

/**
 * VIPTaskAutoGenerateJob.java
 * 运营任务生成
 *
 * <AUTHOR>
 * @date 2025年7月9日
 */
@JobHandler(value = "VIPTaskAutoGenerateJob")
@Component
public class VIPTaskAutoGenerateJob extends IJobHandler {

    @Resource
    private TaskAutoGenerateService service;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        String traceId = MDC.get(CoreConstant.logtraceid);
        if (service.autoGenerateVIPService()) {
            return new ReturnT<>(traceId);
        }
        return new ReturnT<String>(ReturnT.FAIL_CODE, traceId);
    }
}
