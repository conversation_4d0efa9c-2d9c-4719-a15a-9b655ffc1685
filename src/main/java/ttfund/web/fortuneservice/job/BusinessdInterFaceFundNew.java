package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.impl.TradeStatServiceImpl;


/**
 * 每天16点执行一次
 * 统计交易数据
 */
@JobHandler("BusinessdInterFaceFundNew")
@Component
@Slf4j
public class BusinessdInterFaceFundNew extends IJobHandler {


    @Autowired
    private TradeStatServiceImpl service;
    @Override
    public ReturnT<String> execute(String dateStr) {
        String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log(traceId);
        service.businessdInterFaceFundNew(dateStr);
        return ReturnT.SUCCESS;
    }

}
