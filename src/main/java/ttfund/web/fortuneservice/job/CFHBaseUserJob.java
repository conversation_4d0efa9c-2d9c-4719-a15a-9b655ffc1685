package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.CFHBaseInfoService;
import ttfund.web.fortuneservice.service.HotLabelService;

import javax.annotation.Resource;

/**
 * 财富号管理员同步
 */
@JobHandler(value="CFHBaseUserJob")
@Component
public class CFHBaseUserJob extends IJobHandler {
    @Resource
    private CFHBaseInfoService service;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log(traceId);
        service.syncCFHAdminUser(param);
        return ReturnT.SUCCESS;
    }
}
