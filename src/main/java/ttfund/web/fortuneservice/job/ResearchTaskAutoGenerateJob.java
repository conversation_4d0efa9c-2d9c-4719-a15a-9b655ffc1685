package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.TaskAutoGenerateService;

import javax.annotation.Resource;

/**
 * ResearchTaskAutoGenerateJob.java
 * 财富号机构周期任务自动生成
 *
 * <AUTHOR>
 * @date 2023/5/9 10:37
 */
@JobHandler(value = "ResearchTaskAutoGenerateJob")
@Component
public class ResearchTaskAutoGenerateJob extends IJobHandler {

    @Resource
    private TaskAutoGenerateService service;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        String traceId = MDC.get(CoreConstant.logtraceid);
        if (service.autoGenerateService()) {
            return new ReturnT<>(traceId);
        }
        return new ReturnT<String>(ReturnT.FAIL_CODE, traceId);
    }
}
