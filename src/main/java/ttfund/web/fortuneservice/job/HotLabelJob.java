package ttfund.web.fortuneservice.job;


import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.HotLabelService;

import javax.annotation.Resource;

/**
 * 计算标签排序
 * <AUTHOR>
 * @date 2022年2月25日
 */
@JobHandler(value="HotLabelJob")
@Component
public class HotLabelJob extends IJobHandler {

    @Resource
    private HotLabelService hotlabelService;


    @Override
    public ReturnT<String> execute(String param) throws Exception {
        hotlabelService.hotLabelHandler();
        return ReturnT.SUCCESS;
    }
}
