package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.CFHBaseInfoService;
import ttfund.web.fortuneservice.service.UserPermissionNoticeService;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 每个季度执行一次
 * 管理员权限管理弹窗床提醒
 */
@JobHandler(value="UserPermissionNoticeJob")
@Component
public class UserPermissionNoticeJob extends IJobHandler {
    @Resource
    private UserPermissionNoticeService service;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log(traceId);
        service.creat(param);
        return ReturnT.SUCCESS;
    }
}
