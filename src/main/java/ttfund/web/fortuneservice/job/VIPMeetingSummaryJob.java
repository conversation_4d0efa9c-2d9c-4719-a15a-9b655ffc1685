package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.VIPMeetingService;


/**
 * 处理VIP会议纪要
 */
@JobHandler("VIPMeetingSummaryJob")
@Component
@Slf4j
public class VIPMeetingSummaryJob extends IJobHandler {


    @Autowired
    private VIPMeetingService service;
    @Override
    public ReturnT<String> execute(String id) {
        String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log(traceId);
        service.handleMeetingSummary(id);
        return ReturnT.SUCCESS;
    }

}
