package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.LiveScoreFieldsSyncService;

import javax.annotation.Resource;

/**
 * Function: 数据同步变更，同步到MongoDB的tb_livescore 新增直播带货表字段
 * pcmp: https://pcmp.eastmoney.com/pcmp/project/demand/detail/index?requirementId=467917
 *
 * <AUTHOR> mourong
 * @date 2024年9月4日自bigdata迁移，2022/9/21-16:44
 */
@Component
@JobHandler(value = "LiveScoreFieldsSyncJob")
public class LiveScoreFieldsSyncJob extends IJobHandler {

    @Resource
    LiveScoreFieldsSyncService liveScoreFieldsSyncService;

    private static final Logger logger = LoggerFactory.getLogger(LiveScoreFieldsSyncJob.class);

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log("execute start sync  LiveScoreFieldsSyncJob，traceId：{}", traceId);
        logger.info("execute start sync  LiveScoreFieldsSyncJob");
        liveScoreFieldsSyncService.sync();
        return ReturnT.SUCCESS;
    }
}
