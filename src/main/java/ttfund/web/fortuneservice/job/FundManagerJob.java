package ttfund.web.fortuneservice.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.FundManagerService;

import javax.annotation.Resource;


/**
 * 人气大咖数据计算
 * <AUTHOR>
 * @date 2022年2月25日
 */
@JobHandler(value="FundManagerJob")
@Component
public class FundManagerJob extends IJobHandler {

    @Resource
    private FundManagerService fundManagerService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        fundManagerService.fundManagerHandler();
        return ReturnT.SUCCESS;
    }
}
