package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.CheckProductReportService;
import ttfund.web.fortuneservice.service.impl.CheckProductReportServiceImpl;

import javax.annotation.Resource;

/**
 * 手动检查 10min/ci
 */
@JobHandler(value = "CheckProductReportPushJob")
@Component
public class CheckProductReportPushJob extends IJobHandler {

    @Resource
    private CheckProductReportService checkProductReportServiceImpl;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log(traceId);
        checkProductReportServiceImpl.checkReportPush();
        return ReturnT.SUCCESS;
    }
}
