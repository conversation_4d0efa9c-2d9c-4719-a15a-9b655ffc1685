package ttfund.web.fortuneservice.job;

import com.ttfund.web.base.helper.DateHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.ProductReadService;

import javax.annotation.Resource;

/**
 * 品种页-公告解读落库任务，涉及表FundAnnouncement.FundNotice，FundAnnouncement.FundPrivateNotice
 * <AUTHOR>
 * @date 2022/10/11 16:10
 * @email <EMAIL>
 * @version 1.0
 */
@JobHandler(value="ProductReadJob")
@Component
public class ProductReadJob extends IJobHandler {

    @Resource
    private ProductReadService productReadService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        productReadService.writeProductReadIntoHq(DateHelper.getNowDate());
        return ReturnT.SUCCESS;
    }
}
