package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.VIPAIReportSyncService;
import ttfund.web.fortuneservice.utils.TimeUtil;
import ttfund.web.fortuneservice.constant.VIPAIReportConstant;

import java.util.Date;

/**
 * VIP AI报告数据同步定时任务
 * 将SQL Server中的VIP AI报告数据同步到MongoDB
 */
@JobHandler("VIPAIReportSyncJob")
@Component
@Slf4j
public class VIPAIReportSyncJob extends IJobHandler {

    @Autowired
    private VIPAIReportSyncService vipAIReportSyncService;

    @Override
    public ReturnT<String> execute(String param) {
        String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log("开始执行VIP AI报告数据同步任务，traceId：{}", traceId);
        
        try {
            // 获取断点时间
            Date breakpointTime = getBreakpointTime(param);
            XxlJobLogger.log("使用断点时间：{}", TimeUtil.dateToStr(breakpointTime, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS));
            
            // 执行同步
            boolean success = vipAIReportSyncService.syncVIPAIReportToMongoDB(breakpointTime);
            
            if (success) {
                XxlJobLogger.log("VIP AI报告数据同步任务执行成功");
                return ReturnT.SUCCESS;
            } else {
                XxlJobLogger.log("VIP AI报告数据同步任务执行失败");
                return ReturnT.FAIL;
            }
        } catch (Exception e) {
            log.error("VIP AI报告数据同步任务执行异常", e);
            XxlJobLogger.log("VIP AI报告数据同步任务执行异常：{}", e.getMessage());
            return new ReturnT<>(ReturnT.FAIL_CODE, e.getMessage());
        }
    }

    /**
     * 获取断点时间
     * 如果参数为空，则使用MongoDB中最新的更新时间
     * 如果MongoDB中没有数据，则使用很早的时间进行全量同步
     */
    private Date getBreakpointTime(String param) {
        try {
            // 如果参数不为空，尝试解析参数作为断点时间
            if (param != null && !param.trim().isEmpty()) {
                return TimeUtil.stringToDate2(param.trim(), TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS);
            }

            // 从MongoDB获取最新更新时间
            Date latestUpdateTime = vipAIReportSyncService.getLatestUpdateTime();
            if (latestUpdateTime != null) {
                return latestUpdateTime;
            }

            // 如果MongoDB中没有数据，使用很早的时间进行全量历史数据同步
            return TimeUtil.stringToDate2(VIPAIReportConstant.Time.FULL_SYNC_START_TIME, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS);

        } catch (Exception e) {
            log.error("获取断点时间失败，使用默认时间", e);
            // 发生异常时使用很早的时间进行全量同步
            try {
                return TimeUtil.stringToDate2(VIPAIReportConstant.Time.FULL_SYNC_START_TIME, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS);
            } catch (Exception ex) {
                // 如果时间解析也失败，使用时间戳0
                return new Date(0);
            }
        }
    }
}
