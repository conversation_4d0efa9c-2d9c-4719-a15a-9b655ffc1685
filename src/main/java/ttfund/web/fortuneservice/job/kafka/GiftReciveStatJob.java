package ttfund.web.fortuneservice.job.kafka;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.model.bo.CFHBaseInfoBo;
import ttfund.web.fortuneservice.service.CFHBaseInfoService;
import ttfund.web.fortuneservice.service.GiftReciveLogService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023年9月20日
 */
@Slf4j
@Component
public class GiftReciveStatJob {

    @Resource
    private GiftReciveLogService service;

    @KafkaListener(topics = {CommonConstant.LOG_TOPIC},
            groupId = CommonConstant.GROUP_ID_CFH, containerFactory = CommonConstant.FACTORY_WEB)
    public void listen(List<ConsumerRecord<?, ?>> records) {
        try {
            for (ConsumerRecord<?, ?> myRecord : records) {
                //获取到消息转换为对象
                String value = (String) myRecord.value();
                String key = (String) myRecord.key();
                log.info("接收到消息【{}】", value);
                if (StringUtils.isEmpty(value)) {
                    continue;
                }
                int sendnum = service.handLog(key, value);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
