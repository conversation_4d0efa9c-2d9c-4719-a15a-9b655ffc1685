package ttfund.web.fortuneservice.job.kafka;

import com.ttfund.web.core.constant.CoreConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.listener.ConsumerSeekAware;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.service.VIPMeetingService;

import java.util.List;
import java.util.UUID;

@Slf4j
@Component
public class VIPMeetingLibJob implements ConsumerSeekAware {

    @Autowired
    private VIPMeetingService service;

    @KafkaListener(topics = {CommonConstant.TOPIC_WEB_MEETING},
            groupId = CommonConstant.GROUP_ID_CFH, containerFactory = CommonConstant.FACTORY_WEB)
    public void listen(List<ConsumerRecord<?, ?>> records) {
        for (ConsumerRecord<?, ?> myRecord : records) {
            String traceId = UUID.randomUUID().toString().replace("-", "");
            MDC.put(CoreConstant.logtraceid, traceId);
            try {
                String message = (String) myRecord.value();
                log.info("vip会议集锦收到消息【{}】", message);
                service.handleMeetingLib(message);
            } catch (Exception e) {
                log.error("处理vip会议集锦消息异常", e);
            } finally {
                MDC.remove(CoreConstant.logtraceid);
            }
        }
    }
}
