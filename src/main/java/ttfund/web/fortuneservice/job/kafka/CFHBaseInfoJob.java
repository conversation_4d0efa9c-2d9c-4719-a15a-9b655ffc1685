package ttfund.web.fortuneservice.job.kafka;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.model.bo.CFHBaseInfoBo;
import ttfund.web.fortuneservice.service.CFHBaseInfoService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className CFHBaseInfoJob
 * @date 2023/4/14 15:13
 */
@Slf4j
@Component
public class CFHBaseInfoJob {

    @Resource
    private CFHBaseInfoService service;

    @KafkaListener(topics = {CommonConstant.TOPIC_EAST_MONEY_CFH_ACCOUNT_UPDATE},
            groupId = CommonConstant.GROUP_ID_CFH, containerFactory = CommonConstant.FACTORY_EAST_MONEY)
    public void listen(List<ConsumerRecord<?, ?>> records) {
        try {
            for (ConsumerRecord<?, ?> myRecord : records) {
                //获取到消息转换为对象
                CFHBaseInfoBo cfhBaseInfo = service.getCFHBaseInfo((String) myRecord.value());
                if (cfhBaseInfo == null) {
                    continue;
                }
                int sendnum = service.handCFHBaseInfo(cfhBaseInfo);
                log.info("kafka receive offset:{},sendnum:{}", myRecord.offset(), sendnum);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
