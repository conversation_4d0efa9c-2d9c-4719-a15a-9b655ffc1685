package ttfund.web.fortuneservice.job.kafka;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.listener.ConsumerSeekAware;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.service.impl.ShortVideoStatService;

import javax.annotation.Resource;
import java.util.List;

/**
 * 短视频统计
 */
@Slf4j
@Component
public class ShortVideoStatJob implements ConsumerSeekAware {

    @Resource
    private ShortVideoStatService service;

    @KafkaListener(topics = {CommonConstant.TOPIC_EAST_MONEY_EMAV_AV_COMMON_STATISTIC},
            groupId = CommonConstant.GROUP_ID_CFH, containerFactory = CommonConstant.FACTORY_LANGKE)
    public void listen(List<ConsumerRecord<?, ?>> records) {
        try {
            for (ConsumerRecord<?, ?> myRecord : records) {
                //获取到消息转换为对象
                String message = (String) myRecord.value();
                log.info("短视频统计收到消息【{}】", message);
                service.handler(message);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    //修改offset值，上线去掉
//    @Override
//    public void onPartitionsAssigned(Map<TopicPartition, Long> assignments, ConsumerSeekCallback callback) {
//        assignments.forEach((topicPartition, offset) -> {
//            long desiredOffset = 0L; // 您想要设置的offset值，这里我们将其设置为0以重新开始消费
//            callback.seek(topicPartition.topic(), topicPartition.partition(), desiredOffset);
//        });
//    }
}
