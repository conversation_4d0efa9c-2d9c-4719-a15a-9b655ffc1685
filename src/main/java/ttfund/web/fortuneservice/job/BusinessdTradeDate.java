package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.CFHScoreService;
import ttfund.web.fortuneservice.service.TradeDateService;


/**
 * 每天18点同步一次
 * 财富号评分
 */
@JobHandler("BusinessdTradeDate")
@Component
@Slf4j
public class BusinessdTradeDate extends IJobHandler {


    @Autowired
    private TradeDateService service;
    @Override
    public ReturnT<String> execute(String executeImmediately) {
        String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log(traceId);
        service.syncTradeDate(executeImmediately);
        return ReturnT.SUCCESS;
    }

}
