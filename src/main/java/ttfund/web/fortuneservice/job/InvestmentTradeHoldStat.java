package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.InvestmentTradeStatService;

/**
 * 每天6点执行一次
 * 统计投顾数据-持有
 */
@JobHandler("InvestmentTradeHoldStat")
@Component
@Slf4j
public class InvestmentTradeHoldStat extends IJobHandler {


    @Autowired
    private InvestmentTradeStatService service;

    @Override
    public ReturnT<String> execute(String dateStr) throws Exception {
        String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log(traceId);
        service.businessdInvestmentTradeHoldStat(dateStr);
        return ReturnT.SUCCESS;
    }
}
