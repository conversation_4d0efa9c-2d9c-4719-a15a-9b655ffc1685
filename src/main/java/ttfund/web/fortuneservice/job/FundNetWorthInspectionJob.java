package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.FundNetWorthInspectionService;

import javax.annotation.Resource;

/**
 * FundNetWorthInspectionJob.java
 * 产品未按期披露净值定期检测
 *
 * <AUTHOR>
 * @date 2023/5/11 10:35
 */
@JobHandler(value = "FundNetWorthInspectionJob")
@Component
public class FundNetWorthInspectionJob extends IJobHandler {
    @Resource
    private FundNetWorthInspectionService inspectionService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {

        String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log(traceId);
        if (inspectionService.inspectionService(param)) {
            return ReturnT.SUCCESS;
        }
        return ReturnT.FAIL;
    }
}

