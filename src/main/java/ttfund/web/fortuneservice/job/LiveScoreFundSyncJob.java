package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.LiveScoreFieldsSyncService;

import javax.annotation.Resource;

/**
 * 直播运营数据（基金）逻辑上6点前后跑一次同步就可以
 * <AUTHOR>
 * @date 2024年9月4日
 */
@Component
@JobHandler(value = "LiveScoreFundSyncJob")
public class LiveScoreFundSyncJob extends IJobHandler {

    @Resource
    LiveScoreFieldsSyncService liveScoreFieldsSyncService;

    private static final Logger logger = LoggerFactory.getLogger(LiveScoreFundSyncJob.class);

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log("execute start sync  LiveScoreFundSyncJob，traceId：{}", traceId);
        logger.info("execute start sync  LiveScoreFundSyncJob");
        liveScoreFieldsSyncService.syncFund(param);
        return ReturnT.SUCCESS;
    }
}
