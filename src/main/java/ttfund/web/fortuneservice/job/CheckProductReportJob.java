package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.impl.CheckProductReportServiceImpl;

import javax.annotation.Resource;

@JobHandler(value = "CheckProductReportJob")
@Component
public class CheckProductReportJob extends IJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(CheckProductReportJob.class);

    @Resource
    private CheckProductReportServiceImpl checkProductReportServiceImpl;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        try {
            String traceId = MDC.get(CoreConstant.logtraceid);
            XxlJobLogger.log(traceId);
            checkProductReportServiceImpl.checkReport(param);
        }catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
