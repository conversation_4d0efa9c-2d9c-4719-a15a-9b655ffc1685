package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.impl.CFHTradeDataOnHourRelocateService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/2/3 8:55,2024年5月30日迁移
 * 财富号分时交易数据同步job 10分钟更新1次
 */
@Component
@JobHandler(value = "CFHTradeDataOnHourRelocateJob")
public class CFHTradeDataOnHourRelocateJob extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(CFHTradeDataOnHourRelocateJob.class);

    @Resource
    CFHTradeDataOnHourRelocateService cfhTradeDataOnHourRelocateService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        //停服，改为Kafka消费
        return SUCCESS;
        /*String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log("start CFHTradeDataOnHourRelocateJob，traceId：{}", traceId);
        try {
            return cfhTradeDataOnHourRelocateService.sync();
        } catch (Exception e) {
            logger.error("财富号分时交易数据迁移代码异常：{}，{}",e.getMessage(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, e.getMessage());
        }*/
    }
}
