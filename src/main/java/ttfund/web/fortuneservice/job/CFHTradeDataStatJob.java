package ttfund.web.fortuneservice.job;

import com.alibaba.fastjson.JSON;
import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.slf4j.MDC;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.model.dto.TradeDetailDTO;
import ttfund.web.fortuneservice.service.impl.CFHTradeDataKafkaRelocateService;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024年12月17日
 * 交易数据消费Kafka
 */
@Slf4j
@Component
@JobHandler(value = "CFHTradeDataStatJob")
public class CFHTradeDataStatJob extends IJobHandler {

    @Resource
    private CFHTradeDataKafkaRelocateService service;

    @Resource(name = CommonConstant.WEB_TRAD_CONSUMER)
    private KafkaConsumer<String, String> kafkaConsumer;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            String traceId = MDC.get(CoreConstant.logtraceid);
            XxlJobLogger.log(traceId);
            // 订阅Kafka主题
            kafkaConsumer.subscribe(Collections.singletonList(CommonConstant.FUND_CFH_TRANSACTION_MESSAGE));

            // 每次从Kafka消费消息
            ConsumerRecords<String, String> records = kafkaConsumer.poll(Duration.ofMillis(60000));

            if (records.isEmpty()) {
                log.info("No messages found in Kafka for this interval.");
                return ReturnT.SUCCESS;
            }

            List<TradeDetailDTO> messages = new ArrayList<>();
            // 处理每条消息
            for (ConsumerRecord<String, String> record : records) {
                String value = record.value();
                log.info("交易数据收到消息【{}】", value);
                if (StringUtils.isEmpty(value)) {
                    continue;
                }
                TradeDetailDTO dto = null;
                try {
                    dto = JSON.parseObject(value, TradeDetailDTO.class);
                } catch (Exception e) {
                    log.error("Error parsing message: {}", value, e);
                }
                messages.add(dto);
            }
            // 调用服务处理消息
            service.sync(messages);
            kafkaConsumer.commitSync(); // 手动同步提交
            log.info("Successfully processed {} messages from Kafka", records.count());
            XxlJobLogger.log("Successfully processed " + records.count() + " messages from Kafka");
        } catch (Exception e) {
            log.error("Error processing messages from Kafka", e);
        }
        return ReturnT.SUCCESS;
    }

    /*@Scheduled(fixedRate = 600000)
    @PostConstruct
    public void consumeMessages() {
        try {
            execute(null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }*/
}
