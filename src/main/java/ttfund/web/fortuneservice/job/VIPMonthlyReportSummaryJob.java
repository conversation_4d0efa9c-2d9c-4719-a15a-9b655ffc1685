package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.VIPMonthlyReportSummaryService;
import ttfund.web.fortuneservice.service.VIPMultiMonthlyReportSummaryService;

/**
 * VIP月度报告总结定时任务
 * 查询数据库中的VIP任务数据，调用AI接口生成月度报告总结
 */
@JobHandler("VIPMonthlyReportSummaryJob")
@Component
@Slf4j
public class VIPMonthlyReportSummaryJob extends IJobHandler {

    @Autowired
    private VIPMonthlyReportSummaryService vipMonthlyReportSummaryService;

    @Autowired
    private VIPMultiMonthlyReportSummaryService vipMultiMonthlyReportSummaryService;

    @Override
    public ReturnT<String> execute(String param) {
        String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log("开始执行VIP月度报告总结任务，traceId：{}", traceId);
        
        try {
            // 1. 执行单机构月度报告总结任务
            XxlJobLogger.log("开始执行单机构月度报告总结任务");
            boolean singleSuccess = vipMonthlyReportSummaryService.generateMonthlyReportSummary(param);

            if (singleSuccess) {
                XxlJobLogger.log("单机构月度报告总结任务执行成功");

                // 2. 执行多机构月度报告总结任务
                XxlJobLogger.log("开始执行多机构月度报告总结任务");
                boolean multiSuccess = vipMultiMonthlyReportSummaryService.generateMultiMonthlyReportSummary(param);

                if (multiSuccess) {
                    XxlJobLogger.log("多机构月度报告总结任务执行成功");
                    XxlJobLogger.log("VIP月度报告总结任务（单机构+多机构）全部执行成功");
                    return ReturnT.SUCCESS;
                } else {
                    XxlJobLogger.log("多机构月度报告总结任务执行失败，但单机构任务已成功");
                    return new ReturnT<>(ReturnT.SUCCESS_CODE, "单机构成功，多机构失败");
                }
            } else {
                XxlJobLogger.log("单机构月度报告总结任务执行失败，跳过多机构任务");
                return ReturnT.FAIL;
            }
        } catch (Exception e) {
            log.error("VIP月度报告总结任务执行异常", e);
            XxlJobLogger.log("VIP月度报告总结任务执行异常：{}", e.getMessage());
            return new ReturnT<>(ReturnT.FAIL_CODE, e.getMessage());
        }
    }
}
