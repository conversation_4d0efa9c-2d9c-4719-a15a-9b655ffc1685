package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.CheckProductReportService;

import javax.annotation.Resource;


/**
 * 报告数据上报
 * <AUTHOR>
 * @date 2025年5月13日
 */
@JobHandler("CheckProductReportDataSyncJob")
@Component
@Slf4j
public class CheckProductReportDataSyncJob extends IJobHandler {


    @Resource
    private CheckProductReportService checkProductReportService;
    @Override
    public ReturnT<String> execute(String executeImmediately) {
        String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log(traceId);
        checkProductReportService.productReportDataSync();
        return ReturnT.SUCCESS;
    }

}
