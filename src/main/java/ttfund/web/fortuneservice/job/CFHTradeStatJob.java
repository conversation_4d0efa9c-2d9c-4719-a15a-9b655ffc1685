package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.impl.CFHTradeStatServiceImpl;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025年2月26日
 * 迁移宋佳兵服务，16点运行一次，S库->mongo
 */
@JobHandler(value="CFHTradeStatJob")
@Component
public class CFHTradeStatJob extends IJobHandler {
    @Resource
    private CFHTradeStatServiceImpl service;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log(traceId);
        service.CFHTradeStat();
        return ReturnT.SUCCESS;
    }
}
