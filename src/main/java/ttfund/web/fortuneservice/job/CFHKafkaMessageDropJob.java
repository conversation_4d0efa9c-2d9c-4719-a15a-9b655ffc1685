package ttfund.web.fortuneservice.job;

import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.service.impl.MessageService;

/**
 * <AUTHOR>
 * @date 2024年12月17日
 * 删除Kafka消息，每天一次
 */
@Component
@JobHandler("CFHKafkaMessageDropJob")
public class CFHKafkaMessageDropJob extends IJobHandler {

    @Autowired
    private MessageService messageService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        String traceId = MDC.get(CoreConstant.logtraceid);
        XxlJobLogger.log("开始执行【删除历史财富号Kafka消息】任务，traceId：{}", traceId);
        messageService.deleteMessage();
        return ReturnT.SUCCESS;
    }
}
