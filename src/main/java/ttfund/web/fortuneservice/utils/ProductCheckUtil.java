package ttfund.web.fortuneservice.utils;

import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.model.dto.DaysInfoMongoDto;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/6/13 9:38
 */
public class ProductCheckUtil {
    private ProductCheckUtil() {
        throw new IllegalStateException(CommonConstant.UTILITY_CLASS);
    }

    /**
     * 判断指定日期是否为交易日
     * @param date 日期
     * @param dayInfoList 日期列表
     * @return boolean
     */
    public static boolean checkIsTradeDay(Date date, List<DaysInfoMongoDto> dayInfoList) {
        if (date == null || CollectionUtils.isEmpty(dayInfoList)) {
            return false;
        }
        boolean result = false;
        Date tempDate = CommonUtil.getCurrentBelongDay(date);
        for (DaysInfoMongoDto dayInfo : dayInfoList) {
            if (dayInfo != null && dayInfo.getDATETIME() != null && tempDate.getTime() == dayInfo.getDATETIME().getTime()) {
                result = "1".equals(dayInfo.getISTDATE());
                break;
            }
        }
        return result;
    }

    /**
     * 判断当前日期在指定范围中处于第几个交易日
     *
     * @param date        日期
     * @param dayInfoList 交易日范围
     * @param reversed    是否倒序 true 计算倒数第几个交易日 false 计算正数第几个交易日
     * @return int
     */
    public static int calculateTradeDayNumber(Date date, List<DaysInfoMongoDto> dayInfoList, boolean reversed) {
        if (date == null || CollectionUtils.isEmpty(dayInfoList)) {
            return 0;
        }
        return (int) dayInfoList.stream().filter(Objects::nonNull)
                .filter(dayInfo -> dayInfo.getDATETIME() != null && "1".equals(dayInfo.getISTDATE()))
                .filter(dayInfo -> reversed ? date.getTime() <= dayInfo.getDATETIME().getTime()
                        : date.getTime() >= dayInfo.getDATETIME().getTime())
                .count();
    }
}
