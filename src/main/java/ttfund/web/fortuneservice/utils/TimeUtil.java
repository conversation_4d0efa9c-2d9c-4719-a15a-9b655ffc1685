package ttfund.web.fortuneservice.utils;

import com.ttfund.web.base.helper.DateHelper;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;

public class TimeUtil extends DateHelper {

    public final static String FORMAT_HHMMSS = "HH:mm:ss";
    public final static String FORMAT_MM_Y_DD_D = "MM月dd日";
    public final static String FORMAT_YYYYMMDD2 = "yyyy/MM/dd";
    public final static String FORMAT_YYYYMD_H_MM_SS = "yyyy/M/d H:mm:ss";
    public final static String FORMAT_YYYYMMDD_23_59_59 = "yyyy-MM-dd 23:59:59";
    public final static String FORMAT_MMDD = "MM/dd";


    public static Date getTodayStartAsDate() {
        LocalDate today = LocalDate.now(); // 获取当前日期
        LocalTime midnight = LocalTime.MIDNIGHT; // 获取0点时间
        LocalDateTime startOfDay = LocalDateTime.of(today, midnight); // 结合日期和时间

        // 转换为Date类型
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static String convertToIntYearMonth(int year, int month, String format) {
        // 创建一个Calendar实例
        try {
            Calendar calendar = Calendar.getInstance();
            // 设置年份和月份
            calendar.set(year, month - 1, 1);
            // 创建一个SimpleDateFormat实例，指定日期/时间格式
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            // 格式化日期
            return sdf.format(calendar.getTime());
        } catch (Exception e) {
            return null;
        }
    }

    public static Date getTodayStartAsDate(Date inputDate) {
        if (inputDate == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(inputDate);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }

    public static Date getTodayEndAsDate(Date inputDate) {
        if (inputDate == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(inputDate);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }
    public static Date getTodayTimeAsDate(Date inputDate,int hour,int minute,int second,int millisecond) {
        if (inputDate == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(inputDate);
        calendar.set(Calendar.HOUR_OF_DAY, Math.min(hour, 23));
        calendar.set(Calendar.MINUTE, Math.min(minute, 59));
        calendar.set(Calendar.SECOND, Math.min(second, 59));
        calendar.set(Calendar.MILLISECOND, Math.min(millisecond, 999));
        return calendar.getTime();
    }
}
