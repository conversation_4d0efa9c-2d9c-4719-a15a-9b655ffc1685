package ttfund.web.fortuneservice.utils;

import com.ttfund.web.base.helper.DruidHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.ParameterMetaData;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
import java.util.Properties;

public class Vertica<PERSON>elper extends DruidHelper {
    private static final Logger logger = LoggerFactory.getLogger(VerticaHelper.class);

    public VerticaHelper(String url, String driver) {
        super(url, driver);
    }
    public VerticaHelper(String url, String driver, Properties properties) {
        super(url, driver,properties);
    }

    public boolean executeBetchUpdate(String sql, List<List<Object>> parameters) {
        boolean result = false;
        Connection conn = null;
        PreparedStatement ps = null;

        try {
            conn = this.getconn();
            conn.setAutoCommit(false);
            ps = conn.prepareStatement(sql);

            for (int i = 0; i < parameters.size(); ++i) {
                List<Object> paramlist = parameters.get(i);

                for (int i2 = 0; i2 < paramlist.size(); ++i2) {
                    ParameterMetaData pmd = ps.getParameterMetaData();
                    Object column = paramlist.get(i2);
                    if (column != null && pmd.getParameterTypeName(i2 + 1).equalsIgnoreCase("VARCHAR")) {
                        int maxLength = pmd.getPrecision(i2 + 1);
                        String varcharColumn = String.valueOf(column);
                        if (varcharColumn.getBytes().length > maxLength) {
                            byte[] bytes = copyOfRange(varcharColumn.getBytes(), 0, maxLength);
                            column = new String(bytes, StandardCharsets.UTF_8);
                        }
                    }
                    ps.setObject(i2 + 1, column);
                }

                ps.addBatch();
                if (i > 0 && i % 200 == 0) {
                    ps.executeBatch();
                }
            }

            ps.executeBatch();
            conn.commit();
            ps.close();
            conn.setAutoCommit(true);
            result = true;
        } catch (Exception var13) {
            logger.error(sql, var13);
        } finally {
            this.closeConn(conn, ps, (ResultSet) null);
        }

        return result;
    }
    public static byte[] copyOfRange(byte[] orignial,int from,int to){
        int newLength=to-from;
        if(newLength<0)
            throw new IllegalArgumentException(from+">"+to);
        byte[] copy=new byte[newLength];
        System.arraycopy(orignial, from, copy, 0, Math.min(orignial.length-from, newLength));
        return copy;
    }
}
