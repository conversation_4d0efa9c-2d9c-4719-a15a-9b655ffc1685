package ttfund.web.fortuneservice.utils.http;

import com.alibaba.fastjson.JSON;
import org.springframework.http.HttpMethod;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class HttpParamers {

    private Map<String, Object> params = new HashMap<String, Object>();
    private HttpMethod httpMethod;
    private String url;
    private Integer timeOut;
    private String jsonParamer = "";

    public HttpParamers(HttpMethod httpMethod) {
        this.httpMethod = httpMethod;
    }

    public static HttpParamers httpPostParamers() {
        return new HttpParamers(HttpMethod.POST);
    }

    public static HttpParamers httpGetParamers() {
        return new HttpParamers(HttpMethod.GET);
    }

    public HttpParamers addParam(String name, Object value) {
        this.params.put(name, value);
        return this;
    }

    public HttpParamers addUrl(String url) {
        this.url = url;
        return this;
    }

    public HttpMethod getHttpMethod() {
        return this.httpMethod;
    }

    public String getQueryString(String charset) throws IOException {
        if ((this.params == null) || (this.params.isEmpty())) {
            return null;
        }
        StringBuilder query = new StringBuilder();
        Set<Map.Entry<String, Object>> entries = this.params.entrySet();

        for (Map.Entry<String, Object> entry : entries) {
            String name = entry.getKey();
            String value = String.valueOf(entry.getValue());
            query.append("&").append(name).append("=").append(URLEncoder.encode(value, charset));
//            query.append("&").append(name).append("=").append(value);
        }
        return query.substring(1);
    }

    public boolean isJson() {
        return !isEmpty(this.jsonParamer);
    }

    public Map<String, Object> getParams() {
        return this.params;
    }

    public String getUrl() {
        return url;
    }

    public String toString() {
        return "HttpParamers " + JSON.toJSONString(this);
    }

    public String getJsonParamer() {
        return this.jsonParamer;
    }

    public void setJsonParamer() {
        this.jsonParamer = JSON.toJSONString(this.params);
    }

    public void setJsonParamer(String params) {
        this.jsonParamer = params;
    }

    private static boolean isEmpty(CharSequence cs) {
        return (cs == null) || (cs.length() == 0);
    }

    public HttpParamers setTimeOut(Integer timeOut) {
        this.timeOut = timeOut;
        return this;
    }
    public Integer getTimeOut() {
        return timeOut;
    }
}
