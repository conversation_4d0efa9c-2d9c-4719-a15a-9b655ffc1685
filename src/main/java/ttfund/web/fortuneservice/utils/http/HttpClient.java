package ttfund.web.fortuneservice.utils.http;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.IPUtils;
import com.ttfund.web.core.constant.CoreConstant;
import javafx.scene.layout.ColumnConstraints;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.http.HttpMethod;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import ttfund.web.fortuneservice.utils.http.HttpParamers;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

/**
 * http工具类
 */
public class HttpClient {

    private final static Logger logger = LoggerFactory.getLogger(HttpClient.class);

    //编码
    public static final String DEFAULT_CHARSET = "UTF-8";
    //数据格式
    public static final String JSON_CONTENT_FORM = "application/json;charset=UTF-8";
    public static final String CONTENT_FORM = "application/x-www-form-urlencoded;charset=UTF-8";
    public static final int CONNECT_TIMEOUT = 3000;// 设置连接超时时间，单位毫秒。
    public static final int READ_TIMEOUT = 3000;// 请求获取数据的超时时间(即响应时间)，单位毫秒。

    private static HttpClient factory;

    @PostConstruct
    public void init() {
        factory = this;
    }

    /**
     * 整合方法
     *
     * @param paramers
     * @param header
     * @return
     * @throws Exception
     */
    public static String doService(HttpParamers paramers, HttpHeader header) {
        HttpMethod httpMethod = paramers.getHttpMethod();
        switch (httpMethod) {
            case GET:
                return doGet(paramers.getUrl(), paramers, header, READ_TIMEOUT);
            case POST:
                return doPost(paramers.getUrl(), paramers, header, READ_TIMEOUT);
            default:
        }
        return null;
    }
    public static String doService(HttpParamers paramers, HttpHeader header,int timeOut) {
        HttpMethod httpMethod = paramers.getHttpMethod();
        switch (httpMethod) {
            case GET:
                return doGet(paramers.getUrl(), paramers, header,timeOut);
            case POST:
                return doPost(paramers.getUrl(), paramers, header,timeOut);
            default:
        }
        return null;
    }

    /**
     * 代理整合方法
     *
     * @param paramers
     * @param header
     * @return
     * @throws Exception
     */
    public static String doProxyService(HttpParamers paramers, HttpHeader header) {
        HttpMethod httpMethod = paramers.getHttpMethod();
        switch (httpMethod) {
            case POST:
                return doProxyPost(paramers.getUrl(), paramers, header, "127.0.0.1", 80, READ_TIMEOUT);
            default:
        }
        return null;
    }
    public static String doProxyService(HttpParamers paramers, HttpHeader header,String ip,int port,int timeOut) {
        HttpMethod httpMethod = paramers.getHttpMethod();
        switch (httpMethod) {
            case POST:
                return doProxyPost(paramers.getUrl(), paramers, header, ip, port, timeOut);
            default:
        }
        return null;
    }

    /**
     * post方法
     *
     * @param url
     * @param paramers
     * @param header
     * @param timeOut
     * @return
     * @throws IOException
     */
    public static String doPost(String url, HttpParamers paramers, HttpHeader header, int timeOut) {
        long start = System.currentTimeMillis();
        long time = 0;
        String responseData = "";
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        timeOut = paramers.getTimeOut() != null && paramers.getTimeOut() > 0 ? paramers.getTimeOut() : timeOut;
        try {
            HttpClientBuilder builder = HttpClients.custom();
            builder.setSSLHostnameVerifier((hostName, sslSession) -> {
                return true; // 证书校验通过
            });
            String query = null;
            HttpPost httpPost = new HttpPost(url);
            setHeader(httpPost, header);
            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(CONNECT_TIMEOUT)
                    .setSocketTimeout(timeOut).build();
            httpPost.setConfig(requestConfig);
            if (paramers.isJson()) {
                //json数据
                httpPost.setHeader(HTTP.CONTENT_TYPE, JSON_CONTENT_FORM);
                query = StringUtils.isNotBlank(paramers.getJsonParamer()) ? paramers.getJsonParamer() : JSONObject.toJSONString(paramers.getParams());
            } else {
                //表单数据
                httpPost.setHeader(HTTP.CONTENT_TYPE, CONTENT_FORM);
                query = paramers.getQueryString(DEFAULT_CHARSET);
            }
            if (query != null) {
                HttpEntity reqEntity = new StringEntity(query,ContentType.APPLICATION_JSON);
                httpPost.setEntity(reqEntity);
            }
            httpClient = builder.build();
            httpResponse = httpClient.execute(httpPost);
            HttpEntity resEntity = httpResponse.getEntity();
            responseData = EntityUtils.toString(resEntity);
        } catch (Exception e) {
            logger.error("http client exception: {}",e.getMessage());
        } finally {
            long end = System.currentTimeMillis();
            time = end - start;
            dealClose(httpClient, httpResponse);
            dealLog(url, header, paramers, responseData, time);//日志记录
        }
        return responseData;
    }

    /**
     * post方法
     *
     * @param url
     * @param paramers
     * @param header
     * @return
     * @throws IOException
     */
    public static String doProxyPost(String url, HttpParamers paramers, HttpHeader header,String ip,int port,int timeOut) {
        long start = System.currentTimeMillis();
        long time = 0;
        String responseData = "";
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        try {
            String query = null;
            HttpPost httpPost = new HttpPost(url);
            setHeader(httpPost, header);
            HttpHost proxy = new HttpHost(ip, port, "http");
            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(CONNECT_TIMEOUT)
                    .setSocketTimeout(timeOut).setProxy(proxy).build();
            httpPost.setConfig(requestConfig);
            if (paramers.isJson()) {
                //json数据
                httpPost.setHeader(HTTP.CONTENT_TYPE, JSON_CONTENT_FORM);
                query = JSONObject.toJSONString(paramers.getParams());
            } else {
                //表单数据
                httpPost.setHeader(HTTP.CONTENT_TYPE, CONTENT_FORM);
                query = paramers.getQueryString(DEFAULT_CHARSET);
            }

            if (query != null) {
                HttpEntity reqEntity = new StringEntity(query, ContentType.APPLICATION_JSON);
                httpPost.setEntity(reqEntity);
            }
            httpClient = HttpClients.createDefault();
            httpResponse = httpClient.execute(httpPost);
            HttpEntity resEntity = httpResponse.getEntity();
            responseData = EntityUtils.toString(resEntity);
        } catch (Exception e) {
            logger.error("http client exception: {}",e.getMessage());
        } finally {
            long end = System.currentTimeMillis();
            time = end - start;
            dealClose(httpClient, httpResponse);
            dealLog(url, header, paramers, responseData,time);//日志记录
        }
        return responseData;
    }


    /**
     * get方法
     *
     * @param url
     * @param params
     * @param header
     * @param timeOut
     * @return
     * @throws IOException
     */
    public static String doGet(String url, HttpParamers params, HttpHeader header, int timeOut) {
        long start = System.currentTimeMillis();
        long time = 0;
        String responseData = "";
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        timeOut = params.getTimeOut() != null && params.getTimeOut() > 0 ? params.getTimeOut() : timeOut;
        try {
            String query = params.getQueryString(DEFAULT_CHARSET);
            url = buildGetUrl(url, query);
            HttpGet httpGet = new HttpGet(url);
            setHeader(httpGet, header);
            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(CONNECT_TIMEOUT)
                    .setSocketTimeout(timeOut).build();
            httpGet.setConfig(requestConfig);
            httpClient = HttpClients.createDefault();
            httpResponse = httpClient.execute(httpGet);
            HttpEntity resEntity = httpResponse.getEntity();
            responseData = EntityUtils.toString(resEntity);
        } catch (Exception e) {
            logger.error("http client exception: {}",e.getMessage());
        } finally {
            long end = System.currentTimeMillis();
            time = end - start;
            //关闭输入流 并记录日志
            dealClose(httpClient, httpResponse);
            dealLog(url, header, params, responseData,time);//日志记录
        }
        return responseData;
    }

    /**
     * 设置头部信息
     *
     * @param httpRequestBase
     * @param header
     */
    private static void setHeader(HttpRequestBase httpRequestBase, HttpHeader header) {
        if (header != null) {
            Map<String, String> headerMap = header.getParams();
            if (headerMap != null && !headerMap.isEmpty()) {
                Set<Map.Entry<String, String>> entries = headerMap.entrySet();
                for (Map.Entry<String, String> entry : entries) {
                    String name = entry.getKey();
                    String value = entry.getValue();
                    httpRequestBase.setHeader(name, value);
                }
            }
        }
    }

    /**
     * 构建url
     *
     * @param url
     * @param query
     * @return
     * @throws IOException
     */
    private static String buildGetUrl(String url, String query) throws IOException {
        if (query == null || "".equals(query)) {
            return url;
        }
        StringBuilder newUrl = new StringBuilder(url);
        boolean hasQuery = url.contains("?");
        boolean hasPrepend = (url.endsWith("?")) || (url.endsWith("&"));
        if (!hasPrepend) {
            if (hasQuery) {
                newUrl.append("&");
            } else {
                newUrl.append("?");
                hasQuery = true;
            }
        }
        newUrl.append(query);
        hasPrepend = false;
        return newUrl.toString();
    }

    /**
     * 关闭资源
     * @param httpClient
     * @param response
     */
    private static void dealClose(CloseableHttpClient httpClient , CloseableHttpResponse response) {
        try {
            if (null != httpClient) {
                httpClient.close();
            }
            if (null != response) {
                response.close();
            }
        } catch (Exception e) {
            logger.error("http client exception: {}",e.getMessage());
        }
    }

    /**
     * 日志记录
     *
     * @param url
     * @param header
     * @param paramers
     * @param responseData
     */
    private static void dealLog(String url, HttpHeader header, HttpParamers paramers, String responseData, long time) {

        Map<String, Object> logMap = new LinkedHashMap<>();
        logMap.put("url", url);
        logMap.put("header", header);
        logMap.put("result", responseData);
        logMap.put("exectime", time);

        if (paramers != null) {
            if (!CollectionUtils.isEmpty(paramers.getParams())) {
                logMap.put("param", paramers.getParams());
            }else {
                logMap.put("param", paramers.getJsonParamer());
            }
        }
        logger.info(JSON.toJSONString(logMap));
    }

//    public static void main(String[] args) {
//        String url = "https://dataapineice.1234567.com.cn/crm-financial/fundTrade/getMonthReport";
//        HttpParamers paramers = new HttpParamers(HttpMethod.POST);
//        paramers.addUrl(url);
//        paramers.addParam("customerNo", "0d105cdea0814c8292d0d2b0e8e756cf");
//        paramers.addParam("month", "202311");
//        paramers.setJsonParamer();
//
//        HttpHeader header = new HttpHeader();
//        header.addParam("Token", "E16DB7EE293748D8B3D1736173286A9D1");
//        String s = HttpClient.doService(paramers, header);
//        System.out.println(s);
//
//    }
}
