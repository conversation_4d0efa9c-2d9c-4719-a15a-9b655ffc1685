package ttfund.web.fortuneservice.utils;


import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 * Function:
 *
 * <AUTHOR>
 * @date 2022/4/2-23:35
 */
public class DateUtils {


    public static String getNowDate() {
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.format(new Date());
    }

    public static Date getDate(String date) {
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return format.parse(date);
        } catch (ParseException e) {
            return null;
        }
    }

    //判断是否超过24小时
    public static boolean isSameDay(Date date1, Date date2) {
        long diff = date2.getTime() - date1.getTime();
        double result = diff * 1.0 / (1000 * 60 * 60);
        if (result <= 24) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 根据当前时间向前或向后取时间 天
     *
     * @param days 天数  负数：向前推n天 ,正数 向后推n天
     * @return
     */
    public static Date calendarDateByDays(int days) {
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DAY_OF_MONTH, days);
        return c.getTime();
    }

    /**
     * 根据传入时间向前或向后取时间 天
     *
     * @param date 传入时间
     * @param days 天数  负数：向前推n天 ,正数 向后推n天
     * @return
     */
    public static Date addDays(Date date, int days) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_MONTH, days);
        return c.getTime();
    }

    /**
     * 根据传入时间向前或向后取 年
     * @param date 传入时间
     * @param years 年数 负数：向前推n年，正数 向后推n年
     * @return
     */
    public static Date addYears(Date date, int years){
        return org.apache.commons.lang3.time.DateUtils.addYears(date,years);
    }

    /**
     * 根据当前时间向前或向后取时间year年month月day天的开始或结束时间戳
     *
     * @param years 天数  负数：向前推n年 ,正数 向后推年
     * @param months 天数  负数：向前推n月 ,正数 向后推n月
     * @param days 天数  负数：向前推n天 ,正数 向后推n天
     * @param type 类型, 0表示获取起始时间戳, 1表示获取结束时间戳, 其他非法参数默认获取结束时间
     * @return
     */
    public static long getTimestamp(int years, int months, int days, int type) {
        Calendar c = Calendar.getInstance();
        c.add(Calendar.YEAR, years);
        c.add(Calendar.MONTH, months);
        c.add(Calendar.DAY_OF_MONTH, days);
        if (type == 0) {
            c.set(Calendar.HOUR_OF_DAY, 0);
            c.set(Calendar.MINUTE, 0);
            c.set(Calendar.SECOND, 0);
            c.set(Calendar.MILLISECOND, 0);
        } else {
            c.set(Calendar.HOUR_OF_DAY, 23);
            c.set(Calendar.MINUTE, 59);
            c.set(Calendar.SECOND, 59);
            c.set(Calendar.MILLISECOND, 999);
        }
        return c.getTimeInMillis();
    }

    /**
     * @Description： 求两个日期的时间差 小时差
     * <AUTHOR>
     * @Date 2022/12/13 16:59
     * @param bigDate 大日期
     * @param smallDate 小日期
     * @return
     */
    public static int diffHour(Date bigDate, Date smallDate) {
        if (null == bigDate || null == smallDate) {
            return 0;
        }
        long from = smallDate.getTime();
        long to = bigDate.getTime();
        return (int) ((to - from)/(1000 * 60 * 60));
    }

    /**
     * 获取指定日期的小时数
     * @param date
     * @return
     */
    public static Integer getHour(Date date){
        if (date == null){
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.HOUR_OF_DAY);
    }

    /**
     * 获得当天最小时间
     *
     * @param date
     * @return
     */
    public static Date getStartOfDay(Date date) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()),
                ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 创建时间：2017年5月24日 下午6:42:30
     * adjustDate(调整时间)
     * 整数往前推,负数往前后推
     * @param date 时间
     * @param year 年
     * @param month 月
     * @param day 日
     * @param hour 时间
     * @param minute 分钟
     * @param second 秒数
     * @return
     * Date
     * @exception
     * @since  1.0.0
     */
    public static Date adjustDate(Date date, int year, int month, int day, int hour, int minute, int second){
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(calendar.YEAR, year);
        calendar.add(calendar.MONTH, month);
        calendar.add(calendar.DATE, day);
        calendar.add(calendar.HOUR, hour);
        calendar.add(calendar.MINUTE, minute);
        calendar.add(calendar.SECOND, second);
        date = calendar.getTime();
        return date;
    }
}
