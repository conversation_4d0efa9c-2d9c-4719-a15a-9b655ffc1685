package ttfund.web.fortuneservice.utils;

import com.mongodb.client.ClientSession;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/13 15:40
 */
public class DBUtil {

    private static Logger logger = LoggerFactory.getLogger(DBUtil.class);

    @Data
    @AllArgsConstructor
    public static class SqlServerClientInfo {
        private Connection connection;
        private PreparedStatement preparedStatement;
    }

    private static ThreadLocal<List<ClientSession>> MONGO_CLIENT = new ThreadLocal<>();
    private static ThreadLocal<List<SqlServerClientInfo>> SQL_SERVER_CLIENT = new ThreadLocal<>();

    /**
     * sqlserver开启事务
     *
     * @param connection 链接
     * @param sql        sql
     * @param parameters 参数
     */
    public static void sqlServerStartTransaction(Connection connection, String sql, List<List<Object>> parameters) {
        if (connection != null) {
            try {
                connection.setAutoCommit(false);
                Statement stm = connection.createStatement();
                PreparedStatement ps = connection.prepareStatement(sql);
                for (int i = 0; i < parameters.size(); i++) {
                    List<Object> paramlist = parameters.get(i);
                    for (int i2 = 0; i2 < paramlist.size(); i2++) {
                        ps.setObject(i2 + 1, paramlist.get(i2));
                    }
                    // 添加批处理SQL
                    ps.addBatch();
                    // 每200条执行一次，避免内存不够的情况
                    if (i > 0 && i % 200 == 0) {
                        ps.executeBatch();
                    }
                }
                // 最后执行剩余不足200条的
                ps.executeBatch();

                List<SqlServerClientInfo> connectionList = SQL_SERVER_CLIENT.get();
                if (connectionList == null) {
                    connectionList = new ArrayList<>();
                    connectionList.add(new SqlServerClientInfo(connection, ps));
                    SQL_SERVER_CLIENT.set(connectionList);
                } else {
                    connectionList.add(new SqlServerClientInfo(connection, ps));
                }
            } catch (SQLException e) {
                try {
                    connection.rollback();
                } catch (SQLException ex) {
                    logger.error("回滚事务失败", ex);
                }
                logger.error("sqlserver开启事务异常", e);
            }
        }
    }

    /**
     * 提交事务
     */
    public static void commitTransaction() {
        try {
            List<SqlServerClientInfo> infoList = SQL_SERVER_CLIENT.get();
            if (!CollectionUtils.isEmpty(infoList)) {
                for (SqlServerClientInfo info : infoList) {
                    if (info.getConnection() != null && !info.getConnection().getAutoCommit()) {
                        try {
                            info.getConnection().commit();
                        } catch (SQLException e) {
                            logger.error("提交事务失败", e);
                            try {
                                info.getConnection().rollback();
                            } catch (SQLException ex) {
                                logger.error("回滚事务失败", ex);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("提交事务异常", e);
        }
    }

    /**
     * 回滚事务
     */
    public static void rollbackTransaction() {
        try {
            List<SqlServerClientInfo> infoList = SQL_SERVER_CLIENT.get();
            if (!CollectionUtils.isEmpty(infoList)) {
                for (SqlServerClientInfo info : infoList) {
                    if (info.getConnection() != null) {
                        if (!info.getConnection().getAutoCommit()) {
                            info.getConnection().rollback();
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("回滚事务异常", e);
        }
    }

    /**
     * sqlserver关闭链接
     */
    public static void closeConnection() {
        try {
            List<SqlServerClientInfo> infoList = SQL_SERVER_CLIENT.get();
            if (!CollectionUtils.isEmpty(infoList)) {
                for (SqlServerClientInfo info : infoList) {
                    if (info.getPreparedStatement() != null) {
                        try {
                            info.getPreparedStatement().close();
                        } catch (SQLException e) {
                            logger.error("关闭 PreparedStatement 失败", e);
                        }
                    }
                    if (info.getConnection() != null) {
                        if (info.getConnection().getAutoCommit()) {
                            info.getConnection().setAutoCommit(false);
                        } else {
                            info.getConnection().setAutoCommit(true);
                        }
                        try {
                            info.getConnection().close();
                        } catch (SQLException e) {
                            logger.error("关闭 Connection 失败", e);
                        }
                    }
                }
            }
            SQL_SERVER_CLIENT.remove();
        } catch (Exception e) {
            logger.error("关闭链接异常", e);
        }
    }




    @Data
    @AllArgsConstructor
    public static class SqlInfo {
        private String sql;
        private List<List<Object>> params;
    }

    /**
     * sql : List<param>
     */
    private static ThreadLocal<List<SqlInfo>> SQL_MAPS = new ThreadLocal<>();

    public static void insertSqlAndParams(String sql, List<List<Object>> params) {
        if (StringUtils.isEmpty(sql)) {
            logger.warn("注入错误sql：{}", sql);
            return;
        }
        if (CollectionUtils.isEmpty(SQL_MAPS.get())) {
            SQL_MAPS.set(new ArrayList<>());
        }
        List<SqlInfo> sqlInfoList = SQL_MAPS.get();
        SqlInfo sqlInfo = new SqlInfo(sql, params);
        sqlInfoList.add(sqlInfo);
    }

    public static void executeSqlsInTransaction(Connection conn) throws SQLException {
        List<PreparedStatement> pstms = new ArrayList<>();
        try {
            if (conn == null) {
                logger.warn("SQL server链接为null");
                return;
            }
            List<SqlInfo> sqlInfoList = SQL_MAPS.get();
            if (CollectionUtils.isEmpty(sqlInfoList)) {
                return;
            }
            // 设置自动提交为false
            conn.setAutoCommit(false);

            for (SqlInfo sqlInfo : sqlInfoList) {
                PreparedStatement ps = conn.prepareStatement(sqlInfo.getSql());
                List<List<Object>> params = sqlInfo.getParams();
                for (int i = 0; i < params.size(); i++) {
                    List<Object> paramlist = params.get(i);
                    for (int i2 = 0; i2 < paramlist.size(); i2++) {
                        ps.setObject(i2 + 1, paramlist.get(i2));
                    }
                    // 添加批处理SQL
                    ps.addBatch();
                    // 每200条执行一次，避免内存不够的情况
                    if (i > 0 && i % 200 == 0) {
                        ps.executeBatch();
                    }
                }
                // 最后执行剩余不足200条的
                ps.executeBatch();
                pstms.add(ps);
            }

            // 提交事务
            conn.commit();
        } catch (SQLException e) {
            // 回滚事务
            conn.rollback();
            logger.error("数据写入失败，数据回滚。");
            // 抛出异常
            throw e;
        } finally {
            // 关闭资源
            if (conn != null) {
                conn.setAutoCommit(true); // 重置自动提交
                conn.close();
            }
            // 关闭PreparedStatement
            for (PreparedStatement pstmt : pstms) {
                if (pstmt != null) {
                    pstmt.close();
                }
            }
            SQL_MAPS.remove();
        }
    }

}
