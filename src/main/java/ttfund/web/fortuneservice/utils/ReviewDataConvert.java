package ttfund.web.fortuneservice.utils;

import com.ttfund.web.base.helper.CommonHelper;
import com.ttfund.web.base.helper.DateHelper;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.model.bo.FundThemeModel;
import ttfund.web.fortuneservice.model.bo.ReviewInfoResponse;
import ttfund.web.fortuneservice.model.dto.FundThemeDto;
import ttfund.web.fortuneservice.model.dto.QuickReviewDto;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> dengchaojun
 * @version : 1.0
 * @email : <EMAIL>
 * @date : 2021-07-30 10:32
 * @description : 数据传输对象转换
 */
public class ReviewDataConvert {

    public static ReviewInfoResponse reviewDataConvert(QuickReviewDto review) {
        if (review == null) {
            return null;
        }
        ReviewInfoResponse temp = new ReviewInfoResponse();
        // Tags 的转换
        if (review.getProductTags2() == null) {
            temp.setProductTags2(new ArrayList<>());
        } else {
            temp.setProductTags2(Arrays.stream(review.getProductTags2().split(",")).distinct().collect(Collectors.toList()));
        }
        if (review.getProductTags() == null) {
            temp.setProductTags(new ArrayList<>());
        } else {
            temp.setProductTags(Arrays.stream(review.getProductTags().split(",")).distinct().collect(Collectors.toList()));
        }
        temp.setId(review.getID());
        temp.setCfhId(review.getCFHID());
        temp.setCfhName(review.getCFHName());
        temp.setPublishTime(DateHelper.dateToStr(review.getUpdateTime(), DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS));
        temp.setMgrId(review.getMGRID());
        temp.setMgrName(review.getMGRName());
        temp.setTitle(review.getTitle());
        temp.setReviewContent(review.getReviewContent());
        temp.setEmotionType(review.getEmotionType());
        temp.setProductType(review.getProductType());
        temp.setProductCode(review.getProductCode());
        temp.setProductType2(review.getProductType2());
        temp.setProductCode2(review.getProductCode2());
        temp.setTimepoint(CommonHelper.toStr(review.getTimepoint()));

        temp.setProductSyImg(review.getProductSyImg());
        temp.setProductSyImg2(review.getProductSyImg2());
        temp.setWeight(review.getWeight());
        List<FundThemeDto> themeLabel = review.getThemeLabel();
        List<FundThemeModel> themeLabelModels = new ArrayList<>();
        if (!CollectionUtils.isEmpty(themeLabel)) {
            for (FundThemeDto fundTheme : themeLabel) {
                themeLabelModels.add(new FundThemeModel(fundTheme.getThemeId(), fundTheme.getThemeName()));
            }
        }
        temp.setThemeLabel(themeLabelModels);
        temp.setLabelType(review.getLabelType());
        return temp;
    }
}
