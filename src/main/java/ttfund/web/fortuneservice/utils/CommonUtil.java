package ttfund.web.fortuneservice.utils;

import com.ttfund.web.base.helper.DateHelper;
import org.apache.commons.lang3.StringUtils;
import ttfund.web.fortuneservice.constant.CommonConstant;

import java.util.Calendar;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Calendar;
import java.util.Date;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 通用工具类
 * <AUTHOR>
 * @version 1.0.0
 * @className CommonUtil
 * @date 2023/4/8 8:39
 */
public class CommonUtil {

    public static Long getTimePoint(Date dt) {
        return DateHelper.dateToUnixTimeMillisecond(dt) * 1000000;
    }

    /**
     * 获取指定位uuid，最高32
     * <AUTHOR>
     * @date 2023/4/14 15:20
     * @param index index
     * @return java.lang.String
     */
    public static String getGuId(int index){
        String uuid = UUID.randomUUID().toString().replace("-", "");
        return uuid.substring(0, index);
    }

    /**
     * 处理http
     * <AUTHOR>
     * @date 2023/4/14 15:21
     * @param http http
     * @return java.lang.String
     */
    public static String replaceHttp(String http) {
        String urlHtpps = null;
        if (StringUtils.isEmpty(http)) {
            return urlHtpps;
        }
        urlHtpps = http.replaceFirst("http://", "https://");
        return urlHtpps;
    }

    /**
     * <AUTHOR>
     * @date 2023/4/14 15:21
     * @param mobile mobile
     * @return boolean
     */
    public static boolean isMobile(String mobile) {
        String regex = "^((13[0-9])|(14[5,7,9])|(15([0-3]|[5-9]))|(16[5,6])|(17[0-8])|(18[0-9])|(19[1、5、8、9]))\\d{8}$";
        Pattern p = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
        Matcher m = p.matcher(mobile);
        return m.matches();
    }

    /**
     * 获取指定date所在月的第一天
     *
     * @param date 指定日期
     * @return date
     **/
    public static Date getFirstDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    /**
     * 获取指定date所在月最后一天
     *
     * @param date 指定日期
     * @return 当月第一天
     */
    public static Date getLastDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return calendar.getTime();
    }

    /**
     * 判断是否为Date所在月是否为每季度的第一月（1，4，7，10）
     *
     * @param date 指定日期
     * @return boolean
     **/
    public static boolean isFirstMonthOfQuarter(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int month = calendar.get(Calendar.MONTH);
        return month == Calendar.JANUARY || month == Calendar.APRIL || month == Calendar.JULY || month == Calendar.OCTOBER;
    }

    /**
     * 将给定日期转换为 形如 2023-05-09 0:00:00
     * @param date 日期
     * @return 给定格式的日期
     */
    public static Date getCurrentBelongDay(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取当月第一天
     * @param instance 日历实例
     * @return 当月第一天
     */
    public static Date getFirstDayOfMonth(Calendar instance){
        instance.set(Calendar.DAY_OF_MONTH, instance.getActualMinimum(Calendar.DAY_OF_MONTH));
        return instance.getTime();

    }

    /**
     * 获取当月最后一天
     * @param instance 日历实例
     * @return 当月第一天
     */
    public static Date getLastDayOfMonth(Calendar instance){
        instance.set(Calendar.DAY_OF_MONTH, instance.getActualMaximum(Calendar.DAY_OF_MONTH));
        return instance.getTime();
    }


    /**
     * 获取当前日期所在月份
     * @return 月份
     */
    public static int getCurrentMonth(Calendar instance){
        return instance.get(Calendar.MONTH) + 1;
    }

    /**
     * 根据月份数获取对应的季度
     * @param monthNum 月份数，从1开始
     * @return 季度数
     */
    public static int getQuarterNum(int monthNum){
        if (0 < monthNum && monthNum <= 3) {
            return 1;
        } else if (monthNum <= 6) {
            return 2;
        } else if (monthNum <= 9) {
            return 3;
        } else if (monthNum <= 12) {
            return 4;
        }
        return -1;
    }

    /**
     * 根据月份数获取上个季度数
     * @param monthNum 月份数，从1开始
     * @return 季度数
     */
    public static int getLastQuarterNum(int monthNum) {
        if (0 < monthNum && monthNum <= 3) {
            return 4;
        } else if (monthNum <= 6) {
            return 1;
        } else if (monthNum <= 9) {
            return 2;
        } else if (monthNum <= 12) {
            return 3;
        }
        return -1;

    }


     /**
     * 将参数按指定格式进行url编码
     * @param param 参数
     * @return String
     */
    public static String encodeParam (String param) {
        return encodeParam(param, "UTF-8");
    }

    /**
     * 将参数按指定格式进行url编码
     * @param param 参数
     * @param format 格式 默认为UTF-8格式
     * @return String
     */
    public static String encodeParam (String param, String format){
        if (StringUtils.isEmpty(param)) {
            return null;
        }
        String encodeString = "";
        try {
            encodeString = URLEncoder.encode(param, format);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return encodeString;
    }

    private CommonUtil() {
        throw new IllegalStateException(CommonConstant.UTILITY_CLASS);
    }

    /**
     * 根据传入时间向前或向后取时间 天
     *
     * @param date 传入时间
     * @param days 天数  负数：向前推n天 ,正数 向后推n天
     * @return Date
     */
    public static Date addDays(Date date, int days) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_MONTH, days);
        return c.getTime();
    }
}
