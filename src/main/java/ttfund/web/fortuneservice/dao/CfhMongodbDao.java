package ttfund.web.fortuneservice.dao;

import com.alibaba.fastjson.JSONObject;
import org.bson.types.ObjectId;
import org.springframework.scheduling.annotation.Async;
import ttfund.web.fortuneservice.model.bo.CFHUpdateInfoBo;
import ttfund.web.fortuneservice.model.dto.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 财富号 mongodb交互
 * <AUTHOR>
 * @version 1.0.0
 * @interfaceName CfhMongodbDao
 * @date 2023/4/7 15:08
 */
public interface CfhMongodbDao {

    /**
     * 批量获取天天快评
     *
     * @param timePointBegin 时间线基准
     * @param timePointEnd   时间线基准
     * @param pageSize 一次获取的数据量
     * @return 快评列表
     * <AUTHOR>
     */
    List<QuickReviewDto> getQuickReview(long timePointBegin, long timePointEnd, int pageSize, Integer labelType);

    /**
     * 查手动权重生效的财富号权重
     * <AUTHOR>
     * @date 2023/4/10 14:59
     * @param cfhIds 财富号id集合
     * @return java.util.List
     */
    List<CfhListDto> getCFHWeight(List<String> cfhIds);

    /**
     * 获取标签json - TTFundCFHDB.Tb_CFHFundTheme
     * <AUTHOR>
     * @date 2023/4/7 15:16
     * @return java.util.List
     */
    List<FundLabelDto> getQuickReviewTheme();

    /**
     * 新增或修改数据 - TTFundCFHDB.TB_CFHGeneralData
     * <AUTHOR>
     * @date 2023/4/7 15:14
     * @param key 主键key
     * @param model 存入数据
     * @return boolean
     */
    boolean upsertCFHGeneralData(String key, Object model);

    /**
     * 删除财富号公告表中EID不存在于源数据表中或已经过期的公告
     * <AUTHOR>
     * @date 2023/4/10 14:42
     * @param noticeEIDs 公告EID字段集合
     * @return int
     */
    int deleteByCondition(List<String> noticeEIDs, Date nowDate);

    /**
     *
     * <AUTHOR>
     * @date 2023/4/14 15:43
     * @param cfhLabelList cfhLabelList
     * @return int
     */
    int saveOrUpdateCFHList(List<CFHInfoDto> cfhLabelList);

    /**
     *
     * <AUTHOR>
     * @date 2023/4/14 15:43
     * @param seqId seqId
     * @param defaultValue defaultValue
     * @return java.lang.String
     */
    String getCFHGeneralData(String seqId, String defaultValue);

    /**
     * @param cfhId     cfhId
     * @param cfhStatus
     * @return ttfund.web.fortuneservice.model.dto.CFHArticleDto
     * <AUTHOR>
     * @date 2023/4/14 15:43
     */
    CFHArticleDto getCFHArticleByAuthor(String cfhId, Integer cfhStatus);

    /**
     *
     * <AUTHOR>
     * @date 2023/4/14 15:43
     * @param cfhId cfhId
     * @param cfhStatus cfhStatus
     * @return boolean
     */
    boolean updateCFHArticle(String cfhId, Integer cfhStatus);

    List<QuickReviewDto> getQuickReviewByActivity(String activityId);

    /**
     * 插入 定期检测报告内容 到库中
     * <AUTHOR>
     * @date 2023/5/6 15:43
     * @param reportContentDtoList reportContentDtoList
     * @return int
     */
    int batchSaveRegularReportContentList(List<RegularInspectionReportContentDto> reportContentDtoList);

    /**
     * 插入 定期检测报告 到库中
     * <AUTHOR>
     * @date 2023/5/6 15:43
     * @param reportDtoList reportDtoList
     * @return int
     */
    int batchSaveRegularReportList(List<RegularInspectionReportDto> reportDtoList);

    /**
     * 判断当天日期是该月的第几个交易日
     * @param now 当前日期
     * @param referenceDate 正序还是倒序
     * @param reversed 第几个交易日
     * @return long
     */
    long getSpecialDayNumOfMonth(Date now, Date referenceDate, boolean reversed);

    /**
     * 获取产品解读数据
     * @return 产品报告map {k:财富号id，v:报告名称列表}
     */
    Map<String, List<String>> getReportHistoryIdList();

    /**
     * 获取date时间以前最近的一个交易日
     * @param date  时间点
     * @return 交易日
     **/
    String getLastTradeDay(Date date);

    /**
     * 查询指定时间范围的交易日信息
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return List<DaysInfoMongoDto>
     */
    List<DaysInfoMongoDto> getDaysInfoByTimeRange(Date startDate, Date endDate);

    void updateCardWhite(String baseUrl, String giftid, int type);

    void insertGiftLog(String key, JSONObject request, JSONObject response, String giftid, Integer giftcount, String requestip, Long exectime, String userid, String url, String passportid, String referer, String time,String tableName);

    /**
     * 获取所有有效财富号(Status != 0)的Id
     * @return
     */
    List<String> getAllCFHIds();

    /**
     * 批量更新财富号信息如关注数、浏览数等
     * @param updateInfoList 带更新的列表
     * @return 更新总数
     */
    int updateCFHInfos(List<CFHUpdateInfoBo> updateInfoList);

    public Date getBreakPoint(String seqId,String defaultValue);

    Date getMoudleCache(String refreshTheMarker);

    void updateTheme(String code, String name, int status);

    /**
     * 季报专用
     * @param read
     * @return
     */
    int saveProductRead(CFHProductReadDto read);

    int saveCommunityDataDay(List<CommunityDataDay> dataList);

    int insertReferenceFundDayData(List<ReferenceFundDayDTO> dataTable);

    int insertReferenceFundJJGSData(List<ReferenceFundDayDTO> dataTable);

    int insertReferenceFundMonthData(List<ReferenceFundDayDTO> dataTable);

    Map<String, Integer> getLastInterfaceFundDayS(Date pdate);

    int insertInterfaceFundDayData(List<InterfaceFundDayDTO> dataTable);

    Map<String, Integer> getLastInterfaceFundJJGSS(Date pdate);

    int insertInterfaceFundJJGSData(List<InterfaceFundDayDTO> dataTable);

    int insertFundCustnoFeaturEData(List<InterfaceFundDayDTO> dataTable);

    int deleteFundCustnoFeatureByFeatureType(String featerType);

    int insertCustnoFeaturEData(List<InterfaceFundDayDTO> dataTable);

    int insertCFTTradeRankData(List<CFHTradeRankDataDTO> dataTable);

    Date getBreakPoint(String seqId, String defaultValue, String timeFormat);

    <T> int insertOrUpdate(List<T> data, String databaseName, String collectionName);

    @Async
    void saveMessage(String message, String topic, ObjectId id);

    <T> int insertInc(T data, String databaseName, String collectionName,String ... incFiled);

    List<ShortVideoMongoAvInfoDTO> getShortVideoByAuthorAndTime(String cfhId, String emuid, Date startTime, Date endTime);

    int deleteAll(String dbName, String collectionName);

    Long selectNewUpdateTradeDate();


    int deleteByTime(String dbName, String collectionName, String fieldName, int day);

    int deleteMessage(Date date, String topic);

    List<String> getAllTopic();

    void getAllCfhAndSetCache();

    Integer selectProductReportDataSync();
}
