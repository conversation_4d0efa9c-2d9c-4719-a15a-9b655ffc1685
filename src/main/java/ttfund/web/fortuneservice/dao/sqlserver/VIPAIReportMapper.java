package ttfund.web.fortuneservice.dao.sqlserver;

import ttfund.web.fortuneservice.model.entity.VIPAIReport;
import java.util.List;

/**
 * AI月报处理结果Mapper
 */
public interface VIPAIReportMapper {
    
    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(String id);
    
    /**
     * 插入记录
     */
    int insert(VIPAIReport record);
    
    /**
     * 选择性插入记录
     */
    int insertSelective(VIPAIReport record);
    
    /**
     * 根据主键查询
     */
    VIPAIReport selectByPrimaryKey(String id);
    
    /**
     * 选择性更新记录
     */
    int updateByPrimaryKeySelective(VIPAIReport record);
    
    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(VIPAIReport record);
    
    /**
     * 根据CFHID和报告日期查询
     */
    VIPAIReport selectByCfhIdAndReportDate(String cfhid, String reportDate);
    
    /**
     * 根据任务配置ID和报告日期查询
     */
    List<VIPAIReport> selectByTaskConfigIdAndReportDate(String taskConfigId, String reportDate);
}
