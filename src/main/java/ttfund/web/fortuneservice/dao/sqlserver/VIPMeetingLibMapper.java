package ttfund.web.fortuneservice.dao.sqlserver;

import ttfund.web.fortuneservice.model.entity.VIPMeetingLib;

import java.util.List;

public interface VIPMeetingLibMapper {
    int deleteByPrimaryKey(String id);

    int insert(VIPMeeting<PERSON>ib record);

    int insertSelective(VIPMeeting<PERSON>ib record);

    VIPMeetingLib selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(VIPMeetingLib record);

    int updateByPrimaryKey(VIPMeetingLib record);

    List<VIPMeetingLib> selectByMeetingId(String meetingId);
}