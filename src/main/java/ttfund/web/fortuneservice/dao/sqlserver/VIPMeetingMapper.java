package ttfund.web.fortuneservice.dao.sqlserver;

import ttfund.web.fortuneservice.model.entity.VIPMeeting;

import java.util.Date;
import java.util.List;

public interface VIPMeetingMapper {
    int deleteByPrimaryKey(String id);

    int insert(VIPMeeting record);

    int insertSelective(VIPMeeting record);

    VIPMeeting selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(VIPMeeting record);

    int updateByPrimaryKey(VIPMeeting record);

    List<VIPMeeting> selectSuccessById(String id);

    List<VIPMeeting> selectList(Date date);

    List<VIPMeeting> selectById(String id);

    List<VIPMeeting> selectSummaryList();
}