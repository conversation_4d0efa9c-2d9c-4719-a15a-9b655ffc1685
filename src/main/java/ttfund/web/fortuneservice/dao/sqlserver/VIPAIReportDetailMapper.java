package ttfund.web.fortuneservice.dao.sqlserver;

import ttfund.web.fortuneservice.model.entity.VIPAIReportDetail;
import java.util.List;

/**
 * AI月报处理结果详细信息Mapper
 */
public interface VIPAIReportDetailMapper {
    
    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(String id);
    
    /**
     * 插入记录
     */
    int insert(VIPAIReportDetail record);
    
    /**
     * 选择性插入记录
     */
    int insertSelective(VIPAIReportDetail record);
    
    /**
     * 根据主键查询
     */
    VIPAIReportDetail selectByPrimaryKey(String id);
    
    /**
     * 选择性更新记录
     */
    int updateByPrimaryKeySelective(VIPAIReportDetail record);
    
    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(VIPAIReportDetail record);
    
    /**
     * 根据报告ID查询详细信息列表
     */
    List<VIPAIReportDetail> selectByReportId(String reportId);
    
    /**
     * 根据报告ID和报告类型查询
     */
    VIPAIReportDetail selectByReportIdAndType(String reportId, String reportType);
    
    /**
     * 批量插入
     */
    int batchInsert(List<VIPAIReportDetail> records);
}
