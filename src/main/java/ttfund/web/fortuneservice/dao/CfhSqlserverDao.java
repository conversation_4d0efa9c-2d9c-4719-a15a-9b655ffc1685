package ttfund.web.fortuneservice.dao;

import ttfund.web.fortuneservice.model.FundCode;
import ttfund.web.fortuneservice.model.dto.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @interfaceName CfhSqlserverDao
 * @date 2023/4/14 14:56
 */
public interface CfhSqlserverDao {

    CFHBaseInfoDto selectByCFHId(String cfhId);

    boolean insert(CFHBaseInfoDto cfhInfo);

    boolean updateById(CFHBaseInfoDto cfhInfo);

    List<CFHInfoDto> selectAllCFHInfo();

    List<CFHLabelDto> selectAllLabel();

    List<CFHInfoDto> selectCFHInfoByUpdate(Date breakPoint);

    boolean updateCFHArticle(String cfhId, Integer status);

    /**
     * 根据周期任务类型 查询 任务配置
     *
     * @param isOnceTask     是否为周期任务
     * @param breakpointTime 指定查询时间
     * @param nowDate        当前时间
     * @return 任务配置
     **/
    Map<ResearchTaskConfigDto, ResearchTaskGroupDto> selectByTaskCycles(boolean isOnceTask, Date breakpointTime, Date nowDate);
    Map<ResearchTaskConfigDto, ResearchTaskGroupDto> selectVIPByTaskCycles(boolean isOnceTask, Date breakpointTime, Date nowDate);

    /**
     * 批量插入Tb_CFHResearchTaskList
     *
     * @param list 待插入的对象
     * @return 是否插入成功
     **/
    Boolean insertResearchTaskList(List<ResearchTaskListDto> list);
    Boolean insertVIPTaskList(List<ResearchTaskListDto> list);

    /**
     * 从Tb_CFHResearchTaskSubject获取  配置类的题目
     * 配置类题目：TaskConfigID=Tb_CFHResearchTaskConfig.ID，TaskCycleMark is null
     * 分发给机构的题目：TaskConfigID=Tb_CFHResearchTaskConfig.ID，TaskCycleMark=跟Tb_CFHResearchTaskList值保持一致
     *
     * @param periodTaskConfigIds 任务配置id
     * @return 配置类的题目
     **/
    List<ResearchTaskSubjectDto> selectConfigSubject(List<String> periodTaskConfigIds);
    List<ResearchTaskSubjectDto> selectVIPConfigSubject(List<String> periodTaskConfigIds);

    /**
     * 插入为定期观点生成的试题
     *
     * @param data 试题
     * @return 生成结果
     **/
    boolean insertResearchSubject(List<ResearchTaskSubjectDto> data);
    boolean insertVIPSubject(List<ResearchTaskSubjectDto> data);

    /**
     * 修改周期任务的TaskReleaseTime
     *
     * @param periodTaskReleaseTimeMap map<周期任务的ID, 修改后的TaskReleaseTime>
     * @return 修改结果
     */
    boolean updatePeriodTaskReleaseTime(Map<String, Date> periodTaskReleaseTimeMap);
    boolean updatePeriodVIPTaskReleaseTime(Map<String, Date> periodTaskReleaseTimeMap);

    boolean insertNoticeList(List<TaskNoticeDto> taskNoticeList);

    List<TaskNoticeUserDto> getNoticeUser(String cfhId);

    TaskNoticeConfigDto getNoticeConfig(int noticeType);

    List<CFHWhiteSheetDTO> selectAllCFHRealCompany();

    List<CFHInfoDto> selectAllCFH();

    CFHBaseInfoDto selectByUID(String emuid);

    List<FundCode> selectFundByCodes(List<String> codes);

    boolean insertCFHUser(CFHInfoDto cfhInfoDto);

    CFHInfoDto getCfhUserById(String id);

    boolean updateCfhUser(CFHInfoDto cfhInfoDto);

    boolean insertNotice(TaskNoticeDto notice);

    List<CFHUser> getCfhUserByCFHId(String cfhId);
}
