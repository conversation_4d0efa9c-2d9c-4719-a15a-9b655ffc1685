package ttfund.web.fortuneservice.dao;


import ttfund.web.fortuneservice.model.dto.FundApplyDataStatModel;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 财富号交易 mongodb交互
 */
public interface CfhTradeMongodbDao {
    Set<String> getTableNames();

    boolean dropTablesByName(Set<String> needDropTableNames);

    void updateAndInsertCFHData(Date date, List<FundApplyDataStatModel> models);

    void deleteAndInsertCFHData(Date date, List<FundApplyDataStatModel> models);
}
