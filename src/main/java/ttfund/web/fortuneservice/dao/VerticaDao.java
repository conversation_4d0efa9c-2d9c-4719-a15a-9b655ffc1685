package ttfund.web.fortuneservice.dao;

import com.mongodb.BasicDBObject;
import org.bson.Document;
import ttfund.web.fortuneservice.model.dto.*;

import java.util.Date;
import java.util.List;

/**
 * VerticaDao.java
 *
 * <AUTHOR>
 * @date 2023/5/6 16:44
 */
public interface VerticaDao {

    /**
     * 获取高端理财产品
     *
     * @return
     **/
    List<FundHighLevelInfo> getFundCodeType();

    /**
     * 获取基金规模
     * @param fundCodes 基金代码列表
     * @return 基金规模实体类
     */
    List<FundSizeDto> getFundSizeInfo(List<String> fundCodes);

    /**
     * 获取关停基金列表
     * @param date 日期
     * @return List<String>
     */
    List<String> getCloseFund(Date date);

    /**
     * 获取基金的最新净值日期
     *
     * @param fundCodes 基金码
     * @return List<FundSizeInfo>
     **/
    List<FundNetWorthDto> getFundNetWorthInfo(List<String> fundCodes);

    List<CommunityDataDayVisitDto> getCommunityDataDayVisitors(Date date);

    List<CommunityDataDaysOfMouthDto> getCommunityDataDaysOfMouth(Date date);

    List<CommunityDataDayTradeDto> getCommunityDataDayTreade(Date now);

    List<CommunityDataDayGroupUserDto> getCommunityDataGroupUser(Date now);

    List<CommunityDataDayGroupUserInOutDto> getCommunityDataGroupUserInOut(Date now);

    List<CommunityDataDayGroupUserNumDto> getCommunityDataGroupUserNum(Date now);

    boolean delByDayAndInsertData(String delSql, String insertSql, Date dt, List<List<Object>> parameters);

    List<FundApplyDataStatModel> statTradeData(Date transactionDate);

    boolean insertTradeDetails(List<TradeDetailRecord> models);

    int selectReferencefundDayRun();

    Date selectCalDay();

    List<ReferenceFundDayDTO> selectReferenceFundDayData(Date calDay);
    List<ReferenceFundDayDTO> selectReferenceFundJJGSData(Date calDay);

    List<ReferenceFundDayDTO> selectReferenceFundMonthData(Date calDay);

    int selectInterfaceFundDayRun();

    List<InterfaceFundDayDTO> selectInterfaceFundDay(Date calDay);

    List<InterfaceFundDayDTO> selectInterfaceFundJJGS(Date calDay);

    List<InterfaceFundDayDTO> selectSql(String sql);

    int selectRun(String sql);

    Date getLiveScoreFieldsSyncMaxLastUpdateTime(Date breakPoint);

    List<LiveScoreFieldsSynModel> getLiveScoreFields(Date breakPoint);

    List<LiveScoreFundSynModel> getLiveScoreFundFields();

    <T> List<T> selectSql(String sql, Class<T> clazz);

    Boolean insertTradeDetail(TradeDetailRecord record);

    boolean updateTradeDetailByApplyNo(TradeDetailRecord record);

    boolean revertTradeDetailByApplyNo(TradeDetailRecord record);

    boolean revertTradeDetailByApplyNos(List<TradeDetailRecord> revertList);

    List<String> selectSql(String string, List<Object> paramList, Class<String> stringClass);

    List<CFHTradeRankDataDTO> selectCFHTradeRankData(Date dt, Date date);
}
