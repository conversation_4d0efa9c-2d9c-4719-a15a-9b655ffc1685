package ttfund.web.fortuneservice.dao.mongo;


import com.google.common.collect.Lists;
import com.mongodb.BasicDBObject;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.model.BulkWriteOptions;
import com.mongodb.client.model.UpdateOneModel;
import com.mongodb.client.model.UpdateOptions;
import com.mongodb.client.model.WriteModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import ttfund.web.fortuneservice.config.MongoConfig;
import ttfund.web.fortuneservice.model.dto.MeetingSummaryPoint;
import ttfund.web.fortuneservice.utils.TableColumn;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

import static ttfund.web.fortuneservice.dao.impl.CfhMongodbDaoImpl.BATCH_SIZE;

@Repository
@Slf4j
public class CFHMongoMapper {

    @Autowired
    private MongoConfig mongoConfig;


    public <T> int insertOrUpdateById(List<T> list, String collectionName) {
        int result = 0;
        if (list == null || list.isEmpty()) {
            return result;
        }

        List<WriteModel<Document>> writeModels = new ArrayList<>();
        for (T item : list) {
            Document updateDoc = buildUpdateDocument(item);
            BasicDBObject upsetbyinsert = new BasicDBObject();
            Object id = updateDoc.get("_id");
            upsetbyinsert.append("_id", id);
            BasicDBObject upset = new BasicDBObject("$setOnInsert", upsetbyinsert);
            updateDoc.remove("_id");
            upset.put("$set", updateDoc);
            BasicDBObject query = new BasicDBObject("_id", id);
            UpdateOneModel<Document> pair = new UpdateOneModel<>(query, upset, new UpdateOptions().upsert(true));
            writeModels.add(pair);
        }

        List<List<WriteModel<Document>>> batches = Lists.partition(writeModels, BATCH_SIZE);

        for (List<WriteModel<Document>> batch : batches) {
            BulkWriteOptions options = new BulkWriteOptions().ordered(false);
            BulkWriteResult temp2 = mongoConfig.getCfhMongoTemplate().getCollection(collectionName).bulkWrite(batch, options);
            log.info("批量插入或更新数TTFundCFHDB据库，表{}，新增：{}，修改：{}，匹配无需修改：{}"
                    , collectionName, temp2.getInsertedCount(), temp2.getModifiedCount()
                    , temp2.getMatchedCount() - temp2.getModifiedCount());
            result += temp2.getInsertedCount() + temp2.getModifiedCount();
        }
        return result;
    }

    private <T> Document buildUpdateDocument(T obj) {
        Document doc = new Document();
        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                String name = field.getName();
                Object value = field.get(obj);
                TableColumn tableColumn = field.getAnnotation(TableColumn.class);
                if (tableColumn != null && StringUtils.isNotEmpty(tableColumn.name())) {
                    name = tableColumn.name();
                }
                if (value instanceof BigDecimal) {
//                    doc.append(name, ((BigDecimal) value).doubleValue());
                    BigDecimal bigDecimalValue = (BigDecimal) value;
                    int scale = bigDecimalValue.scale(); // 获取小数位数
                    BigDecimal roundedValue = bigDecimalValue.setScale(scale, RoundingMode.HALF_UP); // 四舍五入
                    double doubleValue = roundedValue.doubleValue();
                    doc.append(name, doubleValue);
                }
                if (value != null) {
                    doc.append(name, value);
                }
            } catch (IllegalAccessException e) {
                log.error("无法访问字段: " + field.getName(), e);
            }
        }
        return doc;
    }

}
