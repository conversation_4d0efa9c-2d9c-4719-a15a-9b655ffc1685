package ttfund.web.fortuneservice.dao.impl;

import com.mongodb.BasicDBObject;
import com.ttfund.web.base.helper.CommonHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import ttfund.web.fortuneservice.config.App;
import ttfund.web.fortuneservice.dao.TgMongodbDao;

import java.util.List;

@Slf4j
@Repository
public class TgMongodbDaoImpl implements TgMongodbDao {
    @Autowired
    private App app;

    @Override
    public <T> List<T> selectCollection(String dbName, String collectionName, Class<T> clazz) {
        BasicDBObject query = new BasicDBObject();
        BasicDBObject field = new BasicDBObject(CommonHelper.fieldDic(clazz));
        return app.tgMongodbRead.query(dbName, collectionName, query, field, null, 1, Integer.MAX_VALUE
                , Integer.MAX_VALUE, clazz);
    }
}
