package ttfund.web.fortuneservice.dao.impl;

import cn.hutool.core.text.StrBuilder;
import com.alibaba.fastjson.JSON;
import com.ttfund.web.base.helper.DateHelper;
import com.ttfund.web.core.annotation.AopCache;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.config.App;
import ttfund.web.fortuneservice.dao.CfhSqlserverDao;
import ttfund.web.fortuneservice.model.FundCode;
import ttfund.web.fortuneservice.model.dto.*;
import ttfund.web.fortuneservice.utils.DBUtil;
import ttfund.web.fortuneservice.utils.TimeUtil;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className CfhSqlserverDaoImpl
 * @date 2023/4/14 15:06
 */
@Component
public class CfhSqlserverDaoImpl implements CfhSqlserverDao {

    private static final Logger logger = LoggerFactory.getLogger(CfhSqlserverDaoImpl.class);

    @Resource
    private App app;

    @Override
    public CFHBaseInfoDto selectByCFHId(String cfhId) {
        CFHBaseInfoDto cfhBaseInfoDO = null;
        String sql = "SELECT * FROM TTFundCFH.dbo.Tb_CFHBaseInfo WHERE CFHID =? ORDER BY UpDataTime desc;";
        List<Map> maps = app.getSqlServer().executeQuery(sql, Arrays.asList(cfhId));
        if (!maps.isEmpty()) {
            cfhBaseInfoDO = JSON.parseObject(JSON.toJSONString(maps.get(0)), CFHBaseInfoDto.class);
        }
        return cfhBaseInfoDO;
    }

    @Override
    public boolean insert(CFHBaseInfoDto cfhInfo) {
        boolean result = false;
        String sql = "insert into dbo.Tb_CFHBaseInfo (" +
                "ID, RelatedUid, Status, CommpanyCode,CommpanyName,OrganizationType,CFHID,CFHName,HeaderImgPath,Summary,Slogans,CreatTime,UpDataTime" +
                ") " +
                "values  (?,?,?,?,?,?,?,?,?,?,?,?,?);";
        List<Object> params = Arrays.asList(cfhInfo.getID(), cfhInfo.getRelatedUid(), cfhInfo.getStatus(), cfhInfo.getCommpanyCode(), cfhInfo.getCommpanyName(),
                cfhInfo.getOrganizationType(), cfhInfo.getCFHID(), cfhInfo.getCFHName(), cfhInfo.getHeaderImgPath(), cfhInfo.getSummary(),
                cfhInfo.getSlogans(), cfhInfo.getCreatTime(), cfhInfo.getUpDataTime());
        result = app.getSqlServer().execute(sql, params);
        return result;
    }

    @Override
    public boolean updateById(CFHBaseInfoDto cfhInfo) {
        boolean result = false;
        String sql = "UPDATE TTFundCFH.dbo.Tb_CFHBaseInfo SET CommpanyCode = ?,CFHName = ?,Summary = ? ,HeaderImgPath = ? ,OrganizationType = ?,Status = ?," +
                "CommpanyName = ?,Slogans = ?,UpDataTime = ?,RelatedUid = ? WHERE CFHID = ?;";
        List<Object> params = Arrays.asList(cfhInfo.getCommpanyCode(), cfhInfo.getCFHName(), cfhInfo.getSummary(), cfhInfo.getHeaderImgPath(), cfhInfo.getOrganizationType(),
                cfhInfo.getStatus(), cfhInfo.getCommpanyName(), cfhInfo.getSlogans(), cfhInfo.getUpDataTime(), cfhInfo.getRelatedUid(),
                cfhInfo.getCFHID());
        result = app.getSqlServer().execute(sql, params);
        return result;
    }

    @Override
    public List<CFHInfoDto> selectAllCFHInfo() {
        List<CFHInfoDto> result = new ArrayList<>();
        String sql = "select a.*,s.BlackState,s.DefaultWeight,s.WeightStartTime,s.WeightEndTime" +
                " from TTFundCFH.dbo.Tb_CFHBaseInfo a " +
                "left join TTFundCFH.dbo.Tb_CFHBaseSettings s on a.CFHID=s.CFHID where IsDel =0 or IsDel is null";
        List<Map> maps = app.getSqlServer().executeQuery(sql, null);
        if (!maps.isEmpty()) {
            result = JSON.parseArray(JSON.toJSONString(maps), CFHInfoDto.class);
        }
        return result;
    }

    @Override
    public List<CFHLabelDto> selectAllLabel() {
        List<CFHLabelDto> result = new ArrayList<>();
        String sql = "select * from Tb_CFHLabel where Status=1 and IsDelete=0 and StartTime < ? and EndTime > ?";
        Date nowDate = DateHelper.getNowDate();
        List<Map> maps = app.getSqlServer().executeQuery(sql, Arrays.asList(nowDate, nowDate));
        if (!maps.isEmpty()) {
            result = JSON.parseArray(JSON.toJSONString(maps), CFHLabelDto.class);
        }
        return result;
    }

    @Override
    public List<CFHInfoDto> selectCFHInfoByUpdate(Date breakPoint) {
        //向前推1s
        breakPoint = DateUtils.addSeconds(breakPoint, -1);
        List<CFHInfoDto> cfhInfoDOS = selectAllCFHInfo();
        //过滤无财富号id的,和指定时间之前的并排序
        Date finalBreakPoint = breakPoint;
        cfhInfoDOS = cfhInfoDOS.stream()
                .filter(o -> StringUtils.isNotEmpty(o.getCFHID()))
                .filter(o-> finalBreakPoint.before(o.getUpDataTime()))
                .collect(Collectors.toList());

        return cfhInfoDOS;
    }

    @Override
    public boolean updateCFHArticle(String cfhId, Integer status) {
        boolean result = false;
        String sql = "UPDATE Tb_CFHArticle WITH(ROWLOCK) SET ISCFH = ? WHERE AuthorId = ?";
        List<Object> params = Arrays.asList(status, cfhId);
        result = app.getSqlServer().execute(sql, params);
        return result;
    }


    @Override
    public Map<ResearchTaskConfigDto, ResearchTaskGroupDto> selectByTaskCycles(boolean isOnceTask, Date breakpointTime, Date nowDate) {
        Map<ResearchTaskConfigDto, ResearchTaskGroupDto> result = new HashMap<>();
        List<ResearchTaskConfigDto> taskConfigList = new ArrayList<>();
        List<ResearchTaskGroupDto> taskGroupList = new ArrayList<>();
        String sql;
        List<Object> param = new ArrayList<>();
        if (isOnceTask) {
            // 筛选一次性任务: 上次时间断点<TaskReleaseTime < 当前时间
            sql = "SELECT tctc.ID, TaskName, TaskContent, TaskFileUrl, TaskType, TaskCycles, TaskGroupId, TaskReleaseTime, TaskValidDay, TaskGroupName, TaskGroupContent, tctg.UpdateTime\n" +
                    "FROM TTFundCFH.dbo.Tb_CFHResearchTaskConfig tctc  \n" +
                    "JOIN TTFundCFH.dbo.Tb_CFHResearchTaskGroup tctg ON tctc.TaskGroupId = tctg.ID  \n" +
                    "WHERE tctc.IsDel = 0 AND tctg.IsDel = 0 AND tctc.TaskCycles = 0 AND tctc.TaskReleaseTime > ? AND tctc.TaskReleaseTime < ? \n" +
                    "ORDER BY tctc.TaskGroupId;";
            param.add(breakpointTime);
        } else {
            // 筛选周期任务: TaskReleaseTime < 当前时间
            sql = "SELECT tctc.ID, TaskName, TaskContent, TaskFileUrl, TaskType, TaskCycles, TaskGroupId, TaskReleaseTime, TaskValidDay, TaskGroupName, TaskGroupContent, tctg.UpdateTime  \n" +
                    "FROM TTFundCFH.dbo.Tb_CFHResearchTaskConfig tctc  \n" +
                    "JOIN TTFundCFH.dbo.Tb_CFHResearchTaskGroup tctg ON tctc.TaskGroupId = tctg.ID  \n" +
                    "WHERE tctc.IsDel = 0 AND tctg.IsDel = 0 AND tctc.TaskCycles != 0 AND tctc.TaskReleaseTime < ?\n" +
                    "ORDER BY tctc.TaskGroupId;";
        }
        param.add(nowDate);
        // 执行sql
        List<Map> maps = app.getSqlServer().executeQuery(sql, param);
        if (!maps.isEmpty()) {
            // 此处ID等个别重复字段映射结果会有偏差
            taskConfigList = JSON.parseArray(JSON.toJSONString(maps), ResearchTaskConfigDto.class);
            taskGroupList = JSON.parseArray(JSON.toJSONString(maps), ResearchTaskGroupDto.class);
        }
        // 若taskConfigList和taskGroupList大小不一致，说明存在一个taskConfig没有组，报错处理
        if (taskConfigList.size() == taskGroupList.size()) {
            for (int i = 0; i < taskConfigList.size(); i++) {
                ResearchTaskConfigDto config = taskConfigList.get(i);
                ResearchTaskGroupDto group = taskGroupList.get(i);
                result.put(config, group);
            }
        } else {
            logger.error("周期任务或一次性任务不存在有效的财富号研究组信息");
        }
        return result;
    }
    @Override
    public Map<ResearchTaskConfigDto, ResearchTaskGroupDto> selectVIPByTaskCycles(boolean isOnceTask, Date breakpointTime, Date nowDate) {
        Map<ResearchTaskConfigDto, ResearchTaskGroupDto> result = new HashMap<>();
        List<ResearchTaskConfigDto> taskConfigList = new ArrayList<>();
        List<ResearchTaskGroupDto> taskGroupList = new ArrayList<>();
        String sql;
        List<Object> param = new ArrayList<>();
        if (isOnceTask) {
            // 筛选一次性任务: 上次时间断点<TaskReleaseTime < 当前时间
            sql = "SELECT tctc.ID, TaskName, TaskContent, TaskFileUrl, TaskType, TaskCycles, TaskGroupId, TaskReleaseTime, TaskValidDay, TaskGroupName, TaskGroupContent, tctg.UpdateTime\n" +
                    "FROM TTFundCFH.dbo.Tb_VIPTaskConfig tctc  \n" +
                    "JOIN TTFundCFH.dbo.Tb_VIPTaskGroup tctg ON tctc.TaskGroupId = tctg.ID  \n" +
                    "WHERE tctc.IsDel = 0 AND tctg.IsDel = 0 AND tctc.TaskCycles = 0 AND tctc.TaskReleaseTime > ? AND tctc.TaskReleaseTime < ? \n" +
                    "ORDER BY tctc.TaskGroupId;";
            param.add(breakpointTime);
        } else {
            // 筛选周期任务: TaskReleaseTime < 当前时间
            sql = "SELECT tctc.ID, TaskName, TaskContent, TaskFileUrl, TaskType, TaskCycles, TaskGroupId, TaskReleaseTime, TaskValidDay, TaskGroupName, TaskGroupContent, tctg.UpdateTime  \n" +
                    "FROM TTFundCFH.dbo.Tb_VIPTaskConfig tctc  \n" +
                    "JOIN TTFundCFH.dbo.Tb_VIPTaskGroup tctg ON tctc.TaskGroupId = tctg.ID  \n" +
                    "WHERE tctc.IsDel = 0 AND tctg.IsDel = 0 AND tctc.TaskCycles != 0 AND tctc.TaskReleaseTime < ?\n" +
                    "ORDER BY tctc.TaskGroupId;";
        }
        param.add(nowDate);
        // 执行sql
        List<Map> maps = app.getSqlServer().executeQuery(sql, param);
        if (!maps.isEmpty()) {
            // 此处ID等个别重复字段映射结果会有偏差
            taskConfigList = JSON.parseArray(JSON.toJSONString(maps), ResearchTaskConfigDto.class);
            taskGroupList = JSON.parseArray(JSON.toJSONString(maps), ResearchTaskGroupDto.class);
        }
        // 若taskConfigList和taskGroupList大小不一致，说明存在一个taskConfig没有组，报错处理
        if (taskConfigList.size() == taskGroupList.size()) {
            for (int i = 0; i < taskConfigList.size(); i++) {
                ResearchTaskConfigDto config = taskConfigList.get(i);
                ResearchTaskGroupDto group = taskGroupList.get(i);
                result.put(config, group);
            }
        } else {
            logger.error("周期任务或一次性任务不存在有效的财富号研究组信息");
        }
        return result;
    }

    @Override
    public Boolean insertResearchTaskList(List<ResearchTaskListDto> list) {
        String sql = "INSERT INTO TTFundCFH.dbo.Tb_CFHResearchTaskList\n" +
                "(ID, CFHID, TaskConfigID, TaskCycleMark, TaskName, TaskContent, TaskFileUrl, TaskType, TaskBeginTime, TaskEndTime, TaskSource, AnswerFileUrl, Status, IsDel, UpdateTime, CreateTime)\n" +
                "VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);";
        List<List<Object>> paramsList = new ArrayList<>();
        for (ResearchTaskListDto model : list) {
            List<Object> params = new ArrayList<>();
            params.add(model.getID());
            params.add(model.getCFHID());
            params.add(model.getTaskConfigID());
            params.add(model.getTaskCycleMark());
            params.add(model.getTaskName());
            params.add(model.getTaskContent());
            params.add(model.getTaskFileUrl());
            params.add(model.getTaskType());
            params.add(model.getTaskBeginTime());
            params.add(model.getTaskEndTime());
            params.add(model.getTaskSource());
            params.add(model.getAnswerFileUrl());
            params.add(model.getStatus());
            params.add(model.getIsDel());
            params.add(model.getUpdateTime());
            params.add(model.getCreateTime());
            paramsList.add(params);
        }
        DBUtil.insertSqlAndParams(sql,paramsList);
        return true;
    }
    @Override
    public Boolean insertVIPTaskList(List<ResearchTaskListDto> list) {
        String sql = "INSERT INTO TTFundCFH.dbo.Tb_VIPTaskList\n" +
                "(ID, CFHID, TaskConfigID, TaskCycleMark, TaskName, TaskContent, TaskFileUrl, TaskType, TaskBeginTime, TaskEndTime, TaskSource, AnswerFileUrl, Status, IsDel, UpdateTime, CreateTime)\n" +
                "VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);";
        List<List<Object>> paramsList = new ArrayList<>();
        for (ResearchTaskListDto model : list) {
            List<Object> params = new ArrayList<>();
            params.add(model.getID());
            params.add(model.getCFHID());
            params.add(model.getTaskConfigID());
            params.add(model.getTaskCycleMark());
            params.add(model.getTaskName());
            params.add(model.getTaskContent());
            params.add(model.getTaskFileUrl());
            params.add(model.getTaskType());
            params.add(model.getTaskBeginTime());
            params.add(model.getTaskEndTime());
            params.add(model.getTaskSource());
            params.add(model.getAnswerFileUrl());
            params.add(model.getStatus());
            params.add(model.getIsDel());
            params.add(model.getUpdateTime());
            params.add(model.getCreateTime());
            paramsList.add(params);
        }
        DBUtil.insertSqlAndParams(sql,paramsList);
        return true;
    }

    @Override
    public List<ResearchTaskSubjectDto> selectConfigSubject(List<String> periodTaskConfigIds) {
        String sql = "SELECT ID, TaskConfigID, TaskCycleMark, SubjectTitle, SubjectCode, SubjectType, SubjectImportant, IsDel, Operator, UpdateTime, CreateTime,extend,sort " +
                "FROM TTFundCFH.dbo.Tb_CFHResearchTaskSubject " +
                "WHERE IsDel = 0 AND TaskCycleMark IS NULL AND TaskConfigID IN (" +
                String.join(",", Collections.nCopies(periodTaskConfigIds.size(), "?")) + ") order by sort, UpdateTime desc";
        List<ResearchTaskSubjectDto> result = new ArrayList<>();
        List<Map> maps = app.getSqlServer().executeQuery(sql, new ArrayList<>(periodTaskConfigIds));
        if (!maps.isEmpty()) {
            result = JSON.parseArray(JSON.toJSONString(maps), ResearchTaskSubjectDto.class);
        }
        return result;
    }
    @Override
    public List<ResearchTaskSubjectDto> selectVIPConfigSubject(List<String> periodTaskConfigIds) {
        String sql = "SELECT ID, TaskConfigID, TaskCycleMark, SubjectTitle, SubjectCode, SubjectType, SubjectImportant, IsDel, Operator, UpdateTime, CreateTime,extend,sort,TaskSubjectType " +
                "FROM TTFundCFH.dbo.Tb_VIPTaskSubject " +
                "WHERE IsDel = 0 AND TaskCycleMark IS NULL AND TaskConfigID IN (" +
                String.join(",", Collections.nCopies(periodTaskConfigIds.size(), "?")) + ") order by sort, UpdateTime desc";
        List<ResearchTaskSubjectDto> result = new ArrayList<>();
        List<Map> maps = app.getSqlServer().executeQuery(sql, new ArrayList<>(periodTaskConfigIds));
        if (!maps.isEmpty()) {
            result = JSON.parseArray(JSON.toJSONString(maps), ResearchTaskSubjectDto.class);
        }
        return result;
    }

    @Override
    public boolean insertResearchSubject(List<ResearchTaskSubjectDto> data) {
        String sql = "INSERT INTO TTFundCFH.dbo.Tb_CFHResearchTaskSubject\n" +
                "(ID, TaskConfigID, TaskCycleMark, SubjectTitle, SubjectCode, SubjectType, SubjectImportant, IsDel, Operator, UpdateTime, CreateTime,extend,sort)\n" +
                "VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?);";
        List<List<Object>> paramsList = new ArrayList<>();
        for (ResearchTaskSubjectDto model : data) {
            List<Object> params = new ArrayList<>();
            params.add(model.getID());
            params.add(model.getTaskConfigID());
            params.add(model.getTaskCycleMark());
            params.add(model.getSubjectTitle());
            params.add(model.getSubjectCode());
            params.add(model.getSubjectType());
            params.add(model.getSubjectImportant());
            params.add(model.getIsDel());
            params.add(model.getOperator());
            params.add(model.getUpdateTime());
            params.add(model.getCreateTime());
            params.add(model.getExtend());
            params.add(model.getSort());
            paramsList.add(params);
        }
        DBUtil.insertSqlAndParams(sql,paramsList);
        return true;
    }
    @Override
    public boolean insertVIPSubject(List<ResearchTaskSubjectDto> data) {
        String sql = "INSERT INTO TTFundCFH.dbo.Tb_VIPTaskSubject\n" +
                "(ID, TaskConfigID, TaskCycleMark, SubjectTitle, SubjectCode, SubjectType, SubjectImportant, IsDel, Operator, UpdateTime, CreateTime,extend,sort,TaskSubjectType)\n" +
                "VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?);";
        List<List<Object>> paramsList = new ArrayList<>();
        for (ResearchTaskSubjectDto model : data) {
            List<Object> params = new ArrayList<>();
            params.add(model.getID());
            params.add(model.getTaskConfigID());
            params.add(model.getTaskCycleMark());
            params.add(model.getSubjectTitle());
            params.add(model.getSubjectCode());
            params.add(model.getSubjectType());
            params.add(model.getSubjectImportant());
            params.add(model.getIsDel());
            params.add(model.getOperator());
            params.add(model.getUpdateTime());
            params.add(model.getCreateTime());
            params.add(model.getExtend());
            params.add(model.getSort());
            params.add(model.getTaskSubjectType());
            paramsList.add(params);
        }
        DBUtil.insertSqlAndParams(sql,paramsList);
        return true;
    }

    @Override
    public boolean updatePeriodTaskReleaseTime(Map<String, Date> periodTaskReleaseTimeMap) {
        String sql = "UPDATE TTFundCFH.dbo.Tb_CFHResearchTaskConfig SET TaskReleaseTime=? WHERE ID=?;";
        List<List<Object>> paramsList = new ArrayList<>();
        for (Map.Entry<String, Date> entry : periodTaskReleaseTimeMap.entrySet()) {
            List<Object> params = new ArrayList<>();
            params.add(entry.getValue());
            params.add(entry.getKey());
            paramsList.add(params);
        }
        DBUtil.insertSqlAndParams(sql,paramsList);
        return true;
    }
    @Override
    public boolean updatePeriodVIPTaskReleaseTime(Map<String, Date> periodTaskReleaseTimeMap) {
        String sql = "UPDATE TTFundCFH.dbo.Tb_VIPTaskConfig SET TaskReleaseTime=? WHERE ID=?;";
        List<List<Object>> paramsList = new ArrayList<>();
        for (Map.Entry<String, Date> entry : periodTaskReleaseTimeMap.entrySet()) {
            List<Object> params = new ArrayList<>();
            params.add(entry.getValue());
            params.add(entry.getKey());
            paramsList.add(params);
        }
        DBUtil.insertSqlAndParams(sql,paramsList);
        return true;
    }

    @Override
    public boolean insertNoticeList(List<TaskNoticeDto> taskNoticeList) {
        String sql = "INSERT INTO TTFundCFH.dbo.Tb_Notice\n" +
                "(ID, Title,Content,EnumTypeID,NoticeObject,Status, UpDateTime, CreatTime,CFHID,MgrName,ReadCFH)\n" +
                "VALUES(?,?,?,?,?,?,?,?,?,?,?);";
        List<List<Object>> paramsList = new ArrayList<>();
        for (TaskNoticeDto model : taskNoticeList) {
            List<Object> params = new ArrayList<>();
            params.add(model.getId());
            params.add(model.getTitle());
            params.add(model.getContent());
            params.add(model.getType());
            params.add(model.getNoticeObj());
            params.add(model.getStatus());
            params.add(model.getUpdateTime());
            params.add(model.getCreateTime());
            params.add(model.getCfhId());
            params.add(model.getMgrName());
            params.add(model.getReadCfh());
            paramsList.add(params);
        }
        DBUtil.insertSqlAndParams(sql,paramsList);
        return true;
    }

    @Override
    public List<TaskNoticeUserDto> getNoticeUser(String cfhId) {
        String sql = "select * from Tb_NoticeUser where CFHID = ? and IsDelete = 0";
        List<Object> param = new LinkedList<>();
        param.add(cfhId);
        List<Map> maps = app.getSqlServer().executeQuery(sql, param);
        if (!maps.isEmpty()) {
            return JSON.parseArray(JSON.toJSONString(maps), TaskNoticeUserDto.class);
        }
        return null;
    }

    @Override
    @AopCache(cachetype = 2, cache2expire = 1000 * 60 * 60, fieldorder = {0})
    public TaskNoticeConfigDto getNoticeConfig(int noticeType) {
        String sql = "select * from Tb_NoticeConfig where NoticeType = ? and IsDelete = 0";
        List<Object> param = new LinkedList<>();
        param.add(noticeType);
        List<Map> maps = app.getSqlServer().executeQuery(sql, param);
        if (!maps.isEmpty()) {
            return JSON.parseObject(JSON.toJSONString(maps.get(0)), TaskNoticeConfigDto.class);
        }
        return null;
    }

    @Override
    public List<CFHWhiteSheetDTO> selectAllCFHRealCompany() {
        String sql = "select * from Tb_CFHWhiteSheet where IsDelete = 0 and RealCompanyCode is not null";
        List<CFHWhiteSheetDTO> result = new ArrayList<>();
        List<Map> maps = app.getSqlServer().executeQuery(sql, null);
        if (!maps.isEmpty()) {
            result = JSON.parseArray(JSON.toJSONString(maps), CFHWhiteSheetDTO.class);
        }
        return result;

    }

    @Override
    public List<CFHInfoDto> selectAllCFH() {
        List<CFHInfoDto> result = new ArrayList<>();
        String sql = "select * from TTFundCFH.dbo.Tb_CFHBaseInfo " +
                "where Status =10 or Status = 1";
        List<Map> maps = app.getSqlServer().executeQuery(sql, null);
        if (!maps.isEmpty()) {
            result = JSON.parseArray(JSON.toJSONString(maps), CFHInfoDto.class);
        }
        return result;
    }

    @Override
    public CFHBaseInfoDto selectByUID(String emuid) {
        CFHBaseInfoDto cfhBaseInfoDO = null;
        String sql = "select * from Tb_CFHBaseInfo where (Status = 1 or Status = 10) and RelatedUid = ?";
        List<Map> maps = app.getSqlServer().executeQuery(sql, Arrays.asList(emuid));
        if (!maps.isEmpty()) {
            cfhBaseInfoDO = JSON.parseObject(JSON.toJSONString(maps.get(0)), CFHBaseInfoDto.class);
        }
        return cfhBaseInfoDO;
    }

    @Override
    public List<FundCode> selectFundByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return new ArrayList<>();
        }
        List<Object> params = new ArrayList<>();
        StrBuilder sql = new StrBuilder("select * from Tb_CFHFundCode where EISDEL = 0 AND (TSRQ IS NULL OR TSRQ > GETDATE()) and FCODE in (");
        for (int i = 0; i < codes.size(); i++) {
            params.add(codes.get(i));
            if (i < codes.size() - 1) {
                sql.append("?,");
            }else {
                sql.append("?)");
            }
        }
        List<Map> maps = app.getSqlServer().executeQuery(sql.toString(), params);
        return JSON.parseArray(JSON.toJSONString(maps), FundCode.class);
    }

    @Override
    public boolean insertCFHUser(CFHInfoDto cfhInfoDto) {
        List<Object> params = new ArrayList<>();
        String sql = "INSERT INTO TTFundCFH.dbo.Tb_CFHUser (id, create_time, update_time, CFHID, uid)\n" +
                "VALUES (?, ?, ?, ?, ?);";
        params.add(cfhInfoDto.getCFHID() + "_" + "admin");
        params.add(TimeUtil.getNowDate());
        params.add(TimeUtil.getNowDate());
        params.add(cfhInfoDto.getCFHID());
        params.add(cfhInfoDto.getRelatedUid());
        boolean execute = app.getSqlServer().execute(sql, params);
        return execute;
    }

    @Override
    public CFHInfoDto getCfhUserById(String id) {
        CFHInfoDto result = null;
        String sql = "SELECT * from Tb_CFHUser where id = ?;";
        List<Map> maps = app.getSqlServer().executeQuery(sql, Arrays.asList(id));
        if (!maps.isEmpty()) {
            result = JSON.parseObject(JSON.toJSONString(maps.get(0)), CFHInfoDto.class);
        }
        return result;
    }

    @Override
    public boolean updateCfhUser(CFHInfoDto cfhInfoDto) {
        List<Object> params = new ArrayList<>();
        String sql = "UPDATE Tb_CFHUser" +
                " set update_time=?, CFHID=?, uid=?" +
                " WHERE id=?;";
        params.add(TimeUtil.getNowDate());
        params.add(cfhInfoDto.getCFHID());
        params.add(cfhInfoDto.getRelatedUid());
        params.add(cfhInfoDto.getID() + "_" + "admin");
        return app.getSqlServer().execute(sql, params);
    }

    @Override
    public boolean insertNotice(TaskNoticeDto model) {
        String sql = "INSERT INTO TTFundCFH.dbo.Tb_Notice\n" +
                "(ID, Title,Content,EnumTypeID,NoticeObject,Status, UpDateTime, CreatTime,CFHID,MgrName,ReadCFH)\n" +
                "VALUES(?,?,?,?,?,?,?,?,?,?,?);";
        List<Object> params = new ArrayList<>();
        params.add(model.getId());
        params.add(model.getTitle());
        params.add(model.getContent());
        params.add(model.getType());
        params.add(model.getNoticeObj());
        params.add(model.getStatus());
        params.add(model.getUpdateTime());
        params.add(model.getCreateTime());
        params.add(model.getCfhId());
        params.add(model.getMgrName());
        params.add(model.getReadCfh());
        boolean result = app.getSqlServer().execute(sql, params);
        return result;
    }

    @Override
    public List<CFHUser> getCfhUserByCFHId(String cfhId) {
        String sql = "select * from Tb_CFHUser where CFHID = ? and is_delete = 0";
        List<Map> maps = app.getSqlServer().executeQuery(sql, Arrays.asList(cfhId));
        if (CollectionUtils.isEmpty(maps)) {
            return new ArrayList<>();
        }
        return JSON.parseArray(JSON.toJSONString(maps), CFHUser.class);

    }


}
