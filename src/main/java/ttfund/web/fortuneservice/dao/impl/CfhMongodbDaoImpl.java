package ttfund.web.fortuneservice.dao.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mongodb.BasicDBObject;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import com.ttfund.web.base.helper.CommonHelper;
import com.ttfund.web.base.helper.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.BasicBSONObject;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.config.App;
import ttfund.web.fortuneservice.constant.CFHBaseInfoStatusEnum;
import ttfund.web.fortuneservice.constant.CFHMongodbConstant;
import ttfund.web.fortuneservice.constant.CFHMongodbConstant.*;
import ttfund.web.fortuneservice.constant.HqMongodbConstant.HqCommonField;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.model.bo.CFHUpdateInfoBo;
import ttfund.web.fortuneservice.model.dto.*;
import ttfund.web.fortuneservice.utils.CommonUtil;
import ttfund.web.fortuneservice.utils.TimeUtil;
import ttfund.web.fortuneservice.utils.TableColumn;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import java.lang.reflect.Field;

/**
 * 财富号 mongodb交互
 *
 * <AUTHOR>
 * @version 1.0.0
 * @className CfhMongodbDaoImpl
 * @date 2023/4/7 14:57
 */
@Repository
@Slf4j
public class CfhMongodbDaoImpl implements CfhMongodbDao {
    public static final int BATCH_SIZE = 1000;

    @Resource
    private App app;

    @Override
    public List<QuickReviewDto> getQuickReview(long timePointBegin, long timePointEnd, int pageSize, Integer labelType) {

        LinkedList<BasicDBObject> arrayList = new LinkedList<>();
        arrayList.add(new BasicDBObject(QuickReviewField.TIME_POINT, new BasicDBObject("$lt", timePointEnd)));
        arrayList.add(new BasicDBObject(QuickReviewField.TIME_POINT, new BasicDBObject("$gt", timePointBegin)));
        arrayList.add(new BasicDBObject(QuickReviewField.IS_DEL, 0));
        arrayList.add(new BasicDBObject(QuickReviewField.STATUS, 1));

        LinkedList<BasicDBObject> orArr = new LinkedList<>();
        if (labelType != null) {
            orArr.add(new BasicDBObject(QuickReviewField.LABEL_TYPE, labelType));
            orArr.add(new BasicDBObject(QuickReviewField.LABEL_TYPE, null));
        }
        BasicDBObject query = new BasicDBObject();
        query.put("$and", arrayList);
        if (!orArr.isEmpty()) {
            query.put("$or", orArr);
        }
        BasicDBObject field =
                new BasicDBObject(CommonHelper.fieldDic(QuickReviewDto.class));

        BasicDBObject sort = new BasicDBObject(QuickReviewField.TIME_POINT, -1);

        return app.getCfhMongodbRead().query(
                CFHMongodbConstant.DB_TT_FUND_CFH,
                CFHMongodbConstant.TB_CFH_QUICK_REVIEW,
                query,
                field,
                sort,
                pageSize,
                QuickReviewDto.class);
    }

    @Override
    public List<CfhListDto> getCFHWeight(List<String> cfhIds) {
        if (cfhIds == null || cfhIds.isEmpty()) {
            return new ArrayList<>();
        }
        BasicDBObject[] array = {
                new BasicDBObject(CfhListField.CFH_ID, new BasicDBObject("$in", cfhIds))// 有效的
        };
        List<BasicDBObject> arrayList = new LinkedList<>();
        arrayList.add(new BasicDBObject(CfhListField.STATUS, 1));
        arrayList.add(new BasicDBObject(CfhListField.STATUS, 10));

        BasicDBObject query = new BasicDBObject();
        query.put("$and", array);
        query.put("$or", arrayList);
        BasicDBObject field =
                new BasicDBObject(CommonHelper.fieldDic(CfhListDto.class));

        return app.getCfhMongodbRead().query(
                CFHMongodbConstant.DB_TT_FUND_CFH,
                CFHMongodbConstant.TB_CFH_LIST,
                query,
                field,
                null,
                0,
                CfhListDto.class);
    }

    @Override
    public List<FundLabelDto> getQuickReviewTheme() {
        BasicDBObject[] array = {
                new BasicDBObject(FundLabelField.STATUS, 0)// 有效的
        };
        BasicDBObject query = new BasicDBObject();
        query.put("$and", array);
        BasicDBObject field =
                new BasicDBObject(CommonHelper.fieldDic(FundLabelDto.class));

        return app.getCfhMongodbRead().query(
                CFHMongodbConstant.DB_TT_FUND_CFH,
                CFHMongodbConstant.TB_CFH_FUND_THEME,
                query,
                field,
                null,
                0,
                FundLabelDto.class);
    }

    @Override
    @Retryable(value = Exception.class, maxAttempts = 3)
    public boolean upsertCFHGeneralData(String key, Object model) {
        List<WriteModel<Document>> writeModels = new ArrayList<>();
        List<Map> maps = JSON.parseArray(JSON.toJSONString(model), Map.class);
        Date now = new Date();
        Document upsertByData = new Document();
        upsertByData.put(CFHMongodbConstant.RESULT, maps);
        upsertByData.put(CFHGeneralDataField.UPDATE_TIME, now);
        Document upset = new Document();

        Document setOn = new Document();
        setOn.put(CFHGeneralDataField.CREATE_TIME, now);
        setOn.put(CFHGeneralDataField.ID, key);
        upset.put("$set", upsertByData);
        upset.put("$setOnInsert", setOn);

        BasicDBObject query = new BasicDBObject(CFHGeneralDataField.ID, key);

        UpdateManyModel<Document> pair = new UpdateManyModel<>(query, upset, new UpdateOptions().upsert(true));
        writeModels.add(pair);
        BulkWriteOptions options = new BulkWriteOptions().ordered(false);
        BulkWriteResult res = app.getCfhMongodbWrite().getMongoserver().getDatabase(CFHMongodbConstant.DB_TT_FUND_CFH)
                .getCollection(CFHMongodbConstant.TB_CFH_GENERAL_DATA).bulkWrite(writeModels, options);
        // getModifiedCount仅为修改，res.getUpserts()里记录了新增的记录
        if (res.getMatchedCount() <= 0 && res.getModifiedCount() <= 0 && (res.getUpserts().isEmpty())) {
            throw new RuntimeException("upsertCFHGeneralData写库失败");
        }
        return res.getModifiedCount() > 0 || (!res.getUpserts().isEmpty());
    }

    @Override
    public int deleteByCondition(List<String> noticeEIDs, Date nowDate) {
        BasicDBObject query = new BasicDBObject()
                .append("$or", Arrays.asList(
                        new BasicDBObject(HqCommonField.EID, new BasicDBObject("$nin", noticeEIDs)),
                        new BasicDBObject(HqCommonField.BEGIN_TIME, new BasicDBObject("$gt", nowDate)),
                        new BasicDBObject(HqCommonField.END_TIME, new BasicDBObject("$lte", nowDate))
                ));
        DeleteResult deleteResult = app.getCfhMongodbWrite().getMongoserver()
                .getDatabase(CFHMongodbConstant.DB_TT_FUND_CFH)
                .getCollection(CFHMongodbConstant.TB_CFH_PRODUCT_READ_HQ)
                .deleteMany(query);
        return (int) deleteResult.getDeletedCount();
    }

    @Override
    public int saveOrUpdateCFHList(List<CFHInfoDto> cfhLabelList) {
        int result = 0;
        if (cfhLabelList == null || cfhLabelList.isEmpty()) {
            return result;
        }
        List<WriteModel<Document>> writeModels = new ArrayList<>();
        for (CFHInfoDto item : cfhLabelList) {
            Document upsetByUpdate = new Document();
            //处理标签
            List<Document> labelList = new ArrayList<>();
            for (CFHLabelMongoDto cfhLabelMongoDO : item.getLabelInfoList()) {
                Document label = new Document();
                label.put("Title", cfhLabelMongoDO.getTitle());
                label.put("UrlType", cfhLabelMongoDO.getUrlType());
                label.put("LinkUrl", cfhLabelMongoDO.getLinkUrl());
                label.put("StartTime", cfhLabelMongoDO.getStartTime());
                label.put("EndTime", cfhLabelMongoDO.getEndTime());
                label.put("ShowPosition", cfhLabelMongoDO.getShowPosition());
                labelList.add(label);
            }
            upsetByUpdate.put("_id", item.getID());
            upsetByUpdate.put("CFHID", item.getCFHID());
            upsetByUpdate.put("CFHName", item.getCFHName());
            upsetByUpdate.put("CommpanyCode", item.getCommpanyCode());
            upsetByUpdate.put("CommpanyName", item.getCommpanyName());
            upsetByUpdate.put("DefaultWeight", item.getDefaultWeight());
            upsetByUpdate.put("HeaderImgPath", item.getHeaderImgPath());
            upsetByUpdate.put("JianPin", item.getJianPin());
            upsetByUpdate.put("LabelInfoList", labelList);
            upsetByUpdate.put("LiveBlackState", item.getBlackState());
            upsetByUpdate.put("OrganizationType", item.getOrganizationType());
            upsetByUpdate.put("Purview", item.getPurview());
            upsetByUpdate.put("PushCategoryCode", item.getPushCategoryCode());
            upsetByUpdate.put("PushPurview", item.getPushPurview());
            upsetByUpdate.put("RelatedUid", item.getRelatedUid());
            upsetByUpdate.put("RoleId", item.getRoleId());
            upsetByUpdate.put("Slogans", item.getSlogans());
            upsetByUpdate.put("Status", item.getStatus());
            upsetByUpdate.put("Summary", item.getSummary());
            upsetByUpdate.put("UpdateTime", item.getUpDataTime());
            upsetByUpdate.put("WeightStartTimeStamp", DateHelper.dateToUnixTimeMillisecond(item.getWeightStartTime()));
            upsetByUpdate.put("WeightEndTimeStamp", DateHelper.dateToUnixTimeMillisecond(item.getWeightEndTime()));

            Document upset = new Document("$set", upsetByUpdate);

            BasicDBObject query = new BasicDBObject("_id", item.getID());

            UpdateOneModel<Document> pair = new UpdateOneModel<>(query, upset, new UpdateOptions().upsert(true));
            writeModels.add(pair);
        }

        BulkWriteOptions options = new BulkWriteOptions().ordered(false);
        BulkWriteResult temp2 = app.getCfhMongodbWrite().getMongoserver().getDatabase(CFHMongodbConstant.DB_TT_FUND_CFH)
                .getCollection(CFHMongodbConstant.TB_CFH_LIST).bulkWrite(writeModels, options);
        result = temp2.getInsertedCount() + temp2.getMatchedCount() + temp2.getModifiedCount() + temp2.getUpserts().size();
        return result;
    }

    @Override
    public String getCFHGeneralData(String seqId, String defaultValue) {
        String result = null;
        if (StringUtils.isEmpty(seqId) || StringUtils.isEmpty(defaultValue)) {
            return result;
        }
        BasicDBObject query = new BasicDBObject("_id", seqId);
        HashMap<String, Object> map = new HashMap<>();
        map.put(CFHMongodbConstant.RESULT, 1);
        map.put("_id", 0);
        BasicDBObject field = new BasicDBObject(map);

        List<Document> data = app.getCfhMongodbRead().query(CFHMongodbConstant.DB_TT_FUND_CFH, CFHMongodbConstant.TB_CFH_GENERAL_DATA, query, field, null, Document.class);
        if (data == null || CollectionUtils.isEmpty(data)) {
            result = defaultValue;
        } else {
            result = JSON.toJSONString(data.get(0).get(CFHMongodbConstant.RESULT));
        }
        return result;
    }

    @Override
    public CFHArticleDto getCFHArticleByAuthor(String cfhid, Integer cfhStatus) {
        CFHArticleDto result = null;
        if (StringUtils.isEmpty(cfhid)) {
            return result;
        }
        BasicDBObject query = new BasicDBObject("AuthorId", cfhid);
        query.append("IsCfhArticle", !cfhStatus.equals(CFHBaseInfoStatusEnum.ADUIT.getCode()));
        BasicDBObject field = new BasicDBObject(CommonHelper.fieldDic(CFHArticleDto.class));
        List<CFHArticleDto> data = app.getCfhMongodbRead().query(CFHMongodbConstant.DB_TT_FUND_CFH, CFHMongodbConstant.TB_CFH_ARTICLE, query, field, null, 1, CFHArticleDto.class);
        if (data != null && !data.isEmpty()) {
            result = data.get(0);
        }
        return result;
    }

    @Override
    public boolean updateCFHArticle(String cfhid, Integer cfhStatus) {
        boolean result = false;
        if (StringUtils.isEmpty(cfhid) || cfhStatus == null) {
            return result;
        }
        BasicDBObject query = new BasicDBObject("AuthorId", cfhid);
        BasicDBObject upsetParam = new BasicDBObject();
        upsetParam.put("IsCfhArticle", cfhStatus.equals(1));
        BasicDBObject upset = new BasicDBObject();
        upset.put("$set", upsetParam);

        UpdateResult res = app.getCfhMongodbWrite().getMongoserver().getDatabase(CFHMongodbConstant.DB_TT_FUND_CFH)
                .getCollection(CFHMongodbConstant.TB_CFH_ARTICLE)
                .updateMany(query, upset);
        result = res.wasAcknowledged();
        return result;
    }

    @Override
    public List<QuickReviewDto> getQuickReviewByActivity(String activityId) {
        List<BasicDBObject> arrayList = new LinkedList<>();
        arrayList.add(new BasicDBObject("IsDel", 0));
        arrayList.add(new BasicDBObject("Status", 1));
        arrayList.add(new BasicDBObject("ActivityId", activityId));

        BasicDBObject query = new BasicDBObject();
        query.put("$and", arrayList);
        BasicDBObject field =
                new BasicDBObject(CommonHelper.fieldDic(QuickReviewDto.class));
        BasicDBObject sort = new BasicDBObject("timepoint", -1);
        List<QuickReviewDto> reviewList = app.getCfhMongodbRead().query(
                CFHMongodbConstant.DB_TT_FUND_CFH,
                CFHMongodbConstant.TB_CFH_QUICK_REVIEW,
                query,
                field,
                sort,
                0,
                QuickReviewDto.class);
        return reviewList;

    }

    @Override
    public int batchSaveRegularReportContentList(List<RegularInspectionReportContentDto> reportContentDtoList) {
        int result = 0;
        if (reportContentDtoList == null || reportContentDtoList.isEmpty()) {
            return result;
        }
        List<WriteModel<Document>> writeModels = new ArrayList<>();
        reportContentDtoList.forEach(item -> {
            Document doc = new Document();
            doc.put(RegularInspectionReportContentField.REPORT_CONTENT_ID, item.getReportContentId());
            doc.put(RegularInspectionReportContentField.REPORT_ID, item.getReportId());
            doc.put(RegularInspectionReportContentField.PRODUCT_CODE, item.getProductCode());
            doc.put(RegularInspectionReportContentField.PRODUCT_NAME, item.getProductName());
            doc.put(RegularInspectionReportContentField.FUND_SIZE, item.getFundSize() != null ?
                    BigDecimal.valueOf(item.getFundSize()).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() : null);
            doc.put(RegularInspectionReportContentField.MISSING_INFORMATION_TYPE, item.getMissingInformationType());
            doc.put(RegularInspectionReportContentField.CREATE_TIME, item.getCreateTime());
            doc.put(RegularInspectionReportContentField.IS_CLOSE, item.getIsClose());
            doc.put(RegularInspectionReportContentField.IS_HOLD, item.getIsHold());
            doc.put(RegularInspectionReportContentField.STABILSH_DATE, item.getStabilshDate());
            doc.put(RegularInspectionReportContentField.MANAGER, item.getManager());
            doc.put(RegularInspectionReportContentField.P_DATE, item.getPDate());
            writeModels.add(new InsertOneModel<>(doc));
        });

        BulkWriteOptions options = new BulkWriteOptions().ordered(false);
        BulkWriteResult bulkWrite = app.getCfhMongodbWrite().getMongoserver().getDatabase(CFHMongodbConstant.DB_TT_FUND_CFH)
                .getCollection(CFHMongodbConstant.REGULAR_REPORT_CONTENT).bulkWrite(writeModels, options);

        result = bulkWrite.getInsertedCount() == reportContentDtoList.size() ? reportContentDtoList.size() : 0;
        return result;
    }

    @Override
    public int batchSaveRegularReportList(List<RegularInspectionReportDto> reportDtoList) {
        int result = 0;
        if (reportDtoList == null || reportDtoList.isEmpty()) {
            return result;
        }
        List<WriteModel<Document>> writeModels = new ArrayList<>();
        reportDtoList.forEach(item -> {
            Document doc = new Document();
            doc.put(RegularInspectionReportField.REPORT_ID, item.getReportId());
            doc.put(RegularInspectionReportField.REPORT_NAME, item.getReportName());
            doc.put(RegularInspectionReportField.REPORT_TYPE, item.getReportType());
            doc.put(RegularInspectionReportField.CHECK_TIME, item.getCheckTime());
            doc.put(RegularInspectionReportField.UPDATE_TIME, DateHelper.getNowDate());
            writeModels.add(new InsertOneModel<>(doc));
        });

        BulkWriteOptions options = new BulkWriteOptions().ordered(false);
        BulkWriteResult bulkWrite = app.getCfhMongodbWrite().getMongoserver().getDatabase(CFHMongodbConstant.DB_TT_FUND_CFH)
                .getCollection(CFHMongodbConstant.REGULAR_REPORT).bulkWrite(writeModels, options);

        result = bulkWrite.getInsertedCount() == reportDtoList.size() ? reportDtoList.size() : 0;
        return result;
    }

    @Override
    public long getSpecialDayNumOfMonth(Date nowDate, Date referenceDate, boolean reversed) {
        BasicDBObject query = new BasicDBObject();
        LinkedList<BasicDBObject> arrayList = new LinkedList<>();
        if (!reversed) {
            //正序计算 当前日期排在该月第几个交易日
            arrayList.add(new BasicDBObject(CFHMongodbConstant.TradeDateField.DATE_TIME, new BasicDBObject("$gte", CommonUtil.getCurrentBelongDay(referenceDate))));
            arrayList.add(new BasicDBObject(CFHMongodbConstant.TradeDateField.DATE_TIME, new BasicDBObject("$lte", CommonUtil.getCurrentBelongDay(nowDate))));
        } else {
            //倒序计算，当前日期在该月倒数第几个交易日
            arrayList.add(new BasicDBObject(CFHMongodbConstant.TradeDateField.DATE_TIME, new BasicDBObject("$lte", CommonUtil.getCurrentBelongDay(referenceDate))));
            arrayList.add(new BasicDBObject(CFHMongodbConstant.TradeDateField.DATE_TIME, new BasicDBObject("$gte", CommonUtil.getCurrentBelongDay(nowDate))));
        }
        arrayList.add(new BasicDBObject(CFHMongodbConstant.TradeDateField.IS_TRADE_DATE, "1"));
        query.put("$and", arrayList);
        return app.getCfhMongodbRead().count(CFHMongodbConstant.DB_CFH_TRADE, CFHMongodbConstant.TB_TRADE_DATE, query);
    }

    @Override
    public Map<String, List<String>> getReportHistoryIdList() {
        Map<String, List<String>> result = new HashMap<>();
        BasicDBObject query = new BasicDBObject();
        query.put("IsShow", 1);  //IsShow：1显示，0隐藏（删除）
//        query.put("Status", 1);  //Status：1审核通过（已上架）0待审核(未上架) 需求优化中不需要这个条件了
        query.put("ProductType", 2);
        query.put("ReadType", 11);
        //查一年前至今的数据
        query.put("CreateTime", new BasicDBObject("$gte", DateUtils.addYears(new Date(), -1)));
        BasicDBObject fields = new BasicDBObject("_id", 1);
        fields.put("FundCode", 1);
        List<Document> documentList = app.getCfhMongodbRead().query(CFHMongodbConstant.DB_TT_FUND_CFH, CFHMongodbConstant.TB_PRODUCT_READ, query, fields, null, 0, Integer.MAX_VALUE, Integer.MAX_VALUE, Document.class);
        result = documentList.stream()
                .collect(Collectors.groupingBy(
                        document -> document.getString("FundCode"),
                        Collectors.mapping(
                                document -> StringUtils.upperCase(document.get("_id").toString()),
                                Collectors.toList()
                        )));
        return result;
    }

    @Override
    public String getLastTradeDay(Date date) {
        BasicDBObject query = new BasicDBObject();
        query.put(CFHMongodbConstant.TradeDateField.DATE_TIME, new BasicDBObject("$lte", CommonUtil.getCurrentBelongDay(date)));
        BasicDBObject sort = new BasicDBObject(CFHMongodbConstant.TradeDateField.DATE_TIME, -1);
        BasicDBObject field = new BasicDBObject(CFHMongodbConstant.TradeDateField.DATE_TIME, 1);

        List<Document> res = app.getCfhMongodbRead().query(
                CFHMongodbConstant.DB_CFH_TRADE,
                CFHMongodbConstant.TB_TRADE_DATE,
                query,
                field,
                sort,
                1,
                Document.class);
        return res.get(0).get("_id").toString();
    }

    @Override
    public List<DaysInfoMongoDto> getDaysInfoByTimeRange(Date startDate, Date endDate) {
        if (startDate == null || endDate == null || startDate.after(endDate)) {
            return Collections.emptyList();
        }
        BasicDBObject query = new BasicDBObject();
        LinkedList<BasicDBObject> arrayList = new LinkedList<>();
        arrayList.add(new BasicDBObject(CFHMongodbConstant.TradeDateField.DATE_TIME, new BasicDBObject("$gte", CommonUtil.getCurrentBelongDay(startDate))));
        arrayList.add(new BasicDBObject(CFHMongodbConstant.TradeDateField.DATE_TIME, new BasicDBObject("$lte", CommonUtil.getCurrentBelongDay(endDate))));
        query.put("$and", arrayList);
        BasicDBObject field = new BasicDBObject(CommonHelper.fieldDic(DaysInfoMongoDto.class));
        BasicDBObject sort = new BasicDBObject(CFHMongodbConstant.TradeDateField.DATE_TIME, 1);
        return app.getCfhMongodbRead().query(CFHMongodbConstant.DB_CFH_TRADE, CFHMongodbConstant.TB_TRADE_DATE, query, field, sort, DaysInfoMongoDto.class);
    }

    @Override
    public void updateCardWhite(String baseUrl, String giftid, int type) {
        BasicDBObject query = new BasicDBObject("giftId", giftid);
        query.put("type", type);
        query.put("referer", baseUrl);

        BasicDBObject upsetParam = new BasicDBObject();
        upsetParam.put("useTime", DateHelper.getNowDate());
        BasicDBObject upset = new BasicDBObject();
        upset.put("$set", upsetParam);

        UpdateResult updateResult = app.getCfhMongodbWrite().getMongoserver().getDatabase(CFHMongodbConstant.DB_TT_FUND_CFH)
                .getCollection(CFHMongodbConstant.TB_CFHCardWhitelist).updateOne(query, upset);
    }

    @Override
    public void insertGiftLog(String key, JSONObject request, JSONObject response, String giftid, Integer giftcount, String requestip, Long exectime, String userid, String url, String passportid, String referer, String time, String tableName) {
        if (StringUtils.isEmpty(key)) {
            key = UUID.randomUUID().toString().replaceAll("-", "");
        }
        tableName = StringUtils.isEmpty(tableName) ? CFHMongodbConstant.TB_CFHCardLog : tableName;
        BasicDBObject upsetbyinsert = new BasicDBObject();
        upsetbyinsert.append("_id", key);
        upsetbyinsert.append("request", request);
        upsetbyinsert.append("response", response);
        upsetbyinsert.append("giftId", giftid);
        upsetbyinsert.append("giftCount", giftcount);
        upsetbyinsert.append("requestip", requestip);
        upsetbyinsert.append("userid", userid);
        upsetbyinsert.append("passportid", passportid);
        upsetbyinsert.append("referer", referer);
        upsetbyinsert.append("url", url);
        upsetbyinsert.append("time", time);

        BasicDBObject upsetbyupdate = new BasicDBObject();
        upsetbyupdate.append("exectime", exectime);
        upsetbyupdate.append("updateTime", DateHelper.getNowDate());

        BasicDBObject upset = new BasicDBObject("$setOnInsert", upsetbyinsert);
        upset.put("$set", upsetbyupdate);

        BasicDBObject query = new BasicDBObject("_id", key);

        UpdateOptions options = new UpdateOptions().upsert(true);
        UpdateResult res = app.getCfhMongodbWrite().getMongoserver()
                .getDatabase(CFHMongodbConstant.DB_TT_FUND_CFH)
                .getCollection(tableName)
                .updateOne(query, upset, options);
    }

    @Override
    public List<String> getAllCFHIds() {
        // 过滤掉无效财富号
        BasicDBObject query = new BasicDBObject("Status", new BasicBSONObject("$ne", 0));
        BasicDBObject field = new BasicDBObject("CFHID", 1).append("_id", 0);
        List<Document> documents = app.getCfhMongodbRead().query(
                CFHMongodbConstant.DB_TT_FUND_CFH,
                CFHMongodbConstant.TB_CFH_LIST,
                query,
                field,
                null,
                Document.class);
        return documents.stream().map(doc -> (String) doc.get("CFHID")).collect(Collectors.toList());
    }

    @Override
    public int updateCFHInfos(List<CFHUpdateInfoBo> updateInfoList) {
        int result = 0;
        if (CollectionUtils.isEmpty(updateInfoList)) {
            return result;
        }

        List<WriteModel<Document>> writeModels = new ArrayList<>();
        for (CFHUpdateInfoBo item : updateInfoList) {
            BasicDBObject query = new BasicDBObject("CFHID", item.getCfhId());

            Document update = new Document();
            update.put("ArticleCount", item.getArticleCount());
            update.put("ReadCount", item.getReadCount());
            update.put("BigVip", item.getBigVip());
            update.put("SubscriberCount", item.getSubscriberCount());
            update.put("IsFund", item.isFund());
            update.put("UpdateTime", new Date());
            Document upset = new Document("$set", update);
            UpdateOneModel<Document> pair = new UpdateOneModel<>(query, upset, new UpdateOptions().upsert(true));
            writeModels.add(pair);
        }
        BulkWriteOptions options = new BulkWriteOptions().ordered(false);
        BulkWriteResult temp = app.getCfhMongodbWrite().getMongoserver().getDatabase(CFHMongodbConstant.DB_TT_FUND_CFH)
                .getCollection(CFHMongodbConstant.TB_CFH_LIST).bulkWrite(writeModels, options);
        return temp.getModifiedCount();
    }

    @Override
    public Date getBreakPoint(String seqId, String defaultValue) {
        Date preBreakPointDate = null;
        String preBreakPoint = getCFHGeneralData(seqId, defaultValue);
        try {
            JSONObject o = (JSONObject) JSON.parseArray(preBreakPoint).get(0);
            preBreakPointDate = DateHelper.stringToDate2(o.getString("time"), DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
            if (preBreakPointDate == null) {
                preBreakPointDate = DateHelper.stringToDate2(defaultValue, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
            }
        } catch (Exception e) {
            log.warn("财富号java服务，断点时间转换异常，【db.getSiblingDB(\"TTFundCFHDB\").getCollection(\"TB_CFHGeneralData\").find({\"_id\": {$eq: '" + seqId + "'}})】");
            preBreakPointDate = DateHelper.stringToDate2(defaultValue, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
        }
        return preBreakPointDate;
    }

    @Override
    public Date getMoudleCache(String id) {
        Date result = null;
        if (StringUtils.isEmpty(id)) {
            return result;
        }
        BasicDBObject query = new BasicDBObject("_id", id);
        HashMap<String, Object> map = new HashMap<>();
        map.put("Datas", 1);
        BasicDBObject field = new BasicDBObject(map);

        List<Document> data = app.getCfhMongodbRead().query(CFHMongodbConstant.DB_TT_FUND_CFH, CFHMongodbConstant.TB_MOUDLECACHE, query, null, null, Document.class);
        if (!CollectionUtils.isEmpty(data)) {
            try {
                String datas = data.get(0).getString("Datas");
                if (StringUtils.isNotEmpty(datas)) {
                    JSONObject object = JSONObject.parseObject(datas);
                    result = DateHelper.stringToDate2(object.getString("CheckTime"), DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
                }
            } catch (Exception e) {
                log.error("id:{},数据时间转换异常：{}", id, data);
            }
        }
        return result;
    }

    @Override
    public void updateTheme(String code, String name, int status) {
        BasicDBObject upsetbyinsert = new BasicDBObject();
        upsetbyinsert.append("_id", code);
        upsetbyinsert.append("IndexCode", code);

        BasicDBObject upsetbyupdate = new BasicDBObject();
        upsetbyupdate.append("IndexName", name);
        upsetbyupdate.append("Status", status);

        BasicDBObject upset = new BasicDBObject("$setOnInsert", upsetbyinsert);
        upset.put("$set", upsetbyupdate);

        BasicDBObject query = new BasicDBObject("_id", code);

        UpdateOptions options = new UpdateOptions().upsert(true);
        UpdateResult res = app.getCfhMongodbWrite().getMongoserver()
                .getDatabase(CFHMongodbConstant.DB_TT_FUND_CFH)
                .getCollection(CFHMongodbConstant.TB_CFH_FUND_THEME)
                .updateOne(query, upset, options);
    }

    /**
     * 季报专用，其他解读不要用！！！！！！！
     *
     * @param read
     * @return
     */
    @Override
    public int saveProductRead(CFHProductReadDto read) {
        int result = 0;
        if (read == null) {
            return result;
        }
        //新增
        Document upsetbyinsert = new Document();
        upsetbyinsert.put("_id", read.getID());
        upsetbyinsert.put("CFHID", read.getCFHID());
        upsetbyinsert.put("CreateTime", read.getCreateTime());
        upsetbyinsert.put("UpdateTime", read.getUpdateTime());

        //修改
        Document upsetbyupdate = new Document();
        upsetbyupdate.put("AppUseLinkUrl", read.getAppUseLinkUrl());
        upsetbyupdate.put("H5LinkUrl", read.getH5LinkUrl());
        upsetbyupdate.put("H5UseLinkUrl", read.getH5UseLinkUrl());
        upsetbyupdate.put("LinkCode", read.getLinkCode());
        upsetbyupdate.put("LinkType", read.getLinkType());
        upsetbyupdate.put("ProductType", read.getProductType());
        upsetbyupdate.put("ReadDecription", read.getReadDecription());
        upsetbyupdate.put("ReportType", read.getReportType());
        upsetbyupdate.put("ShowPosition", read.getShowPosition());
        upsetbyupdate.put("SpecialUserGroupID", read.getSpecialUserGroupID());
        upsetbyupdate.put("StartTime", read.getStartTime());
        upsetbyupdate.put("Status", read.getStatus());
        upsetbyupdate.put("IsShow", read.getIsShow());
        upsetbyupdate.put("JsonAudio", read.getJsonAudio());
        upsetbyupdate.put("WarnNum", read.getWarnNum());
        //处理JsonAudioModel
        Document jsonAudioModel = null;
        VedioAudioJson audioModel = read.getJsonAudioModel();
        if (audioModel != null) {
            jsonAudioModel = new Document();
            jsonAudioModel.put("ID", audioModel.getID());
            jsonAudioModel.put("Name", audioModel.getName());
            jsonAudioModel.put("Duration", audioModel.getDuration());
            jsonAudioModel.put("Size", audioModel.getSize());
            jsonAudioModel.put("Url", audioModel.getUrl());
            jsonAudioModel.put("ImgUrl", audioModel.getImgUrl());
            jsonAudioModel.put("AuthorId", audioModel.getAuthorId());
            jsonAudioModel.put("PointTime", audioModel.getPointTime());
            jsonAudioModel.put("StartTime", audioModel.getStartTime());
            jsonAudioModel.put("HeadAddress", audioModel.getHeadAddress());
            jsonAudioModel.put("RoomNumber", audioModel.getRoomNumber());
        }
        upsetbyupdate.put("JsonAudioModel", jsonAudioModel);
        upsetbyupdate.put("ReadTitle", read.getReadTitle());
        upsetbyupdate.put("ReadType", read.getReadType());
        upsetbyupdate.put("EndTime", read.getEndTime());
        upsetbyupdate.put("FundCode", read.getFundCode());
        upsetbyupdate.put("FundCodeArry", Arrays.asList(read.getFundCodeArry()));

        Document upset = new Document("$setOnInsert", upsetbyinsert);
        upset.put("$set", upsetbyupdate);

        //查询条件
        BasicDBObject query = new BasicDBObject("_id", read.getID());

        UpdateOptions options = new UpdateOptions().upsert(true);
        UpdateResult res = app.getCfhMongodbWrite().getMongoserver()
                .getDatabase(CFHMongodbConstant.DB_TT_FUND_CFH)
                .getCollection(CFHMongodbConstant.TB_CFH_PRODUCT_READ)
                .updateOne(query, upset, options);
        // 获取被修改和插入的文档数量
        int modifiedCount = (int) res.getModifiedCount();
        int insertedCount = res.getUpsertedId() != null ? 1 : 0;
        result = modifiedCount + insertedCount;
        return result;
    }

    @Override
    public int saveCommunityDataDay(List<CommunityDataDay> dataList) {
        int result = 0;
        if (CollectionUtils.isEmpty(dataList)) {
            return result;
        }
        List<WriteModel<Document>> writeModels = new ArrayList<>();
        for (CommunityDataDay item : dataList) {
            BasicDBObject upsetbyinsert = new BasicDBObject();
            upsetbyinsert.append("_id", item.get_id());
            upsetbyinsert.append("date", item.getDate());
            upsetbyinsert.append("CFHID", item.getCFHID());
            upsetbyinsert.append("companyId", item.getCompanyId());
            upsetbyinsert.append("CreateTime", DateHelper.getNowDate());

            BasicDBObject upsetbyupdate = new BasicDBObject();
            upsetbyupdate.append("UpdateTime", DateHelper.getNowDate());
            upsetbyupdate.append("visitorsNum", item.getVisitorsNum());
            upsetbyupdate.append("visitorsNumReal", item.getVisitorsNumReal());
            upsetbyupdate.append("userNum", item.getUserNum());
            upsetbyupdate.append("speakNum", item.getSpeakNum());
            upsetbyupdate.append("speakUserNum", item.getSpeakUserNum());
            upsetbyupdate.append("userInNum", item.getUserInNum());
            upsetbyupdate.append("userOutNum", item.getUserOutNum());
            upsetbyupdate.append("visitDaysPreMonth", item.getVisitDaysPreMonth());
            upsetbyupdate.append("payScore", item.getPayScore());
            upsetbyupdate.append("speakScore", item.getSpeakScore());
            upsetbyupdate.append("activeScore", item.getActiveScore());
            upsetbyupdate.append("comprehensiveScore", item.getComprehensiveScore());

            BasicDBObject upset = new BasicDBObject("$setOnInsert", upsetbyinsert);
            upset.put("$set", upsetbyupdate);
            BasicDBObject query = new BasicDBObject("_id", item.get_id());
            UpdateOneModel<Document> pair = new UpdateOneModel<>(query, upset, new UpdateOptions().upsert(true));
            writeModels.add(pair);
        }
        BulkWriteOptions options = new BulkWriteOptions().ordered(false);
        BulkWriteResult temp2 = app.getCfhMongodbWrite().getMongoserver().getDatabase(CFHMongodbConstant.DB_TT_FUND_CFH)
                .getCollection(CFHMongodbConstant.TB_COMMUNITY_DATA_DAY).bulkWrite(writeModels, options);
        result = temp2.getInsertedCount() + temp2.getMatchedCount() + temp2.getModifiedCount() + temp2.getUpserts().size();
        return result;
    }

    @Override
    public int insertReferenceFundDayData(List<ReferenceFundDayDTO> dataTable) {
        int result = 0;
        if (CollectionUtils.isEmpty(dataTable)) {
            return result;
        }
        List<WriteModel<Document>> writeModels = new ArrayList<>();
        for (ReferenceFundDayDTO item : dataTable) {
            String id = (item.getBUSINTYPE() == null ? "" : item.getBUSINTYPE()) + item.getFUNDCODE() + TimeUtil.dateToStr(item.getPDATE(), TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS);

            BasicDBObject upsetbyinsert = new BasicDBObject();
            upsetbyinsert.append("_id", id);

            BasicDBObject upsetbyupdate = new BasicDBObject();
            upsetbyupdate.append("BUSINTYPE", item.getBUSINTYPE());
            upsetbyupdate.append("CFMSAMOUNT", item.getCFMSAMOUNT() != null ? item.getCFMSAMOUNT().doubleValue() : null);
            upsetbyupdate.append("CFMSNUM", item.getCFMSNUM());
            upsetbyupdate.append("FUNDCODE", item.getFUNDCODE());
            upsetbyupdate.append("FUNDTYPE", item.getFUNDTYPE());
            upsetbyupdate.append("JJGSID", item.getJJGSID());
            upsetbyupdate.append("PDATE", item.getPDATE());
            upsetbyupdate.append("PERNUM", item.getPERNUM() != null ? item.getPERNUM().doubleValue() : null);
            upsetbyupdate.append("SHORTNAME", item.getSHORTNAME());

            BasicDBObject upset = new BasicDBObject("$setOnInsert", upsetbyinsert);
            upset.put("$set", upsetbyupdate);
            BasicDBObject query = new BasicDBObject("_id", id);
            UpdateOneModel<Document> pair = new UpdateOneModel<>(query, upset, new UpdateOptions().upsert(true));
            writeModels.add(pair);
        }
        int batchSize = 1000; // 每批次处理的数量
        List<List<WriteModel<Document>>> batches = Lists.partition(writeModels, batchSize);

        for (List<WriteModel<Document>> batch : batches) {
            BulkWriteOptions options = new BulkWriteOptions().ordered(false);
            BulkWriteResult temp2 = app.getCfhMongodbWrite().getMongoserver()
                    .getDatabase(CFHMongodbConstant.DB_CFH_TRADE)
                    .getCollection(CFHMongodbConstant.TB_REFERENCEFUND_DAY)
                    .bulkWrite(batch, options);
            log.info("批量插入ReferenceFundDayDTO数据，结果：{}", JSON.toJSONString(temp2));
            result += temp2.getInsertedCount() + temp2.getModifiedCount();
        }

        return result;
    }

    @Override
    public int insertReferenceFundJJGSData(List<ReferenceFundDayDTO> dataTable) {
        int result = 0;
        if (CollectionUtils.isEmpty(dataTable)) {
            return result;
        }
        List<WriteModel<Document>> writeModels = new ArrayList<>();
        for (ReferenceFundDayDTO item : dataTable) {
            String id = (item.getBUSINTYPE() == null ? "" : item.getBUSINTYPE()) + TimeUtil.dateToStr(item.getPDATE(), TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS)
                    + item.getJJGSID() + item.getFUNDTYPE();

            BasicDBObject upsetbyinsert = new BasicDBObject();
            upsetbyinsert.append("_id", id);

            BasicDBObject upsetbyupdate = new BasicDBObject();
            upsetbyupdate.append("BUSINTYPE", item.getBUSINTYPE());
            upsetbyupdate.append("CFMSAMOUNT", item.getCFMSAMOUNT() != null ? item.getCFMSAMOUNT().doubleValue() : null);
            upsetbyupdate.append("CFMSNUM", item.getCFMSNUM());
            upsetbyupdate.append("FUNDTYPE", item.getFUNDTYPE());
            upsetbyupdate.append("JJGSID", item.getJJGSID());
            upsetbyupdate.append("PDATE", item.getPDATE());
            upsetbyupdate.append("PERNUM", item.getPERNUM() != null ? item.getPERNUM().doubleValue() : null);

            BasicDBObject upset = new BasicDBObject("$setOnInsert", upsetbyinsert);
            upset.put("$set", upsetbyupdate);
            BasicDBObject query = new BasicDBObject("_id", id);
            UpdateOneModel<Document> pair = new UpdateOneModel<>(query, upset, new UpdateOptions().upsert(true));
            writeModels.add(pair);
        }
        int batchSize = 1000; // 每批次处理的数量
        List<List<WriteModel<Document>>> batches = Lists.partition(writeModels, batchSize);

        for (List<WriteModel<Document>> batch : batches) {
            BulkWriteOptions options = new BulkWriteOptions().ordered(false);
            BulkWriteResult temp2 = app.getCfhMongodbWrite().getMongoserver()
                    .getDatabase(CFHMongodbConstant.DB_CFH_TRADE)
                    .getCollection(CFHMongodbConstant.TB_REFERENCEFUND_JJGS)
                    .bulkWrite(batch, options);
            log.info("批量插入ReferenceFundJJGS数据，结果：{}", JSON.toJSONString(temp2));
            result += temp2.getInsertedCount() + temp2.getModifiedCount();
        }
        return result;
    }

    @Override
    public int insertReferenceFundMonthData(List<ReferenceFundDayDTO> dataTable) {
        int result = 0;
        if (CollectionUtils.isEmpty(dataTable)) {
            return result;
        }
        List<WriteModel<Document>> writeModels = new ArrayList<>();
        for (ReferenceFundDayDTO item : dataTable) {
            String id = (item.getBUSINTYPE() == null ? "" : item.getBUSINTYPE()) + item.getPDATESTR()
                    + item.getJJGSID();
            BasicDBObject upsetbyinsert = new BasicDBObject();
            upsetbyinsert.append("_id", id);

            BasicDBObject upsetbyupdate = new BasicDBObject();
            upsetbyupdate.append("CFMSNUM", item.getCFMSNUM());
            upsetbyupdate.append("JJGSID", item.getJJGSID());
            upsetbyupdate.append("PDATE", item.getPDATESTR());
            upsetbyupdate.append("PERNUM", item.getPERNUM() != null ? item.getPERNUM().doubleValue() : null);
            upsetbyupdate.append("cfmsamount", item.getCFMSAMOUNT() != null ? item.getCFMSAMOUNT().doubleValue() : null);
            upsetbyupdate.append("BUSINTYPE", item.getBUSINTYPE());

            BasicDBObject upset = new BasicDBObject("$setOnInsert", upsetbyinsert);
            upset.put("$set", upsetbyupdate);
            BasicDBObject query = new BasicDBObject("_id", id);
            UpdateOneModel<Document> pair = new UpdateOneModel<>(query, upset, new UpdateOptions().upsert(true));
            writeModels.add(pair);
        }
        int batchSize = 1000; // 每批次处理的数量
        List<List<WriteModel<Document>>> batches = Lists.partition(writeModels, batchSize);

        for (List<WriteModel<Document>> batch : batches) {
            BulkWriteOptions options = new BulkWriteOptions().ordered(false);
            BulkWriteResult temp2 = app.getCfhMongodbWrite().getMongoserver()
                    .getDatabase(CFHMongodbConstant.DB_CFH_TRADE)
                    .getCollection(CFHMongodbConstant.TB_REFERENCEFUND_MONTH)
                    .bulkWrite(batch, options);
            log.info("批量插入ReferenceFundMonth数据，结果：{}", JSON.toJSONString(temp2));
            result += temp2.getInsertedCount() + temp2.getModifiedCount();
        }
        return result;
    }

    @Override
    public Map<String, Integer> getLastInterfaceFundDayS(Date pdate) {
        try {
            List<BasicDBObject> andArr = new ArrayList<>();
            andArr.add(new BasicDBObject("PDATE", pdate));

            BasicDBObject query = new BasicDBObject("$and", andArr);
            HashMap<String, Object> map = new HashMap<>();
            map.put("FUNDCODE", 1);
            map.put("ZERONUM", 1);
            BasicDBObject field = new BasicDBObject(map);
            List<InterfaceFundDayDTO> documents = app.getCfhMongodbRead().query(CFHMongodbConstant.DB_CFH_TRADE, CFHMongodbConstant.TB_INTERFACEFUND_DAY, query, field, null, 1, 0, Integer.MAX_VALUE, InterfaceFundDayDTO.class);
            if (!CollectionUtils.isEmpty(documents)) {
                return documents.stream().collect(Collectors.toMap(InterfaceFundDayDTO::getFUNDCODE, InterfaceFundDayDTO::getZERONUM));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return new HashMap<>();
    }

    @Override
    public int insertInterfaceFundDayData(List<InterfaceFundDayDTO> dataTable) {
        int result = 0;
        if (CollectionUtils.isEmpty(dataTable)) {
            return result;
        }
        List<WriteModel<Document>> writeModels = new ArrayList<>();
        for (InterfaceFundDayDTO item : dataTable) {
            String id = item.getFUNDCODE() + TimeUtil.dateToStr(item.getPDATE(), TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS);
            BasicDBObject upsetbyinsert = new BasicDBObject();
            upsetbyinsert.append("_id", id);

            BasicDBObject upsetbyupdate = new BasicDBObject();
            upsetbyupdate.append("ALLNUM", item.getALLNUM());
            if (item.getDAYZERONUM() != null) {
                upsetbyupdate.append("DAYZERONUM", item.getDAYZERONUM());
            }
            upsetbyupdate.append("DAYZEROOLD", item.getDAYZEROOLD());
            upsetbyupdate.append("FUNDCODE", item.getFUNDCODE());
            upsetbyupdate.append("HOLDNUM", item.getHOLDNUM());
            upsetbyupdate.append("JJGSID", item.getJJGSID());
            upsetbyupdate.append("PDATE", item.getPDATE());
            upsetbyupdate.append("PERNUM", item.getPERNUM() != null ? item.getPERNUM().doubleValue() : null);
            upsetbyupdate.append("POSIVOL", item.getPOSIVOL() != null ? item.getPOSIVOL().doubleValue() : null);
            upsetbyupdate.append("POSIVOL_AMT", item.getPOSIVOL_AMT() != null ? item.getPOSIVOL_AMT().doubleValue() : null);
            upsetbyupdate.append("SHORTNAME", item.getSHORTNAME());
            upsetbyupdate.append("STOCKNUM", item.getSTOCKNUM());
            upsetbyupdate.append("ZERONUM", item.getZERONUM());
            upsetbyupdate.append("FIRSTNUM", item.getFIRSTNUM());

            BasicDBObject upset = new BasicDBObject("$setOnInsert", upsetbyinsert);
            upset.put("$set", upsetbyupdate);
            BasicDBObject query = new BasicDBObject("_id", id);
            UpdateOneModel<Document> pair = new UpdateOneModel<>(query, upset, new UpdateOptions().upsert(true));
            writeModels.add(pair);
        }
        int batchSize = 1000; // 每批次处理的数量
        List<List<WriteModel<Document>>> batches = Lists.partition(writeModels, batchSize);

        for (List<WriteModel<Document>> batch : batches) {
            BulkWriteOptions options = new BulkWriteOptions().ordered(false);
            BulkWriteResult temp2 = app.getCfhMongodbWrite().getMongoserver()
                    .getDatabase(CFHMongodbConstant.DB_CFH_TRADE)
                    .getCollection(CFHMongodbConstant.TB_INTERFACEFUND_DAY)
                    .bulkWrite(batch, options);
            log.info("批量插入TB_INTERFACEFUND_DAY数据，结果：{}", JSON.toJSONString(temp2));
            result += temp2.getInsertedCount() + temp2.getModifiedCount();
        }
        return result;
    }

    @Override
    public Map<String, Integer> getLastInterfaceFundJJGSS(Date pdate) {
        try {
            List<BasicDBObject> andArr = new ArrayList<>();
            andArr.add(new BasicDBObject("PDATE", pdate));
            andArr.add(new BasicDBObject("FUNDTYPE", "全部"));

            BasicDBObject query = new BasicDBObject("$and", andArr);
            HashMap<String, Object> map = new HashMap<>();
            map.put("JJGSID", 1);
            map.put("ZERONUM", 1);
            BasicDBObject field = new BasicDBObject(map);
            List<InterfaceFundDayDTO> documents = app.getCfhMongodbRead().query(CFHMongodbConstant.DB_CFH_TRADE, CFHMongodbConstant.TB_INTERFACEFUND_JJGS, query, field, null, 1, 0, Integer.MAX_VALUE, InterfaceFundDayDTO.class);
            if (!CollectionUtils.isEmpty(documents)) {
                return documents.stream().collect(Collectors.toMap(InterfaceFundDayDTO::getJJGSID, InterfaceFundDayDTO::getZERONUM));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return new HashMap<>();
    }

    @Override
    public int insertInterfaceFundJJGSData(List<InterfaceFundDayDTO> dataTable) {
        int result = 0;
        if (CollectionUtils.isEmpty(dataTable)) {
            return result;
        }
        List<WriteModel<Document>> writeModels = new ArrayList<>();
        for (InterfaceFundDayDTO item : dataTable) {
            String id = item.getJJGSID() + item.getFUNDTYPE() + TimeUtil.dateToStr(item.getPDATE(), TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS);
            BasicDBObject upsetbyinsert = new BasicDBObject();
            upsetbyinsert.append("_id", id);

            BasicDBObject upsetbyupdate = new BasicDBObject();
            if (item.getDAYZERONUM() != null) {
                upsetbyupdate.append("DAYZERONUM", item.getDAYZERONUM());
            }
            upsetbyupdate.append("DAYZEROOLD", item.getDAYZEROOLD());
            upsetbyupdate.append("FIRSTNUM", item.getFIRSTNUM());
            upsetbyupdate.append("FUNDTYPE", item.getFUNDTYPE());
            upsetbyupdate.append("HOLDNUM", item.getHOLDNUM());
            upsetbyupdate.append("JJGSID", item.getJJGSID());
            upsetbyupdate.append("PDATE", item.getPDATE());
            upsetbyupdate.append("PERNUM", item.getPERNUM() != null ? item.getPERNUM().doubleValue() : null);
            upsetbyupdate.append("POSIVOL", item.getPOSIVOL() != null ? item.getPOSIVOL().doubleValue() : null);
            upsetbyupdate.append("POSIVOL_AMT", item.getPOSIVOL_AMT() != null ? item.getPOSIVOL_AMT().doubleValue() : null);
            upsetbyupdate.append("STOCKNUM", item.getSTOCKNUM());
            upsetbyupdate.append("ZERONUM", item.getZERONUM());

            BasicDBObject upset = new BasicDBObject("$setOnInsert", upsetbyinsert);
            upset.put("$set", upsetbyupdate);
            BasicDBObject query = new BasicDBObject("_id", id);
            UpdateOneModel<Document> pair = new UpdateOneModel<>(query, upset, new UpdateOptions().upsert(true));
            writeModels.add(pair);
        }
        int batchSize = 1000; // 每批次处理的数量
        List<List<WriteModel<Document>>> batches = Lists.partition(writeModels, batchSize);

        for (List<WriteModel<Document>> batch : batches) {
            BulkWriteOptions options = new BulkWriteOptions().ordered(false);
            BulkWriteResult temp2 = app.getCfhMongodbWrite().getMongoserver()
                    .getDatabase(CFHMongodbConstant.DB_CFH_TRADE)
                    .getCollection(CFHMongodbConstant.TB_INTERFACEFUND_JJGS)
                    .bulkWrite(batch, options);
            log.info("批量插入TB_INTERFACEFUND_JJGS数据，结果：{}", JSON.toJSONString(temp2));
            result += temp2.getInsertedCount() + temp2.getModifiedCount();
        }
        return result;
    }

    @Override
    public int insertFundCustnoFeaturEData(List<InterfaceFundDayDTO> dataTable) {
        int result = 0;
        if (CollectionUtils.isEmpty(dataTable)) {
            return result;
        }
        List<WriteModel<Document>> writeModels = new ArrayList<>();
        for (InterfaceFundDayDTO item : dataTable) {
            String id = item.getFEATURETYPE() + item.getFUNDCODE()
                    + (item.getCYSC() == null ? "" : item.getCYSC()) + (item.getFEATURE() == null ? "" : item.getFEATURE());
            BasicDBObject upsetbyinsert = new BasicDBObject();
            upsetbyinsert.append("_id", id);

            BasicDBObject upsetbyupdate = new BasicDBObject();
            upsetbyupdate.append("FEATURE", item.getFEATURE());
            upsetbyupdate.append("FEATURETYPE", item.getFEATURETYPE());
            upsetbyupdate.append("FUNDCODE", item.getFUNDCODE());
            upsetbyupdate.append("JJGSID", item.getJJGSID());
            upsetbyupdate.append("NUM", item.getNUM());
            upsetbyupdate.append("SHORTNAME", item.getSHORTNAME());
            upsetbyupdate.append("CYSC", item.getCYSC());

            BasicDBObject upset = new BasicDBObject("$setOnInsert", upsetbyinsert);
            upset.put("$set", upsetbyupdate);
            BasicDBObject query = new BasicDBObject("_id", id);
            UpdateOneModel<Document> pair = new UpdateOneModel<>(query, upset, new UpdateOptions().upsert(true));
            writeModels.add(pair);
        }
        int batchSize = 1000; // 每批次处理的数量
        List<List<WriteModel<Document>>> batches = Lists.partition(writeModels, batchSize);

        for (List<WriteModel<Document>> batch : batches) {
            BulkWriteOptions options = new BulkWriteOptions().ordered(false);
            BulkWriteResult temp2 = app.getCfhMongodbWrite().getMongoserver()
                    .getDatabase(CFHMongodbConstant.DB_CFH_TRADE)
                    .getCollection(CFHMongodbConstant.TB_FUNDCUSTNO_FEATURE)
                    .bulkWrite(batch, options);
            log.info("批量插入TB_FUNDCUSTNO_FEATURE数据，结果：{}", JSON.toJSONString(temp2));
            result += temp2.getInsertedCount() + temp2.getModifiedCount();
        }
        return result;
    }

    @Override
    public int deleteFundCustnoFeatureByFeatureType(String featerType) {
        if (StringUtils.isEmpty(featerType)) {
            return 0;
        }
        List<BasicDBObject> andArr = new ArrayList<>();
        andArr.add(new BasicDBObject("FEATURETYPE", featerType));
        BasicDBObject query = new BasicDBObject("$and", andArr);
        DeleteResult deleteResult = app.getCfhMongodbWrite().getMongoserver().getDatabase(CFHMongodbConstant.DB_CFH_TRADE)
                .getCollection(CFHMongodbConstant.TB_FUNDCUSTNO_FEATURE).deleteMany(query);
        return (int) deleteResult.getDeletedCount();
    }

    @Override
    public int insertCustnoFeaturEData(List<InterfaceFundDayDTO> dataTable) {
        int result = 0;
        if (CollectionUtils.isEmpty(dataTable)) {
            return result;
        }
        List<WriteModel<Document>> writeModels = new ArrayList<>();
        for (InterfaceFundDayDTO item : dataTable) {
            String id = item.getFEATURETYPE() + (item.getFEATURE() == null ? "" : item.getFEATURE()) + item.getJJGSID();
            BasicDBObject upsetbyinsert = new BasicDBObject();
            upsetbyinsert.append("_id", id);

            BasicDBObject upsetbyupdate = new BasicDBObject();
            upsetbyupdate.append("FEATURE", item.getFEATURE());
            upsetbyupdate.append("FEATURETYPE", item.getFEATURETYPE());
            upsetbyupdate.append("JJGSID", item.getJJGSID());
            upsetbyupdate.append("NUM", item.getNUM());

            BasicDBObject upset = new BasicDBObject("$setOnInsert", upsetbyinsert);
            upset.put("$set", upsetbyupdate);
            BasicDBObject query = new BasicDBObject("_id", id);
            UpdateOneModel<Document> pair = new UpdateOneModel<>(query, upset, new UpdateOptions().upsert(true));
            writeModels.add(pair);
        }
        int batchSize = 1000; // 每批次处理的数量
        List<List<WriteModel<Document>>> batches = Lists.partition(writeModels, batchSize);

        for (List<WriteModel<Document>> batch : batches) {
            BulkWriteOptions options = new BulkWriteOptions().ordered(false);
            BulkWriteResult temp2 = app.getCfhMongodbWrite().getMongoserver()
                    .getDatabase(CFHMongodbConstant.DB_CFH_TRADE)
                    .getCollection(CFHMongodbConstant.TB_COMCUSTNO_FEATURE)
                    .bulkWrite(batch, options);
            log.info("批量插入TB_COMCUSTNO_FEATURE数据，结果：{}", JSON.toJSONString(temp2));
            result += temp2.getInsertedCount() + temp2.getModifiedCount();
        }
        return result;
    }

    @Override
    public int insertCFTTradeRankData(List<CFHTradeRankDataDTO> dataTable) {
        int result = 0;
        if (CollectionUtils.isEmpty(dataTable)) {
            return result;
        }
        List<WriteModel<Document>> writeModels = new ArrayList<>();
        for (CFHTradeRankDataDTO item : dataTable) {
            String id = item.getJJGSID() + item.getFUNDTYPE() + item.getCalc_date();
            BasicDBObject upsetbyinsert = new BasicDBObject();
            upsetbyinsert.append("_id", id);

            BasicDBObject upsetbyupdate = new BasicDBObject();
            upsetbyupdate.append("JJGSID", item.getJJGSID());
            upsetbyupdate.append("FUNDTYPE", item.getFUNDTYPE());
            upsetbyupdate.append("CALCDATE", item.getCalc_date());
            upsetbyupdate.append("POSIAMT", item.getPOSI_AMT() != null ? item.getPOSI_AMT().doubleValue() : null);
            upsetbyupdate.append("POSIAMTRANK", item.getPOSI_AMT_RANK());
            upsetbyupdate.append("POSIAMTRATE", item.getPOSI_AMT_RATE() != null ? item.getPOSI_AMT_RATE().doubleValue() : null);
            upsetbyupdate.append("POSIAMTRATERANK", item.getPOSI_AMT_RATE_RANK());
            upsetbyupdate.append("POSINUM", item.getPOSI_NUM() != null ? item.getPOSI_NUM().doubleValue() : null);
            upsetbyupdate.append("POSINUMRANK", item.getPOSI_NUM_RANK());
            upsetbyupdate.append("POSIPER", item.getPOSI_PER() != null ? item.getPOSI_PER().doubleValue() : null);
            upsetbyupdate.append("POSIPERRANK", item.getPOSI_PER_RANK());

            BasicDBObject upset = new BasicDBObject("$setOnInsert", upsetbyinsert);
            upset.put("$set", upsetbyupdate);
            BasicDBObject query = new BasicDBObject("_id", id);
            UpdateOneModel<Document> pair = new UpdateOneModel<>(query, upset, new UpdateOptions().upsert(true));
            writeModels.add(pair);
        }
        int batchSize = 1000; // 每批次处理的数量
        List<List<WriteModel<Document>>> batches = Lists.partition(writeModels, batchSize);
        for (List<WriteModel<Document>> batch : batches) {
            BulkWriteOptions options = new BulkWriteOptions().ordered(false);
            BulkWriteResult temp2 = app.getCfhMongodbWrite().getMongoserver()
                    .getDatabase(CFHMongodbConstant.DB_CFH_TRADE)
                    .getCollection(CFHMongodbConstant.TB_CFHTRADE_RANK)
                    .bulkWrite(batch, options);
            log.info("批量插入TB_CFHTRADE_RANK数据，结果：{}", JSON.toJSONString(temp2));
            result += temp2.getInsertedCount() + temp2.getModifiedCount();
        }
        return result;
    }

    @Override
    public Date getBreakPoint(String seqId, String defaultValue, String timeFormat) {
        Date preBreakPointDate = null;
        String preBreakPoint = getCFHGeneralData(seqId, defaultValue);
        try {
            JSONObject o = (JSONObject) JSON.parseArray(preBreakPoint).get(0);
            preBreakPointDate = DateHelper.stringToDate2(o.getString("time"), timeFormat);
        } catch (Exception e) {
            log.warn("财富号java服务，断点时间转换异常，【db.getSiblingDB(\"TTFundCFHDB\").getCollection(\"TB_CFHGeneralData\").find({\"_id\": {$eq: '" + seqId + "'}})】");
            preBreakPointDate = DateHelper.stringToDate2(defaultValue, timeFormat);
        }
        return preBreakPointDate;

    }

    /**
     * 必须要_id
     *
     * @param data
     * @param databaseName
     * @param collectionName
     * @param <T>
     * @return
     */
    @Override
    public <T> int insertOrUpdate(List<T> data, String databaseName, String collectionName) {
        int result = 0;
        if (data == null || data.isEmpty()) {
            return result;
        }

        List<WriteModel<Document>> writeModels = new ArrayList<>();
        for (T item : data) {
            Document updateDoc = buildUpdateDocument(item);
            BasicDBObject upsetbyinsert = new BasicDBObject();
            Object id = updateDoc.get("_id");
            upsetbyinsert.append("_id", id);
            BasicDBObject upset = new BasicDBObject("$setOnInsert", upsetbyinsert);
            updateDoc.remove("_id");
            upset.put("$set", updateDoc);
            BasicDBObject query = new BasicDBObject("_id", id);
            UpdateOneModel<Document> pair = new UpdateOneModel<>(query, upset, new UpdateOptions().upsert(true));
            writeModels.add(pair);
        }

        List<List<WriteModel<Document>>> batches = Lists.partition(writeModels, BATCH_SIZE);

        for (List<WriteModel<Document>> batch : batches) {
            BulkWriteOptions options = new BulkWriteOptions().ordered(false);
            BulkWriteResult temp2 = app.getCfhMongodbWrite().getMongoserver().getDatabase(databaseName).getCollection(collectionName).bulkWrite(batch, options);
            log.info("批量插入或更新数据库{}，表{}，新增：{}，修改：{}，匹配无需修改：{}"
                    , databaseName, collectionName, temp2.getInsertedCount(), temp2.getModifiedCount()
                    , temp2.getMatchedCount() - temp2.getModifiedCount());
            result += temp2.getInsertedCount() + temp2.getModifiedCount();
        }
        return result;
    }

    @Override
    public void saveMessage(String message, String topic, ObjectId id) {

        try {
            if (StringUtils.isEmpty(message)) {
                return;
            }
            // 创建文档
            Document document = new Document();
            document.put("time", TimeUtil.getNowDate());
            document.put("message", message);
            document.put("topic", topic);
            document.put("_id", id == null ? new ObjectId() : id);

            // 获取 MongoDB 客户端
            MongoDatabase database = app.getCfhMongodbWrite().getMongoserver().getDatabase(CFHMongodbConstant.DB_TT_FUND_CFH);
            MongoCollection<Document> collection = database.getCollection(CFHMongodbConstant.TB_KAFKA_MESSAGE);

            // 插入文档
            collection.insertOne(document);
        } catch (Exception e) {
            log.error("保存kafka消息失败,", e.getMessage(), e);
        }
    }

    @Override
    public <T> int insertInc(T data, String databaseName, String collectionName, String... incFiled) {
        int result = 0;
        if (data == null) {
            return result;
        }
        Document document = buildUpdateDocument(data);
        //新增
        BasicDBObject upsetbyinsert = new BasicDBObject();
        Object id = document.get("_id");
        upsetbyinsert.append("_id", id);
        document.remove("_id");

        //inc
        Document upsetbyinc = new Document();
        for (String field : incFiled) {
            upsetbyinc.put(field, document.get(field));
            document.remove(field);
        }

        BasicDBObject upset = new BasicDBObject("$setOnInsert", upsetbyinsert);
        //修改
        upset.put("$set", document);
        if (incFiled.length > 0) {
            upset.put("$inc", upsetbyinc);
        }

        //查询条件
        BasicDBObject query = new BasicDBObject("_id", id);

        UpdateOptions options = new UpdateOptions().upsert(true);
        UpdateResult res = app.getCfhMongodbWrite().getMongoserver().getDatabase(databaseName).getCollection(collectionName)
                .updateOne(query, upset, options);
        // 获取被修改和插入的文档数量
        int modifiedCount = (int) res.getModifiedCount();
        int insertedCount = res.getUpsertedId() != null ? 1 : 0;
        return modifiedCount + insertedCount;
    }

    @Override
    public List<ShortVideoMongoAvInfoDTO> getShortVideoByAuthorAndTime(String cfhId, String emuid, Date startTime, Date endTime) {
        List<BasicDBObject> andArr = new ArrayList<>();
        if (StringUtils.isNotEmpty(cfhId)) {
            //财富号数据
            andArr.add(new BasicDBObject("cfhId", cfhId));
        } else if (StringUtils.isNotEmpty(emuid)) {
            //大V数据
            andArr.add(new BasicDBObject("emUID", emuid));
        } else {
            return new ArrayList<>();
        }
        andArr.add(new BasicDBObject("releaseTime", new BasicDBObject("$gte", startTime)));
        andArr.add(new BasicDBObject("releaseTime", new BasicDBObject("$lt", endTime)));

        BasicDBObject query = new BasicDBObject();
        query.append("$and", andArr);

        BasicDBObject field = new BasicDBObject(CommonHelper.fieldDic(ShortVideoMongoAvInfoDTO.class));

        List<ShortVideoMongoAvInfoDTO> data = app.getCfhMongodbRead().query(CFHMongodbConstant.DB_TT_FUND_CFH, CFHMongodbConstant.TB_SHORTVIDEO
                , query, field, null, 1, Integer.MAX_VALUE
                , ShortVideoMongoAvInfoDTO.class);
        return data;
    }


    @Override
    public int deleteAll(String dbName, String collectionName) {
        if (StringUtils.isEmpty(dbName) || StringUtils.isEmpty(collectionName)) {
            return 0;
        }
        BasicDBObject query = new BasicDBObject();
        DeleteResult deleteResult = app.getCfhMongodbWrite().getMongoserver().getDatabase(dbName)
                .getCollection(collectionName).deleteMany(query);
        return (int) deleteResult.getDeletedCount();
    }

    @Override
    public Long selectNewUpdateTradeDate() {

        BasicDBObject query = new BasicDBObject();
        BasicDBObject sort = new BasicDBObject("ESEQID", -1);
        HashMap<String, Object> map = new HashMap<>();
        map.put("ESEQID", 1);
        BasicDBObject field = new BasicDBObject(map);
        List<Document> documents = app.getCfhMongodbRead().query(CFHMongodbConstant.DB_CFH_TRADE
                , CFHMongodbConstant.TB_TRADE_DATE, query, field, sort, 1, 1, 1);
        if (!CollectionUtils.isEmpty(documents)) {
            String eseqid = documents.get(0).get("ESEQID").toString();
            return new BigDecimal(eseqid).longValue();
        }
        return null;
    }


    @Override
    public int deleteByTime(String dbName, String collectionName, String fieldName, int day) {
        if (StringUtils.isEmpty(dbName) || StringUtils.isEmpty(collectionName) || StringUtils.isEmpty(fieldName)) {
            return 0;
        }
        List<BasicDBObject> andArr = new ArrayList<>();
        andArr.add(new BasicDBObject(fieldName, new BasicDBObject("$lt", DateUtils.addDays(TimeUtil.getNowDate(), day))));
        BasicDBObject query = new BasicDBObject("$and", andArr);
        DeleteResult deleteResult = app.getCfhMongodbWrite().getMongoserver().getDatabase(dbName)
                .getCollection(collectionName).deleteMany(query);
        return (int) deleteResult.getDeletedCount();
    }

    @Override
    public int deleteMessage(Date date, String topic) {

        BasicDBObject query = new BasicDBObject("time", new BasicDBObject("$lt", date));
        query.append("topic", topic);
        DeleteResult deleteResult = app.getCfhMongodbWrite().getMongoserver()
                .getDatabase(CFHMongodbConstant.DB_TT_FUND_CFH)
                .getCollection(CFHMongodbConstant.TB_KAFKA_MESSAGE)
                .deleteMany(query);
        return (int) deleteResult.getDeletedCount();
    }

    @Override
    public List<String> getAllTopic() {
        return app.getCfhMongodbWrite().getMongoserver()
                .getDatabase(CFHMongodbConstant.DB_TT_FUND_CFH)
                .getCollection(CFHMongodbConstant.TB_KAFKA_MESSAGE)
                .distinct("topic", String.class)
                .into(new ArrayList<>());
    }

    @Override
    public void getAllCfhAndSetCache() {
        String key = "allcfhlist_java";
        BasicDBObject query = new BasicDBObject();

        BasicDBObject field =
                new BasicDBObject(CommonHelper.fieldDic(HomeCFHInfoDO.class));

        List<HomeCFHInfoDO> docs = app.getCfhMongodbRead().query(
                CFHMongodbConstant.DB_TT_FUND_CFH,
                CFHMongodbConstant.TB_CFH_LIST,
                query,
                field,
                null,
                HomeCFHInfoDO.class);
        if (!CollectionUtils.isEmpty(docs)) {
            app.cfhrediswrite.set(key, JSON.toJSONString(docs), 60 * 15L);
            app.cfhrediswritePJ.set(key, JSON.toJSONString(docs), 60 * 15L);
        }
    }

    @Override
    public Integer selectProductReportDataSync() {
        BasicDBObject query = new BasicDBObject();
        List<BasicDBObject> andArr = new ArrayList<>();
        andArr.add(new BasicDBObject("ProductType", 2));
        andArr.add(new BasicDBObject("ReadType", 11));
        andArr.add(new BasicDBObject("CFHID", ""));
        andArr.add(new BasicDBObject("IsShow", 1));
        andArr.add(new BasicDBObject("ReportType", 1));
        andArr.add(new BasicDBObject("Status", 0));
        query.put("$and", andArr);
        Long count = app.getCfhMongodbRead().count(CFHMongodbConstant.DB_TT_FUND_CFH, CFHMongodbConstant.TB_PRODUCT_READ, query);
        return count.intValue();

    }

    private <T> Document buildUpdateDocument(T obj) {
        Document doc = new Document();
        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                String name = field.getName();
                Object value = field.get(obj);
                TableColumn tableColumn = field.getAnnotation(TableColumn.class);
                if (tableColumn != null && StringUtils.isNotEmpty(tableColumn.name())) {
                    name = tableColumn.name();
                }
                if (value instanceof BigDecimal) {
//                    doc.append(name, ((BigDecimal) value).doubleValue());
                    BigDecimal bigDecimalValue = (BigDecimal) value;
                    int scale = bigDecimalValue.scale(); // 获取小数位数
                    BigDecimal roundedValue = bigDecimalValue.setScale(scale, RoundingMode.HALF_UP); // 四舍五入
                    double doubleValue = roundedValue.doubleValue();
                    doc.append(name, doubleValue);
                }
                if (value != null) {
                    doc.append(name, value);
                }
            } catch (IllegalAccessException e) {
                log.error("无法访问字段: " + field.getName(), e);
            }
        }
        return doc;
    }
}
