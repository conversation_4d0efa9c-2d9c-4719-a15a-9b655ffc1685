package ttfund.web.fortuneservice.dao.impl;

import com.alibaba.fastjson.JSON;
import com.mongodb.BasicDBObject;
import com.mongodb.MongoClient;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.MongoIterable;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import com.ttfund.web.base.helper.DateHelper;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.config.App;
import ttfund.web.fortuneservice.constant.CFHMongodbConstant;
import ttfund.web.fortuneservice.constant.CFHTradeMongodbConstant;
import ttfund.web.fortuneservice.constant.TradeTypeEnum;
import ttfund.web.fortuneservice.dao.CfhTradeMongodbDao;
import ttfund.web.fortuneservice.model.dto.FundApplyDataStatModel;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Repository
public class CfhTradeMongodbDaoImpl implements CfhTradeMongodbDao {

    private static final String MONGODB_DATABASES = "CFHTradeDB";

    @Autowired
    private App app;
    @Override
    /**
     * 查询mongodb库存在的表名
     * @return
     */
    public Set<String> getTableNames(){
        return getTableNames(MONGODB_DATABASES);
    }

    /**
     * 按照表名删除财富号交易数据表
     * @param needDropTableNames
     * @return
     */
    @Override
    public boolean dropTablesByName(Set<String> needDropTableNames) {
        boolean result = false;
        try {
            for (String tableName : needDropTableNames) {
                app.getCfhMongodbWrite().getMongoserver().getDatabase(MONGODB_DATABASES).getCollection(tableName).drop();
            }
            result = true;
        }catch (Exception e){
            log.error("按照名称删除财富号交易数据表失败，{}，{}", e.getMessage(), e);
        }
        return result;
    }

    @Override
    public void updateAndInsertCFHData(Date date, List<FundApplyDataStatModel> models) {
        if (date == null || CollectionUtils.isEmpty(models)) {
            log.info("数据为空，不执行操作");
            return;
        }
        String transDay = DateHelper.dateToStr(date, DateHelper.FORMAT_YYYYMMDD);
        String tbName = String.format(CFHTradeMongodbConstant.TB_NAME_FUND_APPLY_DAY, transDay);
        List<WriteModel<Document>> writeModels = new ArrayList<>();
        for (FundApplyDataStatModel model : models) {
            String id = String.format("%s_%s_%s_%s", model.getFundcode(), DateHelper.dateToStr(model.getApplyday(), DateHelper.FORMAT_YYYYMMDD), model.getApplyhour(),model.getBusintype());
            Document upsetbyinsert = new Document();
            upsetbyinsert.put("_id", id);

            Document upsetbyupdate = new Document();
            upsetbyupdate.put("JJGSID", model.getJjgsid());
            upsetbyupdate.put("FUNDCODE", model.getFundcode());
            upsetbyupdate.put("BUSINTYPE", model.getBusintype());
            upsetbyupdate.put("APPLYDAY", model.getApplyday());
            upsetbyupdate.put("APPLYAMOUNT", model.getApplyAmount() == null? null: model.getApplyAmount().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
            upsetbyupdate.put("APPLYHOUR", model.getApplyhour());
            upsetbyupdate.put("APPLYNUM", model.getApplynum() == null? null: model.getApplynum().doubleValue());
            upsetbyupdate.put("SHORTNAME", model.getShortname());
            upsetbyupdate.put("TRANSACTIONDATE", model.getTransactionDate());
            upsetbyupdate.put("DATATYPE", TradeTypeEnum.PERSONAL.name());//先写死个人数据，接口只给了个人数据
            Document upset = new Document("$setOnInsert", upsetbyinsert);
            upset.put("$set", upsetbyupdate);

            BasicDBObject query = new BasicDBObject("_id", id);

            UpdateManyModel<Document> pair = new UpdateManyModel<>(query, upset, new UpdateOptions().upsert(true));
            writeModels.add(pair);
        }
        if (writeModels.size() > 0) {
            BulkWriteOptions options = new BulkWriteOptions().ordered(false);
            BulkWriteResult temp2 = app.getCfhMongodbWrite().getMongoserver().getDatabase(MONGODB_DATABASES)
                    .getCollection(tbName).bulkWrite(writeModels, options);
            XxlJobLogger.log("一共{}条数据，{}表插入了{}条数据,更新了{}条", models.size(), tbName, temp2.getUpserts().size(), temp2.getModifiedCount());
            log.info("一共{}条数据，{}表插入了{}条数据,更新了{}条", models.size(), tbName, temp2.getUpserts().size(), temp2.getModifiedCount());
        }

    }

    @Override
    public void deleteAndInsertCFHData(Date date, List<FundApplyDataStatModel> models) {
        if (date == null || CollectionUtils.isEmpty(models)) {
            log.info("数据为空，不执行操作");
            return;
        }

        String transDay = DateHelper.dateToStr(date, DateHelper.FORMAT_YYYYMMDD);
        String tbName = String.format(CFHTradeMongodbConstant.TB_NAME_FUND_APPLY_DAY, transDay);

        try {
            // 删除表全部数据
            DeleteResult deleteResult = app.getCfhMongodbWrite().getMongoserver()
                    .getDatabase(MONGODB_DATABASES).getCollection(tbName).deleteMany(new Document());
            log.info("删除{}表全部数据，删除{}条数据", tbName, deleteResult.getDeletedCount());

            // 初始化计数器
            int totalInserted = 0;
            int totalUpdated = 0;
            // 分批处理数据
            int batchSize = 2000; // 每批处理2000条数据
            for (int i = 0; i < models.size(); i += batchSize) {
                List<FundApplyDataStatModel> batch = models.subList(i, Math.min(i + batchSize, models.size()));
                BulkWriteResult result = processBatchData(tbName, batch);
                // 累计插入和更新的条数
                totalInserted += result.getUpserts().size();
                totalUpdated += result.getModifiedCount();
            }
            log.info("处理完成：总计{}条数据，{}表插入了{}条数据，更新了{}条数据",
                    models.size(), tbName, totalInserted, totalUpdated);
        } catch (Exception e) {
            log.error("处理{}表数据时发生异常：{}", tbName, e.getMessage(), e);
        }
    }

    private BulkWriteResult processBatchData(String tbName, List<FundApplyDataStatModel> models) {
        List<WriteModel<Document>> writeModels = new ArrayList<>();
        for (FundApplyDataStatModel model : models) {
            try {
                String id = generateUniqueId(model);
                Document upset = buildUpsertDocument(model, id);
                BasicDBObject query = new BasicDBObject("_id", id);
                writeModels.add(new UpdateManyModel<>(query, upset, new UpdateOptions().upsert(true)));
            } catch (Exception e) {
                log.warn("处理单条数据时发生异常,data:{}", JSON.toJSONString(model), e);
            }
        }

        if (!writeModels.isEmpty()) {
            BulkWriteOptions options = new BulkWriteOptions().ordered(false);
            BulkWriteResult result = app.getCfhMongodbWrite().getMongoserver()
                    .getDatabase(MONGODB_DATABASES).getCollection(tbName).bulkWrite(writeModels, options);
            log.info("批量处理{}表数据：插入{}条，更新{}条", tbName,
                    result.getUpserts().size(), result.getModifiedCount());
            return result;
        }
        return BulkWriteResult.unacknowledged(); // 返回空结果以避免空指针异常
    }

    private String generateUniqueId(FundApplyDataStatModel model) {
        return String.format("%s_%s_%s_%s",
                model.getFundcode(),
                DateHelper.dateToStr(model.getApplyday(), DateHelper.FORMAT_YYYYMMDD),
                model.getApplyhour(),
                model.getBusintype());
    }

    private Document buildUpsertDocument(FundApplyDataStatModel model, String id) {
        Document upsetbyinsert = new Document("_id", id);

        Document upsetbyupdate = new Document();
        upsetbyupdate.put("JJGSID", model.getJjgsid());
        upsetbyupdate.put("FUNDCODE", model.getFundcode());
        upsetbyupdate.put("BUSINTYPE", model.getBusintype());
        upsetbyupdate.put("APPLYDAY", model.getApplyday());
        upsetbyupdate.put("APPLYAMOUNT", model.getApplyAmount() == null
                ? null : model.getApplyAmount().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
        upsetbyupdate.put("APPLYHOUR", model.getApplyhour());
        upsetbyupdate.put("APPLYNUM", model.getApplynum() == null
                ? null : model.getApplynum().doubleValue());
        upsetbyupdate.put("SHORTNAME", model.getShortname());
        upsetbyupdate.put("TRANSACTIONDATE", model.getTransactionDate());
        upsetbyupdate.put("DATATYPE", TradeTypeEnum.PERSONAL.name());

        Document upset = new Document("$setOnInsert", upsetbyinsert);
        upset.put("$set", upsetbyupdate);

        return upset;
    }


    public Set<String> getTableNames(String dbName){
        Set<String> result = new HashSet<>();
        MongoClient mongoClient = app.getCfhMongodbWrite().getMongoserver();
        // 获取数据库中的所有集合（表名列表）
        MongoIterable<String> tableNames = mongoClient.getDatabase(dbName).listCollectionNames();

        // 遍历集合名称并打印
        for (String collectionName : tableNames) {
            result.add(collectionName);
        }
        return result;
    }
}
