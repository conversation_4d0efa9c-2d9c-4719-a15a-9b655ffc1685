package ttfund.web.fortuneservice.dao.impl;

import com.mongodb.BasicDBObject;
import com.ttfund.web.base.helper.CommonHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import ttfund.web.fortuneservice.config.App;
import ttfund.web.fortuneservice.dao.HqMongodbDao;

import java.util.List;

/**
 * 行情 mongodb交互
 * <AUTHOR>
 * @version 1.0.0
 * @className HqMongodbDaoImpl
 * @date 2023/4/7 15:34
 */
@Slf4j
@Repository
public class HqMongodbDaoImpl implements HqMongodbDao {

    @Autowired
    private App app;

    @Override
    public <T> List<T> selectCollection(String dbName, String collectionName, Class<T> clazz) {
        BasicDBObject query = new BasicDBObject();
        BasicDBObject field = new BasicDBObject(CommonHelper.fieldDic(clazz));
        return app.getHqMongodbRead().query(dbName, collectionName, query, field, null, 1, Integer.MAX_VALUE
                , Integer.MAX_VALUE, clazz);
    }
}
