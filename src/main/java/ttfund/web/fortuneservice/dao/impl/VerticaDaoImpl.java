package ttfund.web.fortuneservice.dao.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrBuilder;
import com.alibaba.fastjson.JSON;
import com.mongodb.BasicDBObject;
import com.ttfund.web.base.helper.DateHelper;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.config.App;
import ttfund.web.fortuneservice.constant.VerticaSqlConstant;
import ttfund.web.fortuneservice.constant.VerticaTableNameConstant;
import ttfund.web.fortuneservice.dao.VerticaDao;
import ttfund.web.fortuneservice.model.dto.*;
import ttfund.web.fortuneservice.service.impl.CFHTradeDataRelocateCommonService;
import ttfund.web.fortuneservice.utils.TimeUtil;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * VerticaDaoImpl.java
 *
 * <AUTHOR>
 * @date 2023/5/6 16:44
 */
@Slf4j
@Repository
public class VerticaDaoImpl implements VerticaDao {

    private static final int BATCH_NUM = 10000;

    @Autowired
    App app;

    @Override
    public List<FundHighLevelInfo> getFundCodeType() {
        String sql = "select fundCode, isPvt, fundName,STABILSHDATE as establishmentTime,nvl(MANAGER, FUNDCOMPANYNAME) as manager from FUND.SYNC_MFINFO_ALL_BASIC_SYN where isbuy in ('1','2','3','4','5','6') and eisdel = '0' and isdisplay = '1';";
        List<Map> map = app.getVerticaRead().executeQuery(sql, null);
        return JSON.parseArray(JSON.toJSONString(map), FundHighLevelInfo.class);
    }

    @Override
    public List<FundSizeDto> getFundSizeInfo(List<String> fundCodes) {
        String sql = "SELECT fundCode, fundName, fundSize  \n" +
                "FROM (  \n" +
                "    SELECT f.C_FUNDCODE as fundCode, f.C_FUNDNAME as fundName, f.C_TOTALFUNDVOL*f.C_NAV as fundSize, f.C_UPDATEDATE,  \n" +
                "           ROW_NUMBER() OVER (PARTITION BY f.C_FUNDCODE ORDER BY f.C_UPDATEDATE DESC) as row_num  \n" +
                "    FROM FUND.TB_ZD07 as f  \n" +
                "    WHERE f.C_FUNDCODE in (" + String.join(",", Collections.nCopies(fundCodes.size(), "?")) + ")  AND f.C_TOTALFUNDVOL IS NOT NULL AND f.C_NAV IS NOT NULL \n" +
                ") ranked_funds  \n" +
                "WHERE row_num = 1  \n" +
                "ORDER BY fundCode;  ";
        List<Map> map = app.getVerticaRead().executeQuery(sql, new ArrayList<>(fundCodes));
        return JSON.parseArray(JSON.toJSONString(map), FundSizeDto.class);
    }

    @Override
    public List<String> getCloseFund(Date date) {
        List<String> result = new ArrayList<>();
        String sql = "select distinct c_fundcode from fund.tb_fundspecialstage a  where c_isenabled = '1' " +
                "and a.c_marketbegindate >= ? and a.C_WHOLELIMIT = 0 and C_SELLSTATE = '2'";
        List<Object> list = new ArrayList<>();
        list.add(date);
        List<Map> maps = app.getVerticaRead().executeQuery(sql, list);
        if (!CollectionUtils.isEmpty(maps)) {
            for (Map map : maps) {
                result.add((String) map.get("c_fundcode"));
            }
        }
        return result;
    }

    @Override
    public List<FundNetWorthDto> getFundNetWorthInfo(List<String> fundCodes) {
        String sql = "SELECT fundCode, pDate \n" +
                "FROM ( \n" +
                "    SELECT f1.mfcode as fundCode, f1.pdate as pDate, \n" +
                "           ROW_NUMBER() OVER (PARTITION BY f1.mfcode ORDER BY f1.pDate DESC) as row_num \n" +
                "    FROM FUND.MFUND_NATVALUE as f1 \n" +
                "    WHERE f1.mfcode in (" + String.join(",", Collections.nCopies(fundCodes.size(), "?")) + ") \n" +
                ") ranked_funds \n" +
                "WHERE row_num = 1 \n" +
                "ORDER BY fundCode; ";
        List<Map> map = app.getVerticaRead().executeQuery(sql, new ArrayList<>(fundCodes));
        return JSON.parseArray(JSON.toJSONString(map), FundNetWorthDto.class);
    }

    @Override
    public List<CommunityDataDayVisitDto> getCommunityDataDayVisitors(Date date) {
        date = DateUtils.addDays(date, -2);
        String timeDay = DateHelper.dateToStr(date, DateHelper.FORMAT_YYYY_MM_DD);
        String sql = "select to_char(date(EventTime))   date,\n" +
                "       INSTITUTION_ID as          institutionId,\n" +
                "       INSTITUTION_NAME           institutionName,\n" +
                "       count(distinct StdTradeID) DAU\n" +
                "from (select EventTime, infocode, StdTradeID\n" +
                "      from SPARK.APP_FACT_EVENT_BASIC_ALL_SYN\n" +
                "      where eventname = 'applt.ltxq.ltypg.show'\n" +
                "        and date(EventTime) between ? and ?) a\n" +
                "         join content.GroupInfoModel_Para_ALL_DRCT b\n" +
                "              on a.InfoCode = b.GroupId\n" +
                "         left join emuser.CONSULTANT_SERVICE_USER_ROLE_BASIC_SYN_ALL c\n" +
                "                   on b.GroupOwner = c.PASSPORT_ID\n" +
                "group by 1, 2, 3;";
        List<Map> maps = app.getVerticaRead().executeQuery(sql, Arrays.asList(timeDay, timeDay));
        if (CollectionUtils.isEmpty(maps)) {
            return new ArrayList<>();
        }
        return JSON.parseArray(JSON.toJSONString(maps), CommunityDataDayVisitDto.class);
    }

    @Override
    public List<CommunityDataDaysOfMouthDto> getCommunityDataDaysOfMouth(Date date) {
        date = DateUtils.addDays(date, -2);
        String timeDay = DateHelper.dateToStr(date, DateHelper.FORMAT_YYYY_MM_DD);
        String sql = "select to_char(DATETIME)                                date,\n" +
                "       INSTITUTION_ID                                   institutionId,\n" +
                "       INSTITUTION_NAME                                 institutionName,\n" +
                "       round(sum(days) / count(distinct StdTradeID), 2) DAU\n" +
                "from (select a.DATETIME, INSTITUTION_NAME, StdTradeID, INSTITUTION_ID, COUNT(DISTINCT DATE(b.EventTime)) days\n" +
                "      from fund.TRADEDATE_BASIC_ALL_SYN a\n" +
                "               left join\n" +
                "           (select StdTradeID, EventTime, INSTITUTION_NAME, INSTITUTION_ID\n" +
                "            from (select EventTime, infocode, StdTradeID\n" +
                "                  from SPARK.APP_FACT_EVENT_BASIC_ALL_SYN\n" +
                "                  where eventname = 'applt.ltxq.ltypg.show') a\n" +
                "                     join content.GroupInfoModel_Para_ALL_DRCT b\n" +
                "                          on a.InfoCode = b.GroupId\n" +
                "                     left join emuser.CONSULTANT_SERVICE_USER_ROLE_BASIC_SYN_ALL c\n" +
                "                               on b.GroupOwner = c.PASSPORT_ID\n" +
                "            where eventtime > date (?) - 31\n" +
                "              and eventtime <= date (?)) b\n" +
                "           on a.DATETIME > b.EventTime and a.DATETIME < date(b.EventTime) + 30\n" +
                "      where a.DATETIME between ? and ?\n" +
                "      group by 1, 2, 3, 4) t\n" +
                "group by 1, 2, 3;";
        List<Map> maps = app.getVerticaRead().executeQuery(sql, Arrays.asList(timeDay, timeDay,timeDay, timeDay));
        if (CollectionUtils.isEmpty(maps)) {
            return new ArrayList<>();
        }
        return JSON.parseArray(JSON.toJSONString(maps), CommunityDataDaysOfMouthDto.class);
    }

    @Override
    public List<CommunityDataDayTradeDto> getCommunityDataDayTreade(Date now) {
        Date date = DateUtils.addDays(now, -2);
        String timeDay = DateHelper.dateToStr(date, DateHelper.FORMAT_YYYY_MM_DD);
        String sql = "with t1 as (select to_char(date(a.C_APPTIME)) 日期\n" +
                "                 , INSTITUTION_ID\n" +
                "                 , INSTITUTION_NAME           机构名\n" +
                "                 , sum(a.C_APPAMOUNT)         当日交易金额\n" +
                "            from BUSIN.TB_BUSINAPP_BASIC_NEW_SYN a\n" +
                "                     join (select a.StdTradeID, INSTITUTION_ID, INSTITUTION_NAME, EventTime, FCODE\n" +
                "                           from (select infocode, StdTradeID, EventTime\n" +
                "                                 from SPARK.APP_FACT_EVENT_BASIC_ALL_SYN\n" +
                "                                 where eventname = 'applt.ltxq.ltypg.show'\n" +
                "                                   and date(EventTime) between ? and ?) a\n" +
                "                                    join content.GroupInfoModel_Para_ALL_DRCT b\n" +
                "                                         on a.InfoCode = b.GroupId\n" +
                "                                    left join emuser.CONSULTANT_SERVICE_USER_ROLE_BASIC_SYN_ALL c\n" +
                "                                              on b.GroupOwner = c.PASSPORT_ID\n" +
                "                                    left join fund.JBXX_BASIC_ALL_SYN d\n" +
                "                                              on c.INSTITUTION_ID = d.JJGSID) t\n" +
                "                          on a.C_CUSTOMERNO = t.StdTradeID and a.C_FUNDCODE = t.FCODE and\n" +
                "                             a.C_APPTIME <= t.EventTime + INTERVAL '60 minutes'\n" +
                "            where 1 = 1\n" +
                "              and date(C_APPTIME) between ? and ?\n" +
                "              and C_TRANSACTIONACCOUNTID not in\n" +
                "                  (select distinct C_TRANSACTIONACCOUNTID from tgzh.tg_transactionaccount_decode_med_app)--排除投顾\n" +
                "              and (C_SUBACCOUNTNO is null or C_SUBACCOUNTNO not in (select SUBACCOUNTNO\n" +
                "                                                                    from TGZH.ZZH_SUBACCOUNTINFO_BASIC_ALL_SYN\n" +
                "                                                                    where FOLLOWEDSUBACCOUNTNO is not null))--排除跟投组合\n" +
                "              and C_CUSTOMERNO not in\n" +
                "                  (select C_CUSTOMERNO from EMUSER.TB_CUSTOMERDETAIL_BASIC_ALL_SYN where nvl(C_INDORINS, '0') != '0')--排除机构\n" +
                "              and C_REFERENCE = '2'\n" +
                "              and C_APPSTATE in ('10', '11')\n" +
                "              and C_BUSINTYPE in ('20', '22', '39')\n" +
                "            group by 1, 2, 3\n" +
                "            order by to_char(date(a.C_APPTIME)) desc)\n" +
                "\n" +
                "   , t2 as (select to_char(date(TRADETIME)) pdate, INSTITUTION_ID, INSTITUTION_NAME 机构名, sum(TRADEAMOUNT) TRADEAMOUNT\n" +
                "            from ACTVT.USERGIFT_ULTIMATE_BASIC_APP_NEW a\n" +
                "                     inner join (select date(eventtime)                                                      pdate,\n" +
                "                                        infocode,\n" +
                "                                        substr(regexp_substr(eventparameter, '\"location\":\"[^\"]+', 1, 1), 13) location\n" +
                "                                 from spark.APP_FACT_EVENT_BASIC_ALL_SYN\n" +
                "                                 where EventName = 'applt.kqlq.lqkq.click'\n" +
                "                                   and date(EventTime) between ? and ?\n" +
                "                                 order by EventTime desc) b\n" +
                "                                on date(a.CREATETIME) = b.pdate and a.GIFTID = b.location\n" +
                "                     join content.GroupInfoModel_Para_ALL_DRCT c\n" +
                "                          on b.InfoCode = c.GroupId\n" +
                "                     left join emuser.CONSULTANT_SERVICE_USER_ROLE_BASIC_SYN_ALL d\n" +
                "                               on c.GroupOwner = d.PASSPORT_ID\n" +
                "            where date(TRADETIME) between ? and ?\n" +
                "              and CUSTOMERNO not in (select StdTradeID\n" +
                "                                     from SPARK.APP_FACT_EVENT_BASIC_ALL_SYN\n" +
                "                                     where eventname = 'applt.ltxq.ltypg.show'\n" +
                "                                       and date(EventTime) between ? and ?)\n" +
                "            group by INSTITUTION_ID, INSTITUTION_NAME, date(TRADETIME))\n" +
                "\n" +
                "select t1.日期                                                                                       date,\n" +
                "       t1.INSTITUTION_ID                                                                             institutionId,\n" +
                "       t1.机构名                                                                                     institutionName,\n" +
                "       case when TRADEAMOUNT is not null then t1.当日交易金额 + TRADEAMOUNT  else t1.当日交易金额 end amount\n" +
                "from t1\n" +
                "         left join t2 on t1.日期 = t2.pdate and t1.机构名 = t2.机构名;";
        List<Map> maps = app.getVerticaRead().executeQuery(sql, Arrays.asList(timeDay, timeDay, timeDay, timeDay, timeDay, timeDay, timeDay, timeDay, timeDay, timeDay));
        if (CollectionUtils.isEmpty(maps)) {
            return new ArrayList<>();
        }
        return JSON.parseArray(JSON.toJSONString(maps), CommunityDataDayTradeDto.class);
    }

    @Override
    public List<CommunityDataDayGroupUserDto> getCommunityDataGroupUser(Date now) {
        Date date = DateUtils.addDays(now, -2);
        String timeDay = DateHelper.dateToStr(date, DateHelper.FORMAT_YYYY_MM_DD);
        String sql = "with tmp1 as\n" +
                "    (select GroupId, GroupName, GroupOwner, GroupState, IsFull, MaxMemberCount, Managers\n" +
                "     from CONTENT.GroupInfoModel_Para_ALL_DRCT\n" +
                "     group by GroupId, GroupName, GroupOwner, GroupState, IsFull, MaxMemberCount, Managers)\n" +
                "\n" +
                "   , tmp3 as\n" +
                "    (select date(a.SendDate)       日期,\n" +
                "            a.GroupId              群ID,\n" +
                "            b.GroupName            群名称,\n" +
                "            IsFull                 群是否已满员,\n" +
                "            MaxMemberCount         群员上限值,\n" +
                "            count(distinct Sender) 发消息人数,\n" +
                "            count(distinct MsgID)  消息条数\n" +
                "     from CONTENT.DBGroupMessage_Basic_ALL_SYN a\n" +
                "              left join tmp1 b\n" +
                "                        on a.GroupId = b.GroupId\n" +
                "     where date(a.SendDate) between ? and ?\n" +
                "       AND GroupName IS NOT NULL\n" +
                "     group by date(a.SendDate), a.GroupId, b.GroupName, case\n" +
                "                                                            when GroupState = '1' then '已启用'\n" +
                "                                                            when GroupState = '8' then '禁用'\n" +
                "                                                            when GroupState = '16' then '解散' end, IsFull,\n" +
                "              MaxMemberCount)\n" +
                "   , tmp4 as\n" +
                "    (select date(CALCDAY)                                         日期,\n" +
                "            INFOCODE,\n" +
                "            sum(EVENTNUM)                                         访问PV,\n" +
                "            sum(VISITNUM)                                         访问UV,\n" +
                "            cast(sum(EVENTNUM) / sum(VISITNUM) as decimal(10, 2)) 人均访问次数\n" +
                "     from BEHAVIOR.T_APP_HUB_EVENT_INFOCODE_DAY_STAT_ALL_SYN\n" +
                "     where eventname in ('applt.ltxq.ltypg.show')\n" +
                "       and CALCDAY between ? and ?\n" +
                "--and ({filter})\n" +
                "     group by date(CALCDAY), INFOCODE)\n" +
                "select to_char(a.日期, 'yyyy-mm-dd') date,\n" +
                "       f.INSTITUTION_ID              institutionId,\n" +
                "       f.INSTITUTION_NAME            institutionName,\n" +
                "       sum(c.发消息人数)             speakUserNum,\n" +
                "       sum(c.消息条数)               speakTotal\n" +
                "from tmp4 a\n" +
                "         left join tmp1 d on a.INFOCODE = d.GroupId\n" +
                "         left join tmp3 c on a.INFOCODE = c.群ID and a.日期 = c.日期\n" +
                "         left join emuser.CONSULTANT_SERVICE_USER_ROLE_BASIC_SYN_ALL f\n" +
                "                   on d.GroupOwner = f.PASSPORT_ID\n" +
                "where f.INSTITUTION_ID is not null\n" +
                "group by to_char(a.日期, 'yyyy-mm-dd'), f.INSTITUTION_NAME, f.INSTITUTION_ID\n" +
                "order by to_char(a.日期, 'yyyy-mm-dd') desc;";
        List<Map> maps = app.getVerticaRead().executeQuery(sql, Arrays.asList(timeDay, timeDay, timeDay, timeDay));
        if (CollectionUtils.isEmpty(maps)) {
            return new ArrayList<>();
        }
        return JSON.parseArray(JSON.toJSONString(maps), CommunityDataDayGroupUserDto.class);

    }

    @Override
    public List<CommunityDataDayGroupUserInOutDto> getCommunityDataGroupUserInOut(Date now) {
        Date date = DateUtils.addDays(now, -2);
        String timeDay = DateHelper.dateToStr(date, DateHelper.FORMAT_YYYY_MM_DD);
        String sql = "select date(OPERTIME)                                                   date\n" +
                "     , INSTITUTION_ID                                                   institutionId\n" +
                "     , INSTITUTION_NAME                                                 institutionName\n" +
                "     , count(case when msgtype = 1 then USERUID else null end) inNum\n" +
                "     , count(case when msgtype = 2 then USERUID else null end) outNum\n" +
                "from CONTENT.Fund_Syncdata_Basic_ALL_SYN a\n" +
                "         left join content.GroupInfoModel_Para_ALL_DRCT b\n" +
                "                   on a.GROUPID = b.GroupId\n" +
                "         left join emuser.CONSULTANT_SERVICE_USER_ROLE_BASIC_SYN_ALL d\n" +
                "                   on b.GroupOwner = d.PASSPORT_ID\n" +
                "where date(OPERTIME) between ? and ?\n" +
                "group by date(OPERTIME), INSTITUTION_NAME, INSTITUTION_ID;";
        List<Map> maps = app.getVerticaRead().executeQuery(sql, Arrays.asList(timeDay, timeDay));
        if (CollectionUtils.isEmpty(maps)) {
            return new ArrayList<>();
        }
        return JSON.parseArray(JSON.toJSONString(maps), CommunityDataDayGroupUserInOutDto.class);
    }

    @Override
    public List<CommunityDataDayGroupUserNumDto> getCommunityDataGroupUserNum(Date now) {
        Date date = DateUtils.addDays(now, -2);
        String timeDay = DateHelper.dateToStr(date, DateHelper.FORMAT_YYYY_MM_DD);
        String sql = "select datetime                                                                  date,\n" +
                "       INSTITUTION_ID                                                            institutionId,\n" +
                "       INSTITUTION_NAME                                                          institutionName,\n" +
                "       SUM(CASE WHEN MSGTYPE = '1' THEN 1 WHEN MSGTYPE = '2' THEN -1 ELSE 0 END) groupUserNum\n" +
                "from fund.TRADEDATE_BASIC_ALL_SYN a\n" +
                "         left join content.Fund_Syncdata_Basic_ALL_SYN b\n" +
                "                   on a.DATETIME > b.OPERTIME\n" +
                "         left join CONTENT.GroupInfoModel_Para_ALL_DRCT d on b.GROUPID = d.GroupId\n" +
                "         left join emuser.CONSULTANT_SERVICE_USER_ROLE_BASIC_SYN_ALL e\n" +
                "                   on d.GroupOwner = e.PASSPORT_ID\n" +
                "where a.DATETIME <= date(sysdate)\n" +
                "  and DATETIME > '2021-01-01'\n" +
                "  and INSTITUTION_ID is not null\n" +
                "  and datetime = ?\n" +
                "group by datetime, INSTITUTION_ID, INSTITUTION_NAME;";
        List<Map> maps = app.getVerticaRead().executeQuery(sql, Arrays.asList(timeDay));
        if (CollectionUtils.isEmpty(maps)) {
            return new ArrayList<>();
        }
        return JSON.parseArray(JSON.toJSONString(maps), CommunityDataDayGroupUserNumDto.class);
    }

    @Override
    public boolean delByDayAndInsertData(String delSql, String insertSql, Date dt, List<List<Object>> parameters) {
        boolean result = false;
        Connection connection = null;
        PreparedStatement delStatement = null;
        PreparedStatement insertStatement = null;
        try {
            connection = app.verticaTrade.getconn();
            delStatement = connection.prepareStatement(delSql);
            insertStatement = connection.prepareStatement(insertSql);
            connection.setAutoCommit(false);
            delStatement.setObject(1, dt);
            delStatement.execute();

            for (int i = 0; i < parameters.size(); i++) {
                List<Object> parameterList = parameters.get(i);
                for (int i2 = 0; i2 < parameterList.size(); i2++) {
                    insertStatement.setObject(i2 + 1, parameterList.get(i2));
                }
                // 添加批处理SQL
                insertStatement.addBatch();
                // 每10000条执行一次，避免内存不够的情况
                if (i > 0 && i % BATCH_NUM == 0) {
                    insertStatement.executeBatch();
                    insertStatement.clearBatch();
                }
            }
            // 最后执行剩余不足10000条的
            insertStatement.executeBatch();
            // 执行完后，手动提交事务
            connection.commit();
            // 在把自动提交打开
            connection.setAutoCommit(true);
            result = true;
        } catch (Exception e) {
            log.error("SQL:{}，按照日期删除再插入数据出现异常，{},{}", delSql,e.getMessage(), e);
        } finally {
            if (delStatement != null) {
                try {
                    delStatement.close();
                } catch (SQLException e) {
                    log.error("delStatement error");
                }
            }
            if (connection != null) {
                app.verticaTrade.closeConn(connection, insertStatement, null);
            }
        }
        return result;
    }

    /**
     * 按照给定交易日期执行sql统计数据
     * @param transactionDate 交易日期
     * @return
     */
    @Override
    public List<FundApplyDataStatModel> statTradeData(Date transactionDate) {
        List<Object> paramList = new ArrayList<>();
        paramList.add(transactionDate);
        paramList.add(transactionDate);
        paramList.add(transactionDate);
        List<Map> mapList = app.verticaTrade.executeQuery(VerticaSqlConstant.SQL_STAT_TRADE_DETAIL, paramList);
        List<FundApplyDataStatModel> result = null;
        if (!CollectionUtils.isEmpty(mapList)) {
            result = JSON.parseArray(JSON.toJSONString(mapList), FundApplyDataStatModel.class);
        }
        return result;
    }

    @Override
    public boolean insertTradeDetails(List<TradeDetailRecord> models) {
        if (CollectionUtils.isEmpty(models)) {
            return false;
        }
        List<List<Object>> insertList = CFHTradeDataRelocateCommonService.getInsertTradeDataParamList(models);
        boolean flag = app.verticaTrade.executeBetchUpdate(VerticaSqlConstant.SQL_INSERT_CFH_TRADE_DETAIL, insertList);
        if (flag) {
            log.info("插入了{}条数据到表{}中",insertList.size(), VerticaTableNameConstant.TTTRADEDETAIL_BASIC_SYN);
            XxlJobLogger.log("插入了{}条数据到表{}中",insertList.size(), VerticaTableNameConstant.TTTRADEDETAIL_BASIC_SYN);
        }
        return flag;
    }

    @Override
    public int selectReferencefundDayRun() {
        String sql = "SELECT count(1) as count\n" +
                "FROM BACKSTAGE.KETTLE_UPDATE_RECORD\n" +
                "where upper(tablename) = upper('TTJJ_REFERENCEFUND_STAT_SYN_ALL')\n" +
                "  and CalendarDate = trunc(sysdate)";
        List<Map> maps = app.getVerticaRead().executeQuery(sql, null);
        if (!CollectionUtils.isEmpty(maps)) {
            return Integer.parseInt(maps.get(0).get("count").toString());
        }
        return 0;
    }

    @Override
    public Date selectCalDay() {
        String sql = "select lastday lasttdate from fund.TRADEDATE_BASIC_ALL_SYN where datetime = trunc(sysdate);";
        List<Map> maps = app.getVerticaRead().executeQuery(sql, null);
        if (!CollectionUtils.isEmpty(maps)) {
            return DateHelper.stringToDate2(maps.get(0).get("lasttdate").toString(), DateHelper.FORMAT_YYYY_MM_DD);
        }
        return null;
    }

    @Override
    public List<ReferenceFundDayDTO> selectReferenceFundDayData(Date calDay) {
        String sql = "select case when a.busintype in ('122','120') then 1 \n" +
                "when a.busintype ='139' then  2  when a.busintype='124' then 3 end BUSINTYPE\n" +
                ", a.fundcode FUNDCODE\n" +
                ",b.shortname SHORTNAME,\n" +
                "decode(b.fundtype,'001','股票','002','混合','003','债券','008','债券','004','货币','005','货币','007','QDII','其它') FUNDTYPE\n" +
                ",a.transactioncfmdate PDATE ,b.jjgsid JJGSID,\n" +
                "sum(a.cfmsamount) CFMSAMOUNT,sum(a.cfmsnum) CFMSNUM \n" +
                ", round(sum(a.cfmsamount)/sum(a.cfmsnum),2) PERNUM\n" +
                "from  busin.TTJJ_REFERENCEFUND_STAT_SYN_ALL a \n" +
                "inner join  fund.CODEALL_ALL_BASIC_APP  b  on a.FundCode =b.fcode \n" +
                "where transactioncfmdate=?   and orgtypename='个人'\n" +
                "group by case when a.busintype in ('122','120') then 1 \n" +
                "when a.busintype ='139' then  2  when a.busintype='124' then 3 end, \n" +
                "a.fundcode,b.shortname,b.fundtype,a.transactioncfmdate,b.jjgsid";
        List<Map> maps = app.getVerticaRead().executeQuery(sql, Arrays.asList(calDay));
        if (!CollectionUtils.isEmpty(maps)) {
            return JSON.parseArray(JSON.toJSONString(maps), ReferenceFundDayDTO.class);
        }
        return null;
    }

    @Override
    public List<ReferenceFundDayDTO> selectReferenceFundJJGSData(Date calDay) {
        String sql = "WITH TMP AS (select case\n" +
                "                        when a.busintype in ('122', '120') then 1\n" +
                "                        when a.busintype = '139' then 2\n" +
                "                        when a.busintype = '124' then 3 end      BUSINTYPE,\n" +
                "                    m.CLASS1_VAL                                 FUNDTYPE,\n" +
                "                    a.transactioncfmdate                         PDATE,\n" +
                "                    b.jjgsid                                     JJGSID,\n" +
                "                    sum(a.cfmsamount)                            cfmsamount,\n" +
                "                    sum(a.cfmsnum)                               CFMSNUM,\n" +
                "                    round(sum(a.cfmsamount) / sum(a.cfmsnum), 2) PERNUM\n" +
                "             from busin.TTJJ_REFERENCEFUND_STAT_SYN_ALL a\n" +
                "                      inner join fund.CODEALL_ALL_BASIC_APP b on a.FundCode = b.fcode\n" +
                "                      inner join fund.RSBTYPE_ENUMRELATION_BASIC_SYN_ALL m on b.rsbtype = m.CLASS2_CODE\n" +
                "             where transactioncfmdate = ?\n" +
                "               and orgtypename = '个人'\n" +
                "             group by case\n" +
                "                          when a.busintype in ('122', '120') then 1\n" +
                "                          when a.busintype = '139' then 2\n" +
                "                          when a.busintype = '124' then 3 end, m.CLASS1_VAL,\n" +
                "                      a.transactioncfmdate, b.jjgsid)\n" +
                "\n" +
                "SELECT BUSINTYPE, PDATE, JJGSID, FUNDTYPE, cfmsamount CFMSAMOUNT, CFMSNUM, PERNUM\n" +
                "FROM TMP\n" +
                "UNION\n" +
                "SELECT busintype,\n" +
                "       PDATE,\n" +
                "       JJGSID,\n" +
                "       '全部',\n" +
                "       sum(cfmsamount)                          cfmsamount,\n" +
                "       sum(cfmsnum)                             cfmsnum,\n" +
                "       round(sum(cfmsamount) / sum(cfmsnum), 2) pernum\n" +
                "FROM TMP\n" +
                "GROUP BY busintype, PDATE, JJGSID;";
        List<Map> maps = app.getVerticaRead().executeQuery(sql, Arrays.asList(calDay));
        if (!CollectionUtils.isEmpty(maps)) {
            return JSON.parseArray(JSON.toJSONString(maps), ReferenceFundDayDTO.class);
        } else {
            return null;
        }

    }

    @Override
    public List<ReferenceFundDayDTO> selectReferenceFundMonthData(Date calDay) {
        String sql = "select case when a.busintype in ('122','120') then 1 \n" +
                "when a.busintype ='139' then  2  when a.busintype='124' then 3 end BUSINTYPE\n" +
                ",to_char(a.transactioncfmdate,'yyyy-mm') PDATESTR ,b.jjgsid JJGSID,\n" +
                "sum(a.cfmsamount) CFMSAMOUNT,sum(a.cfmsnum) CFMSNUM , round(sum(a.cfmsamount)/sum(a.cfmsnum),2) PERNUM\n" +
                "from busin.TTJJ_REFERENCEFUND_STAT_SYN_ALL  a  inner join fund.CODEALL_ALL_BASIC_APP  b  on a.FundCode =b.fcode \n" +
                "where transactioncfmdate=?   and orgtypename='个人'\n" +
                "group by case when a.busintype in ('122','120') then 1 \n" +
                "when a.busintype ='139' then  2  when a.busintype='124' then 3 end, \n" +
                "to_char(a.transactioncfmdate,'yyyy-mm'),b.jjgsid";
        List<Map> maps = app.getVerticaRead().executeQuery(sql, Arrays.asList(calDay));
        if (!CollectionUtils.isEmpty(maps)) {
            return JSON.parseArray(JSON.toJSONString(maps), ReferenceFundDayDTO.class);
        } else {
            return null;
        }
    }

    @Override
    public int selectInterfaceFundDayRun() {

        String sql = "SELECT count(1) as count  FROM    BACKSTAGE.KETTLE_UPDATE_RECORD\n" +
                " where upper(tablename) in ('USER_FCODE_FIN_STAT_ALL_APP', 'STTJJ_CUSTFUNDPOSIDETAILS_BASIC_ALL_SYN',\n" +
                " 'STTJJ_INTERFACEFUND_STAT_ALL_SYN','STTJJ_INTERFACEJJGS_STAT_ALL_SYN')\n" +
                " \tand CalendarDate =trunc(sysdate)\n" +
                " \t  having count(1)=4  ";
        List<Map> maps = app.getVerticaRead().executeQuery(sql, null);
        if (!CollectionUtils.isEmpty(maps)) {
            return Integer.parseInt(maps.get(0).get("count").toString());
        }
        return 0;
    }

    @Override
    public List<InterfaceFundDayDTO> selectInterfaceFundDay(Date calDay) {
        String sql = "select a.fundcode FUNDCODE,b.shortname SHORTNAME,b.jjgsid JJGSID, date '%s' PDATE,\n" +
                "a.posivol_amt POSIVOL_AMT,a.allnum ALLNUM,\n" +
                "a.holdnum HOLDNUM,a.allnum-a.holdnum ZERONUM,nvl(c.dayzeroold,0) DAYZEROOLD,\n" +
                "c.firstnum FIRSTNUM,case when a.holdnum =0 then null else round(a.posivol_amt/a.holdnum,2) end PERNUM,a.holdnum - nvl(c.firstnum,0)- nvl(c.dayzeroold,0) STOCKNUM ,a.posivol POSIVOL\n" +
                "from (\n" +
                "select ttjj_fundcode fundcode,sum(ttjj_posi_amt) posivol_amt, sum(ttjj_posi_vol) posivol, count(1) allnum,\n" +
                "count( case when ttjj_iscurposi=1 then 1 end) holdnum\n" +
                "from RETENTION.STTJJ_CUSTFUNDPOSIDETAILS_BASIC_ALL_SYN\n" +
                "where ttjj_custid not in (select C_CUSTOMERNO from emuser.TB_CUSTOMERDETAIL_BASIC_ALL_SYN\n" +
                "where C_INDORINS in ('1','2') and C_ISDEL =0)\n" +
                "group by ttjj_fundcode)a\n" +
                "inner join fund.CODEALL_ALL_BASIC_APP b on a.fundcode=b.fcode\n" +
                "left join fund.sttjj_interfacefund_stat_all_syn c on a.fundcode=c.fcode\n" +
                "and c.pdate=?;";
        List<Map> maps = app.getVerticaRead().executeQuery(String.format(sql, TimeUtil.dateToStr(calDay, TimeUtil.FORMAT_YYYY_MM_DD)), Arrays.asList(calDay));
        if (!CollectionUtils.isEmpty(maps)) {
            return JSON.parseArray(JSON.toJSONString(maps), InterfaceFundDayDTO.class);
        }
        return new ArrayList<>();
    }

    @Override
    public List<InterfaceFundDayDTO> selectInterfaceFundJJGS(Date calDay) {
        String sql = "WITH TMP AS (\n" +
                "select a.fcode FUNDCODE ,JJGSID, b.CLASS1_VAL fundtype  from fund.CODEALL_ALL_BASIC_APP a\n" +
                "inner join fund.RSBTYPE_ENUMRELATION_BASIC_SYN_ALL b on a.rsbtype =b.CLASS2_CODE\n" +
                " where jjgsid is not null\n" +
                "),tmp1 as\n" +
                "(\n" +
                "select b.jjgsid,b.fundtype,sum(ttjj_posi_amt) posivol_amt , sum(ttjj_posi_vol) posivol\n" +
                "from RETENTION.STTJJ_CUSTFUNDPOSIDETAILS_BASIC_ALL_SYN a inner join tmp b on a.ttjj_fundcode=b.fundcode\n" +
                "where ttjj_custid not in (select C_CUSTOMERNO from emuser.TB_CUSTOMERDETAIL_BASIC_ALL_SYN\n" +
                "where C_INDORINS in ('1','2') and C_ISDEL =0)\n" +
                "group by b.jjgsid,b.fundtype\n" +
                "),\n" +
                "tmp2 as (\n" +
                "select b.jjgsid, count(distinct a.ttjj_custid ) allnum,\n" +
                "count( distinct case when a.ttjj_iscurposi=1 then a.ttjj_custid end) holdnum ,sum(ttjj_posi_amt) posivol_amt , sum(ttjj_posi_vol) posivol\n" +
                "from RETENTION.STTJJ_CUSTFUNDPOSIDETAILS_BASIC_ALL_SYN a inner join tmp b on a.ttjj_fundcode=b.fundcode\n" +
                "where ttjj_custid not in (select C_CUSTOMERNO from emuser.TB_CUSTOMERDETAIL_BASIC_ALL_SYN\n" +
                "where C_INDORINS in ('1','2') and C_ISDEL =0)\n" +
                "group by b.jjgsid\n" +
                ")\n" +
                "select a.jjgsid JJGSID,'全部' FUNDTYPE,date '%s' PDATE,a.posivol_amt POSIVOL_AMT,a.holdnum HOLDNUM,a.allnum-a.holdnum ZERONUM,\n" +
                "nvl(b.dayzeroold,0) DAYZEROOLD,b.firstnum FIRSTNUM,case when a.holdnum =0 then null else round(a.posivol_amt/a.holdnum,2) end\n" +
                "PERNUM,a.holdnum - nvl(b.firstnum,0)- nvl(b.dayzeroold,0) STOCKNUM,a.posivol POSIVOL\n" +
                "from tmp2 a\n" +
                "left join fund.sttjj_interfacejjgs_stat_all_syn b on a.jjgsid=b.jjgsid AND B.PDATE=?\n" +
                "union all\n" +
                "select jjgsid,fundtype,date '%s' pdate,posivol_amt,null holdnum ,null zeronum,null dayzeroold,null firstnum,null pernum,null STOCKNUM,posivol\n" +
                "from tmp1;";
        List<Map> maps = app.getVerticaRead().executeQuery(String.format(sql, TimeUtil.dateToStr(calDay, TimeUtil.FORMAT_YYYY_MM_DD), TimeUtil.dateToStr(calDay, TimeUtil.FORMAT_YYYY_MM_DD))
                , Arrays.asList(calDay));

        if (!CollectionUtils.isEmpty(maps)) {
            return JSON.parseArray(JSON.toJSONString(maps), InterfaceFundDayDTO.class);
        }
        return new ArrayList<>();
    }

    @Override
    public List<InterfaceFundDayDTO> selectSql(String sql) {
        if (StringUtils.isEmpty(sql)) {
            return new ArrayList<>();
        }
        List<Map> maps = app.getVerticaRead().executeQuery(sql, null);
        if (!CollectionUtils.isEmpty(maps)) {
            return JSON.parseArray(JSON.toJSONString(maps), InterfaceFundDayDTO.class);
        }
        return new ArrayList<>();
    }
    @Override
    public int selectRun(String sql) {
        if (StringUtils.isNotEmpty(sql)) {
            List<Map> maps = app.getVerticaRead().executeQuery(sql, null);
            if (!CollectionUtils.isEmpty(maps)) {
                return Integer.parseInt(maps.get(0).get("count").toString());
            }
        }
        return 0;
    }

    @Override
    public Date getLiveScoreFieldsSyncMaxLastUpdateTime(Date breakPoint) {
        String sql = "select max(updatetime) updatetime\n" +
                "from (select updatetime\n" +
                "      from content.LIVE_TB_SHUYUCALC_STAT_ALL_DRCT\n" +
                "      union\n" +
                "      select updatetime\n" +
                "      from content.LIVE_TB_LIVESCORE_STAT_ALL_APP\n" +
                "      union\n" +
                "      select updatetime\n" +
                "      from content.TB_LIVE_HEAT_DEGREE_STAT_ALL_DRCT\n" +
                "      union\n" +
                "      select updatetime\n" +
                "      from content.TB_LIVEBROADCAST_BASIC_ALL_SYN\n" +
                "      union\n" +
                "      select updatetime\n" +
                "      from content.LIVESALE_STAT_ALL_APP) A\n" +
                "where updatetime > ?;";
        List<Map> maps = app.getVerticaRead().executeQuery(sql, Arrays.asList(breakPoint));
        if (!CollectionUtils.isEmpty(maps)) {
            LiveScoreFieldsSyncUpdateTime dto = JSON.parseObject(JSON.toJSONString(maps.get(0)), LiveScoreFieldsSyncUpdateTime.class);
            if (dto != null && dto.getUpdatetime() != null) {
                return dto.getUpdatetime();
            }
        }
        return null;
    }

    @Override
    public List<LiveScoreFieldsSynModel> getLiveScoreFields(Date breakPoint) {
        String sql = "SELECT A.LIVEID,\n" +
                "       c.Channel_ID,\n" +
                "       D.STARTTIME,\n" +
                "       D.ENDTIME,\n" +
                "       D.TITLE,\n" +
                "       D.CFHID,\n" +
                "       E.ACCOUNTNAME         AS CFHNAME,\n" +
                "       STATUS,\n" +
                "       ISDELETE,\n" +
                "       ISHIDE,\n" +
                "       C.HEAT_DEGREE,\n" +
                "       C.LIKE_NUM,\n" +
                "       A.CLICKNUM * 2.01     AS CLICKNUM,\n" +
                "       A.CLICKUSERNUM * 2.01 AS CLICKUSERNUM,\n" +
                "       A.REPLYUSERNUM,\n" +
                "       A.AVGDURATION,\n" +
                "       case when A.CLICKUSERNUM != 0 then A.FUNDCLICKUSERNUM / A.CLICKUSERNUM end\n" +
                "                             AS FUNDCLICKRATIO,\n" +
                "       case when A.CLICKUSERNUM != 0 then A.SHAREUSERNUM / A.CLICKUSERNUM end\n" +
                "                             AS SHARERATIO,\n" +
                "       case when A.CLICKUSERNUM != 0 then A.FOLLOWUSERNUM / A.CLICKUSERNUM end\n" +
                "                             AS FOLLOWRATIO,\n" +
                "       case when A.CLICKUSERNUM != 0 then A.REPLYUSERNUM / A.CLICKUSERNUM end\n" +
                "                             AS REPLYRATIO,\n" +
                "       B.TOTAL_SCORE,\n" +
                "       B.WATCH_SCORE,\n" +
                "       B.INTER_SCORE,\n" +
                "       B.FUND_SCORE,\n" +
                "       B.UPDATETIME          AS LIVESCORE_UPDATETIME,\n" +
                "       f.FavorCustNum,\n" +
                "       f.PlanNum,\n" +
                "       f.tinusernum,\n" +
                "       f.tinamt,\n" +
                "       f.updatetime          AS LIVESALE_UPDATETIME\n" +
                "FROM content.LIVE_TB_SHUYUCALC_STAT_ALL_DRCT A\n" +
                "         LEFT JOIN\n" +
                "     content.LIVE_TB_LIVESCORE_STAT_ALL_APP B ON A.LIVEID = B.LIVEID\n" +
                "         LEFT JOIN\n" +
                "     content.TB_LIVE_HEAT_DEGREE_STAT_ALL_DRCT C ON A.LIVEID = C.SHOW_ID\n" +
                "         LEFT JOIN\n" +
                "     content.TB_LIVEBROADCAST_BASIC_ALL_SYN D ON A.LIVEID = D.C_ID\n" +
                "         LEFT JOIN\n" +
                "     content.CFH_FOLLOW_BASIC_ALL_SYN E ON D.CFHID = E.ACCOUNTID\n" +
                "         LEFT JOIN\n" +
                "     (SELECT liveid,\n" +
                "             SUM(FavorCustNum) AS FavorCustNum,\n" +
                "             SUM(PlanNum)      AS PlanNum,\n" +
                "             MAX(updatetime)   AS updatetime,\n" +
                "             sum(TradeInCustNum)  tinusernum,\n" +
                "             sum(TradeInAmt)      tinamt\n" +
                "      FROM content.LIVESALE_STAT_ALL_APP\n" +
                "      WHERE DATEDIFF(dd, LiveStartTime, visitdate) <= 2\n" +
                "      GROUP BY liveid) f ON a.liveid = f.liveid\n" +
                "WHERE A.UPDATETIME > ?\n" +
                "   OR B.UPDATETIME > ?\n" +
                "   OR C.UPDATETIME > ?\n" +
                "   OR D.UPDATETIME > ?\n" +
                "   OR F.UPDATETIME > ?;";
        List<Map> maps = app.getVerticaRead().executeQuery(sql, Arrays.asList(breakPoint, breakPoint, breakPoint, breakPoint, breakPoint));
        if (!CollectionUtils.isEmpty(maps)) {
            return JSON.parseArray(JSON.toJSONString(maps), LiveScoreFieldsSynModel.class);
        }
        return null;
    }

    @Override
    public List<LiveScoreFundSynModel> getLiveScoreFundFields() {
        String sql = "select a.liveid,\n" +
                "       a.fundcode,\n" +
                "       ifnull(b.shortname, c.C_STRATEGYNAME) shortname,\n" +
                "       a.tinusernum,\n" +
                "       a.tinamt,\n" +
                "       a.favornum,\n" +
                "       a.plannum,\n" +
                "       a.maxut\n" +
                "from (select liveid,\n" +
                "             fundcode,\n" +
                "             sum(TradeInCustNum) tinusernum,\n" +
                "             sum(TradeInAmt)     tinamt,\n" +
                "             sum(FavorCustNum)   favornum,\n" +
                "             sum(PlanNum)        plannum,\n" +
                "             max(updatetime)     maxut\n" +
                "      from content.LIVESALE_STAT_ALL_APP\n" +
                "      where datediff(dd, LiveStartTime, visitdate) <= 2\n" +
                "      group by 1, 2) a\n" +
                "         left join fund.CODEALL_ALL_BASIC_APP b on a.fundcode = b.fcode\n" +
                "         left join tgzh.TG_STRATEGY_BASIC_SYN_ALL c on a.FundCode = c.C_DISPLAYID";

        List<Map> maps = app.getVerticaRead().executeQuery(sql, null);
        if (!CollectionUtils.isEmpty(maps)) {
            return JSON.parseArray(JSON.toJSONString(maps), LiveScoreFundSynModel.class);
        }
        return null;
    }

    @Override
    public <T> List<T> selectSql(String sql , Class<T> clazz) {
        if (StringUtils.isEmpty(sql)) {
            return null;
        }
        List<Map> maps = app.getVerticaRead().executeQuery(sql, null);
        if (!CollectionUtils.isEmpty(maps)) {
            return JSON.parseArray(JSON.toJSONString(maps), clazz);
        }
        return null;
    }

    @Override
    public Boolean insertTradeDetail(TradeDetailRecord model) {
        List<Object> paramList = new ArrayList<>(12);
        paramList.add(model.getApplyNo());
        paramList.add(model.getTradeType());
        paramList.add(model.getParentTradeType());
        paramList.add(model.getParentApplyNo());
        paramList.add(model.getTraceNo());
        paramList.add(model.getFundCode());
        paramList.add(model.getApplyAmount());
        paramList.add(model.getApplyVol());
        Date applyTime = model.getApplyTime();
        paramList.add(applyTime);
        paramList.add(model.getTransactionDate());
        paramList.add(applyTime);
        paramList.add(ttfund.web.fortuneservice.utils.DateUtils.getHour(applyTime));
        return app.verticaTrade.execute(VerticaSqlConstant.SQL_INSERT_CFH_TRADE_DETAIL, paramList);
    }

    @Override
    public boolean updateTradeDetailByApplyNo(TradeDetailRecord model) {
        if (model == null || StringUtils.isEmpty(model.getApplyNo())) {
            return false;
        }
        List<Object> paramList = new ArrayList<>(12);
        paramList.add(model.getTradeType());
        paramList.add(model.getParentTradeType());
        paramList.add(model.getParentApplyNo());
        paramList.add(model.getTraceNo());
        paramList.add(model.getFundCode());
        paramList.add(model.getApplyAmount());
        paramList.add(model.getApplyVol());
        Date applyTime = model.getApplyTime();
        paramList.add(applyTime);
        paramList.add(model.getTransactionDate());
        paramList.add(applyTime);
        paramList.add(ttfund.web.fortuneservice.utils.DateUtils.getHour(applyTime));
        paramList.add(model.getApplyNo());
        String sql = "update " + VerticaTableNameConstant.TTTRADEDETAIL_BASIC_SYN +
                " set TRADETYPE=?" +
                ", PARENTTRADETYPE=?" +
                ", PARENTAPPLYNO=?" +
                ", TRACENO=?" +
                ", FUNDCODE=?" +
                ", APPLYAMOUNT=?" +
                ", APPLYVOL=?" +
                ", APPLYTIME=?" +
                ", TRANSACTIONDATE=?" +
                ", APPLYDAY=?" +
                ", APPLYHOUR=?" +
                " where APPLYNO = ?";
        return app.verticaTrade.execute(sql, paramList);
    }

    @Override
    public boolean revertTradeDetailByApplyNo(TradeDetailRecord model) {
        if (model == null || StringUtils.isEmpty(model.getApplyNo())) {
            return false;
        }
        List<Object> paramList = new ArrayList<>(2);
        paramList.add(1);
        paramList.add(model.getApplyNo());
        String sql = "update " + VerticaTableNameConstant.TTTRADEDETAIL_BASIC_SYN + " set  REVOKE=? where APPLYNO = ?";
        return app.verticaTrade.execute(sql, paramList);
    }

    @Override
    public boolean revertTradeDetailByApplyNos(List<TradeDetailRecord> revertList) {
        if (CollectionUtils.isEmpty(revertList)) {
            return false;
        }
        Set<String> applyNos = revertList.stream().filter(o -> StringUtils.isNotEmpty(o.getApplyNo())).map(o -> o.getApplyNo()).collect(Collectors.toSet());
        StrBuilder sql = new StrBuilder("update ").append(VerticaTableNameConstant.TTTRADEDETAIL_BASIC_SYN).append(" set  REVOKE=1 where APPLYNO in (");

        List<Object> paramList = new ArrayList<>(applyNos.size());
        for (String applyNo : applyNos) {
            sql.append("?,");
            paramList.add(applyNo);
        }
        sql.del(sql.length() - 1, sql.length());
        sql.append(")");
        boolean flag = app.verticaTrade.execute(sql.toString(), paramList);
        if (flag) {
            log.info("撤回了{}条数据到表{}中",revertList.size(), VerticaTableNameConstant.TTTRADEDETAIL_BASIC_SYN);
            XxlJobLogger.log("撤回了{}条数据到表{}中",revertList.size(), VerticaTableNameConstant.TTTRADEDETAIL_BASIC_SYN);
        }
        return flag;
    }

    @Override
    public List<String> selectSql(String sql, List<Object> paramList, Class<String> clazz) {
        if (StringUtils.isEmpty(sql)) {
            return new ArrayList<>();
        }
        List<Map> maps = app.getVerticaRead().executeQuery(sql, paramList);
        if (!CollectionUtils.isEmpty(maps)) {
            return JSON.parseArray(JSON.toJSONString(maps), clazz);
        }
        return new ArrayList<>();
    }

    @Override
    public List<CFHTradeRankDataDTO> selectCFHTradeRankData(Date calDay, Date date) {
        String sql = "WITH TMP AS (select a.fcode FUNDCODE, JJGSID, b.CLASS1_VAL fundtype\n" +
                "             from fund.CODEALL_ALL_BASIC_APP a\n" +
                "                      inner join fund.RSBTYPE_ENUMRELATION_BASIC_SYN_ALL b on a.rsbtype = b.CLASS2_CODE\n" +
                "             where jjgsid is not null\n" +
                "               and ptypename <> '高端理财'\n" +
                "             union\n" +
                "             select a.fcode FUNDCODE, JJGSID, '全部' fundtype\n" +
                "             from fund.CODEALL_ALL_BASIC_APP a\n" +
                "             where jjgsid is not null\n" +
                "               and ptypename <> '高端理财'\n" +
                "             union\n" +
                "             select a.fcode FUNDCODE, JJGSID, '非货' fundtype\n" +
                "             from fund.CODEALL_ALL_BASIC_APP a\n" +
                "                      inner join fund.RSBTYPE_ENUMRELATION_BASIC_SYN_ALL b on a.rsbtype = b.CLASS2_CODE\n" +
                "             where jjgsid is not null\n" +
                "               and ptypename <> '高端理财'\n" +
                "               and b.CLASS1_VAL <> '货币型'),\n" +
                "     tmp1 as (select max(datetime) date1\n" +
                "              From fund.TRADEDATE_BASIC_ALL_SYN\n" +
                "              where istdate = 1 AND TO_CHAR(datetime, 'yyyymm') = ?),\n" +
                "     tmp2 as (select max(datetime) date2\n" +
                "              From fund.TRADEDATE_BASIC_ALL_SYN\n" +
                "              where istdate = 1 AND TO_CHAR(datetime, 'yyyymm') = ?),\n" +
                "     tmp3 as (select to_char(a.calc_date, 'yyyymm') calc_date, TTJJ_FUNDCODE, TTJJ_PER_POSI_AMT, TTJJ_PER_POSI_CUSTNUM\n" +
                "              from RETENTION.FUNDPOSI_DAY_STAT_SYN_ALL a\n" +
                "                       inner join tmp1 b on a.calc_date = date1\n" +
                "              where TTJJ_PER_POSI_AMT > 0),\n" +
                "     tmp4 as (select TTJJ_FUNDCODE, TTJJ_PER_POSI_AMT, TTJJ_PER_POSI_CUSTNUM\n" +
                "              from RETENTION.FUNDPOSI_DAY_STAT_SYN_ALL a\n" +
                "                       inner join tmp2 b on a.calc_date = date2\n" +
                "              where TTJJ_PER_POSI_AMT > 0)\n" +
                "select a.JJGSID,\n" +
                "       a.FUNDTYPE,\n" +
                "       b.calc_date,\n" +
                "       sum(b.TTJJ_PER_POSI_AMT)                                                                                                            POSI_AMT,\n" +
                "       rank()\n" +
                "       over (partition by a.fundtype,b.calc_date order by sum(b.TTJJ_PER_POSI_AMT) desc nulls last)                                        POSI_AMT_RANK,\n" +
                "       sum(b.TTJJ_PER_POSI_CUSTNUM)                                                                                                        POSI_NUM,\n" +
                "       rank()\n" +
                "       over (partition by a.fundtype,b.calc_date order by sum(b.TTJJ_PER_POSI_CUSTNUM) desc nulls last)                                    POSI_NUM_RANK,\n" +
                "       round(sum(b.TTJJ_PER_POSI_AMT) / sum(b.TTJJ_PER_POSI_CUSTNUM), 2)                                                                   POSI_PER,\n" +
                "       rank()\n" +
                "       over (partition by a.fundtype,b.calc_date order by sum(b.TTJJ_PER_POSI_AMT) / sum(b.TTJJ_PER_POSI_CUSTNUM) desc nulls last)         POSI_PER_RANK,\n" +
                "       round(100 * (sum(b.TTJJ_PER_POSI_AMT) / sum(c.TTJJ_PER_POSI_AMT) - 1),\n" +
                "             2)                                                                                                                            POSI_AMT_RATE,\n" +
                "       rank()\n" +
                "       over (partition by a.fundtype,b.calc_date order by 100 * (sum(b.TTJJ_PER_POSI_AMT) / sum(c.TTJJ_PER_POSI_AMT) - 1) desc nulls last) POSI_AMT_RATE_RANK\n" +
                "from tmp a\n" +
                "         inner join tmp3 b on a.FUNDCODE = b.TTJJ_FUNDCODE\n" +
                "         left join tmp4 c on a.FUNDCODE = c.TTJJ_FUNDCODE\n" +
                "group by a.JJGSID, a.fundtype, b.calc_date";
        List<Map> maps = app.getVerticaRead().executeQuery(sql, Arrays.asList(TimeUtil.dateToStr(calDay, TimeUtil.FORMAT_YYYYMM),TimeUtil.dateToStr(date, TimeUtil.FORMAT_YYYYMM)));
        if (!CollectionUtils.isEmpty(maps)) {
            return JSON.parseArray(JSON.toJSONString(maps), CFHTradeRankDataDTO.class);
        }
        return new ArrayList<>();
    }
}
