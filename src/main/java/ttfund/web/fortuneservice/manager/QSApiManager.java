package ttfund.web.fortuneservice.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.CommonHelper;
import com.ttfund.web.base.helper.HttpHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.config.CommonConfig;
import ttfund.web.fortuneservice.constant.AppConfigConstant;
import ttfund.web.fortuneservice.utils.CommonUtil;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class QSApiManager {

    public static final String QS_TASK_MSG = "qs.task.log.msg";

    @Value("${" + AppConfigConstant.API_QS_URL + ":http://10.191.84.64:18899}")
    public String baseurl;

    @Value("${" + AppConfigConstant.API_QS_ALARM_URL + ":http://172.31.38.62:8811}")
    public String alarmBaseurl;

    @Value("${" + AppConfigConstant.API_QS_TASK_URL + ":http://flowmg.clear:88}")
    public String taskUrl;

    @Value("${" + QS_TASK_MSG + ":待上架定期报告%s个}")
    public String taskLogMsg;

    @Resource
    private CommonConfig config;

    public List<String> getHoldFundList(List<String> codeList) {
        String url = baseurl + "/clear/fundIsHold";

        if (CollectionUtils.isEmpty(codeList)) {
            return new ArrayList<>();
        }

        String http = HttpHelper.requestPostJson(url, JSONObject.toJSONString(codeList), 10 * 60 * 1000);

        if (StringUtils.isEmpty(http)) {
            return new ArrayList<>();
        }

        JSONObject json = JSONObject.parseObject(http);
        JSONObject data = json.getJSONObject("data");

        return data.entrySet().stream()
                .filter(entry -> "1".equals(entry.getValue().toString()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    public boolean sendMessage(String eventAbstract, String eventDetail) {
        boolean result = false;
        if (StringUtils.isEmpty(eventAbstract) || StringUtils.isEmpty(eventDetail)) {
            return result;
        }
        String url = alarmBaseurl + "/alarm-server/send";
        HashMap<String, Object> map = new HashMap<>();
        map.put("configId", config.inspectionReportQSConfig);
        map.put("eventAbstract", eventAbstract);
        map.put("eventDetail", eventDetail);
        String http = HttpHelper.requestPostJson(url, JSON.toJSONString(map), true);
        if (StringUtils.isNotEmpty(http)) {
            JSONObject jsonObject = JSON.parseObject(http);
            if (jsonObject != null && Integer.valueOf(200).equals(jsonObject.getInteger("code"))
                    && jsonObject.getBoolean("success")) {
                result = jsonObject.getBoolean("success");
            }
        }
        return result;
    }

    public void taskLog(Date startTime, Date endTime, Integer count) {
        String url = taskUrl + "/prod-api/api/taskLog/save";
        HashMap<String, Object> map = new HashMap<>();
        map.put("systemCode", config.systemReportQSConfig);
        map.put("taskNo", config.taskReportQSConfig);
        map.put("execResult", String.format(taskLogMsg, count));
        map.put("execStatus", Integer.valueOf(0).equals(count) ? 0 : 3);
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        String http = HttpHelper.requestPostJson(url, JSON.toJSONString(map), true);
    }
}
