package ttfund.web.fortuneservice.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import ttfund.web.fortuneservice.model.dto.ApiResultAIDTO;
import ttfund.web.fortuneservice.model.dto.MeetingSummaryAIDTO;
import ttfund.web.fortuneservice.model.dto.MeetingSummaryDataDTO;
import ttfund.web.fortuneservice.utils.http.HttpClient;
import ttfund.web.fortuneservice.utils.http.HttpParamers;

import java.util.Optional;


@Service
public class AIApiService {
    /**
     * 密钥key
     */
    public static final  String AI_URL = "url.base.ai";
    public static final  String TIME_OUT = "timeout.ai";

    @Value("${" + AI_URL + ":http://8test.k8s.yunwei}")
    private String baseUrl;
    @Value("${" + TIME_OUT + ":180}")
    private Integer timeOut;

    private Integer getTimeOut(){
        return Math.max(Optional.ofNullable(timeOut).orElse(0), 180);
    }

    public MeetingSummaryDataDTO getAIContent(String userInput) {
        try {
            if (StringUtils.isEmpty(userInput)) {
                return null;
            }
            //调AI接口编辑样式
            String url = baseUrl + "/ttagent/priMeetingSummary";
            HttpParamers paramers = new HttpParamers(HttpMethod.POST);
            paramers.addUrl(url);
            paramers.addParam("customerNo", "PRI_MEETING_SUMMARY");
            paramers.addParam("userInput", userInput);
            paramers.setJsonParamer();
            String http = HttpClient.doService(paramers, null, 1000 * getTimeOut());
            if (StringUtils.isNotEmpty(http)) {
                ApiResultAIDTO<MeetingSummaryAIDTO> result = JSON.parseObject(http, new TypeReference<ApiResultAIDTO<MeetingSummaryAIDTO>>() {
                });
                if (result != null && result.getSucceed() && result.getResult() != null && result.getResult().getData() != null
                        && StringUtils.isNotEmpty(result.getResult().getData().getContent())) {
                    String content = result.getResult().getData().getContent().replaceAll("\\n", "").replaceAll("\n", "");
                    result.getResult().getData().setContent(content);
                    return result.getResult().getData();
                }
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }
}
