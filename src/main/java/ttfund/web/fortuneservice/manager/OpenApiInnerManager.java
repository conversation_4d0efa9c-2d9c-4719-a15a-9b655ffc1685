package ttfund.web.fortuneservice.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ttfund.web.base.helper.CommonHelper;
import com.ttfund.web.base.helper.DateHelper;
import com.ttfund.web.base.helper.HttpHelper;
import com.ttfund.web.core.annotation.AopCache;
import com.ttfund.web.core.model.ApiResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import ttfund.web.fortuneservice.config.CommonConfig;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.model.bo.TradeDayApiBo;
import ttfund.web.fortuneservice.model.dto.ApiOpenInner;
import ttfund.web.fortuneservice.model.dto.TradeDayDTO;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class OpenApiInnerManager {

    private static final String OPENAPI_INNER_URL = "open.api.inner.url";
    private static final String OPENAPI_INNER_VIEW = "fundadmin.fdbio_b_tradedate";
    private static final String OPENAPI_INNER_FILED = "datetime,istdate,belongday,lastday,eisdel,eseqid";


    @Value("${" + OPENAPI_INNER_URL + ":https://dataapineice.1234567.com.cn}")
    public String baseUrl;



    public List<TradeDayDTO> getTradeDayBySeqId(Long eseqId) {
        if (eseqId == null) {
            return null;
        }
        String url = baseUrl + "/OpenAPI3.0/InnerAPI/Common/OracleDataByBreakPoint";
        Map<String, Object> map = new HashMap<>();
        map.put("view", OPENAPI_INNER_VIEW);
        map.put("eseqid", eseqId);
        map.put("fields", OPENAPI_INNER_FILED);

        TypeReference<ApiOpenInner<List<TradeDayDTO>>> typeReference = new TypeReference<ApiOpenInner<List<TradeDayDTO>>>(){};
        String http = HttpHelper.requestGet(url, CommonHelper.getPostFromStr(map), true);
        if (StringUtils.isEmpty(http)) {
            return null;
        }
        ApiOpenInner<List<TradeDayDTO>> response = JSON.parseObject(http, typeReference);
        if (response == null) {
            return null;
        }
        return response.getData();
    }
}
