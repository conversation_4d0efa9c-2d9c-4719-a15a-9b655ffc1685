package ttfund.web.fortuneservice.manager;

import com.alibaba.fastjson.JSON;
import com.ttfund.web.base.helper.CommonHelper;
import com.ttfund.web.base.helper.HttpHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import ttfund.web.fortuneservice.config.CommonConfig;
import ttfund.web.fortuneservice.utils.CommonUtil;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Optional;

/**
 * YuYanApiManager.java
 *
 * <AUTHOR>
 * @date 2023/5/24 14:43
 */
@Service
public class YuYanApiManager {

    @Resource
    private CommonConfig config;

    /**
     * 生成财富号首页
     *
     * @param id          财富号ID
     * @param name        财富号名称
     * @param passportId  财富号通行证ID
     * @param companyCode 公司code
     * @param isPrivate   1为私募,0为普通财富号
     * @return 是否生成成功
     **/
    public boolean generateNewPage(String id, String name, String passportId, String companyCode, Integer isPrivate) {
        if (StringUtils.isNotEmpty(id) && StringUtils.isNotEmpty(name) && StringUtils.isNotEmpty(passportId)
                && StringUtils.isNotEmpty(companyCode) && isPrivate != null) {
            //调雨燕接口生成财富号首页
            String url = config.yuYanBaseUrl + "/page/generateNewPage";
            HashMap<String, Object> map = new HashMap<>(5);
            map.put("id", id);
            map.put("name", CommonUtil.encodeParam(name));
            map.put("passportId", passportId);
            map.put("companyCode", companyCode);
            map.put("isPrivate", isPrivate);
            String http = HttpHelper.requestGet(url, CommonHelper.getPostFromStr(map), true);
            return Optional.ofNullable(http)
                    .map(JSON::parseObject)
                    .map(data -> data.getBoolean("status"))
                    .orElse(false);
        }
        return false;
    }
}
