package ttfund.web.fortuneservice.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ttfund.web.base.helper.CommonHelper;
import com.ttfund.web.base.helper.HttpHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import ttfund.web.fortuneservice.constant.AppConfigConstant;
import ttfund.web.fortuneservice.model.bo.AuthorDetailBo;
import ttfund.web.fortuneservice.model.dto.ApiResultTGDTO;
import ttfund.web.fortuneservice.model.dto.InvestSpecialDTO;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class TGApiManager {

    private static final String fieldNames = "PARTNER,TGCODE,TGNAME,SYL_D,SYL_Z,SYL_Y,SYL_3Y,SYL_6Y,SYL_1N,SYL_LN,SYL_2N,SYL_3N,SYL_5N,SYL_JN,ANNSYL_LN,ESTABDATE,ACCOUNTID,JJGSID";

    public static final  String TG_URL_BASE = "url.base.tg.api";

    @Value("${" + TG_URL_BASE + ":}")
    private String tgBaseUrl;

    public List<InvestSpecialDTO> getAlFundListByCfhId(String cfhId) {
        List<InvestSpecialDTO> result = new ArrayList<>();
        String url = tgBaseUrl + "/combine/investAdviserInfo/getFundIAList";

        HashMap<String, Object> params = new HashMap<>();

        if (StringUtils.isNotEmpty(cfhId)) {
            params.put("cfhId", cfhId);
        }
        params.put("fieldNames", fieldNames);

        String http = HttpHelper.requestGet(url, CommonHelper.getPostFromStr(params), true);

        if (StringUtils.isNotEmpty(http)) {
            ApiResultTGDTO<List<InvestSpecialDTO>> res = JSON.parseObject(http, new TypeReference<ApiResultTGDTO<List<InvestSpecialDTO>>>() {
            });
            if (res != null && res.getSucceed() && res.getData() != null) {
                result = res.getData();
                if (StringUtils.isNotEmpty(cfhId)) {
                    result = result.stream().filter(o->cfhId.equals(o.getACCOUNTID())).collect(Collectors.toList());
                }
            }
        }
        return result;
    }
}
