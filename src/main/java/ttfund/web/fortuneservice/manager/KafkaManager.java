package ttfund.web.fortuneservice.manager;

import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import ttfund.web.fortuneservice.config.App;
import ttfund.web.fortuneservice.model.cfhkafka.CFHNoticeModel;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/30 10:03
 */
@Service
public class KafkaManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(KafkaManager.class);
    
    @Resource
    private App app;

    public boolean sendCfhNotice(List<CFHNoticeModel> noticeModelList) {
        boolean success = true;
        try {
            for (CFHNoticeModel cfhNoticeModel : noticeModelList) {
                app.getKafkafundweb().produce("cfh_notice_msg", cfhNoticeModel.getNoticeid(), JSON.toJSONString(cfhNoticeModel));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            success = false;
        }
        return success;
    }
}
