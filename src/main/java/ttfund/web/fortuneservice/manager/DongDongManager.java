package ttfund.web.fortuneservice.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.CommonHelper;
import com.ttfund.web.base.helper.HttpHelper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.config.CommonConfig;
import ttfund.web.fortuneservice.utils.CommonUtil;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;

/**
 * DongDongManager.java
 *
 * <AUTHOR>
 * @date 2023/5/11 14:18
 */
@Service
public class DongDongManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(DongDongManager.class);


    @Resource
    private CommonConfig config;

    /**
     * 送咚咚消息-宋佳兵提供的接口
     *
     * @param dongDongAccountList 咚咚号列表
     * @param content             内容
     * @return 发送结果
     */
    public int sendDongDongMessage(List<String> dongDongAccountList, String title, String content) {
        if (CollectionUtils.isEmpty(dongDongAccountList)) {
            return 0;
        }
        int sum = 0;
        for (String account : dongDongAccountList) {
            boolean flag = sendDongDongMessage(account, title, content);
            if (!flag) {
                LOGGER.error("咚咚消息推送失败，咚咚号：{}, content:{}", account, content);
                continue;
            }
            sum++;
        }
        return sum;
    }

    /**
     * 发送咚咚消息-宋佳兵提供的接口
     *
     * @param dongDong 咚咚号
     * @param title    题目
     * @param content  内容
     * @return 发送结果
     **/
    public boolean sendDongDongMessage(String dongDong, String title, String content) {
        boolean flag = false;
        if (StringUtils.isEmpty(dongDong) || StringUtils.isEmpty(content)) {
            return flag;
        }
        String url = config.dongDongSendUrl;
        HashMap<String, Object> map = new HashMap<>();
        map.put("dongDong", dongDong);
        map.put("title", CommonUtil.encodeParam(title));
        map.put("content", CommonUtil.encodeParam(content));
        String http = HttpHelper.requestGet(url, CommonHelper.getPostFromStr(map), true);
        if (StringUtils.isNotEmpty(http)) {
            JSONObject jsonObject = JSON.parseObject(http);
            if (jsonObject != null) {
                flag = "0".equals(jsonObject.getString("ErrCode"));
            }
        }
        return flag;
    }
}
