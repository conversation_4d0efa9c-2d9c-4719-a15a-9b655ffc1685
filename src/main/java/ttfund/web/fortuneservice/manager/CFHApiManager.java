package ttfund.web.fortuneservice.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ttfund.web.base.helper.HttpHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import ttfund.web.fortuneservice.constant.AppConfigConstant;
import ttfund.web.fortuneservice.model.bo.AuthorDetailBo;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description CFHApiManager。java
 * @date 2023/8/3 10:45
 */
@Service
public class CFHApiManager {

    @Value("${" + AppConfigConstant.API_CFH_URL + "}")
    public String baseurl;

    /**
     * 得到作者的详细信息(包括订阅数和文章数)
     *
     * @param authorId
     * @return
     */
    public AuthorDetailBo getAuthorDetail(String authorId) {
        AuthorDetailBo result = null;
        if (this.validParam(authorId)) {
            if (StringUtils.isNotEmpty(authorId)) {
                String url = baseurl + "/api/v1/article/Author/GetAuthorDetail";
                Map<String, String> map = new HashMap<>();
                map.put("authorId", authorId);
                map.put("isall", String.valueOf(0));
                String html = HttpHelper.requestPostJson(url, JSON.toJSONString(map), 3000);
                if (StringUtils.isNotEmpty(html)) {
                    result = JSON.parseObject(html, new TypeReference<AuthorDetailBo>() {
                    });
                }
            }
        }
        return result;
    }

    public boolean validParam(String str) {
        List<String> filterId = Arrays.asList("undefined", "null");
        boolean result = false;
        if (StringUtils.isNotBlank(str)) {
            if (!filterId.contains(str.toLowerCase().trim())) {
                result = true;
            }
        }
        return result;
    }
}
