package ttfund.web.fortuneservice.manager;

import cn.hutool.core.util.ZipUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ttfund.web.base.helper.CommonHelper;
import com.ttfund.web.base.helper.DateHelper;
import com.ttfund.web.base.helper.HttpHelper;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import ttfund.web.fortuneservice.model.dto.TradeDetailRecord;
import ttfund.web.fortuneservice.utils.TimeUtil;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/2/3 10:30
 * 财富号数据迁移的数据来源
 * 开发：杨金雨
 * 测试base地址：http://8test.k8s.yunwei
 * 线上base地址：http://api.datacent.trade
 */
@Repository
public class TTTradeDetailApi {

    /**
     * 财富号数据迁移数据接口基本url
     */
    private static final String CFH_DATA_RELOCATE_BASE_URL = "cfh.data.relocate.base.url";
    /**
     * 交易日查询连接
     */
    private static final String TRADE_DAY_INFO_URL = "trade.day.info.url";
    /**
     * 请求判断是否为交易日接口的密钥
     */
    private static final String TRADE_DAY_CHECK_AUTH_KEY = "tradeDay.check.authKey";
    /**
     * 财富号数据迁移源数据api等待响应时间(秒)
     */
    private static final String CFH_RELOCATE_API_WAIT_TIME = "cfh.relocate.api.wait.time";

    private static Logger logger = LoggerFactory.getLogger(TTTradeDetailApi.class);

    @Value("${" + CFH_DATA_RELOCATE_BASE_URL + ":https://dataapineice.1234567.com.cn}")
    private String baseUrl;

    @Value("${" + TRADE_DAY_INFO_URL + ":https://dataapineice.1234567.com.cn/mm/FundSpecical/TradeDayInfo}")
    private String tradeDayInfoUrl;

    @Value("${" + TRADE_DAY_CHECK_AUTH_KEY + ":wrs_b5fc9836-3918-598a-1c61-b4a8562d4962}")
    private String authKey;

    @Value("${" + CFH_RELOCATE_API_WAIT_TIME + ":60}")
    private int timeOut;

    /**
     * 获取分时数据
     *
     * @return 当前小时的交易数据
     */
    public List<TradeDetailRecord> getHourData() {
        List<TradeDetailRecord> list = null;
        try {
            String url = baseUrl + "/datacent/getZippedDataOnHour";
            String html = HttpHelper.requestGet(url, timeOut * 1000);
            list = getTradeDetailRecords(html);
        } catch (Exception e) {
            logger.error("调api获取分时数据出现异常:{}", e.getMessage());
        }
        return list;
    }

    /**
     * 获取全天数据
     *
     * @returnDay 全天的交易数据（前一天15：00到当天15：00的数据）
     */
    public List<TradeDetailRecord> getDayData() {
        List<TradeDetailRecord> list = null;
        try {
            String url = baseUrl + "/datacent/getZippedDataOnDay";
            String apiResult = HttpHelper.requestGet(url, 6 * timeOut * 1000);
            list = getTradeDetailRecords(apiResult);
        } catch (Exception e) {
            logger.error("调api获取全天数据出现异常:{},{}", e.getMessage(), e);
        }
        return list;
    }

    /**
     * 根据补充数据接口获取全天数据
     *
     * @returnDay 全天的交易数据（前一天15：00到当天15：00的数据）
     */
    public List<TradeDetailRecord> getLoseData(Date dt) {
        List<TradeDetailRecord> list = null;
        Map<String, Object> map = new HashMap<>();
        map.put("date", DateHelper.dateToStr(dt, DateHelper.FORMAT_YYYYMMDD));
        try {
            String url = baseUrl + "/datacent/getZippedLoseDataOnDay";
            String postData = JSON.toJSONString(map);
            String apiResult = HttpHelper.requestPostJson(url, postData, 6 * timeOut * 1000);
            list = getTradeDetailRecords(apiResult);
        } catch (Exception e) {
            logger.error("调api获取补充数据出现异常:{},{}", e.getMessage(), e);
        }
        return list;
    }

    /**
     * 获取交易数据公共的解析html方法
     * @param html api响应
     * @return 交易数据list
     */
    private List<TradeDetailRecord> getTradeDetailRecords(String html) {
        List<TradeDetailRecord> list = null;
        if (StringUtils.isNotEmpty(html)) {
            try {
                String succeed = JSON.parseObject(html).getString("succeed");
                if ("true".equalsIgnoreCase(succeed)) {
                    //解压数据并解析为list
                    String str = JSON.parseObject(html).getString("result");
                    html = null;
                    byte[] bytes = Base64.getDecoder().decode(str);
                    str = null;
                    String dataStr = ZipUtil.unGzip(bytes, "UTF-8");
                    bytes = null;
                    list = JSON.parseObject(dataStr, new TypeReference<List<TradeDetailRecord>>() {
                    });
                    XxlJobLogger.log("本次获取到的交易数据为数量为{}",list.size());
                    logger.info("本次获取到的交易数据为数量为{}",list.size());
                }
            }catch (Exception e){
                logger.error("解析交易数据出现异常：{}",e.getMessage());
            }
        }
        return list;
    }

    /**
     * 判断给定日期是否为交易日
     *
     * @param dt 日期
     * @return
     */
    public boolean isTradeDay(Date dt) {
        boolean result = false;
        Map<String, Object> map = new HashMap<>();
        map.put("authkey", authKey);
        map.put("DATE", DateHelper.dateToStr(dt, DateHelper.FORMAT_YYYY_MM_DD));
        String postData = CommonHelper.getPostFromStr(map);
        try {
            String html = HttpHelper.requestPostFrom(tradeDayInfoUrl, postData, 3000);
            if (StringUtils.isNotEmpty(html)) {
                JSONObject jsonData = JSON.parseObject(html);
                if ("true".equalsIgnoreCase(JSON.parseObject(html).getString("success"))) {
                    JSONObject data = jsonData.getJSONObject("data");
                    if (data != null) {
                        result = data.getBoolean("ISTRADE");
                    }
                }
            }
        } catch (Exception e) {
            logger.error("请求判断交易日接口出现异常：{}，{}", e.getMessage(), e);
        }
        return result;
    }

    public List<TradeDetailRecord> getDataByTime(Date startTime, Date endTime) {
        List<TradeDetailRecord> list = null;
        try {
            String url = baseUrl + "/datacent/getZippedDataByTime";
            HashMap<String, Object> map = new HashMap<>();
            map.put("beginTime", TimeUtil.dateToStr(startTime, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS));
            map.put("endTime", TimeUtil.dateToStr(endTime, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS));
            String http = HttpHelper.requestPostJson(url, JSON.toJSONString(map), timeOut * 1000);

            list = getTradeDetailRecords(http);
        } catch (Exception e) {
            logger.error("调api获取分时数据出现异常:{}", e.getMessage());
        }
        return list;
    }
}
