package ttfund.web.fortuneservice.manager;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import ttfund.web.fortuneservice.model.dto.MonthlyReportRequestDTO;
import ttfund.web.fortuneservice.model.dto.MonthlyReportResponseDTO;
import ttfund.web.fortuneservice.utils.http.HttpClient;
import ttfund.web.fortuneservice.utils.http.HttpParamers;

import java.util.Map;
import java.util.Optional;

/**
 * 月度报告AI接口服务
 */
@Service
public class MonthlyReportAIApiService {
    
    /**
     * AI服务基础URL
     */
    public static final String AI_URL = "finance.ai.url";
    public static final String TIME_OUT = "timeout.ai";

    @Value("${" + AI_URL + ":http://localhost:8081/polaris}")
    private String baseUrl;
    
    @Value("${" + TIME_OUT + ":180}")
    private Integer timeOut;

    private Integer getTimeOut(){
        return Math.max(Optional.ofNullable(timeOut).orElse(0), 180);
    }

    /**
     * 获取月度报告总结（支持异步模式）
     *
     * @param request 月度报告请求数据
     * @return 月度报告响应数据或任务提交响应
     */
    public Object getMonthlySummary(MonthlyReportRequestDTO request) {
        try {
            if (request == null) {
                return null;
            }
            //调AI接口生成月度报告总结
            String url = baseUrl + "/monthly-report/summary";
            HttpParamers paramers = new HttpParamers(HttpMethod.POST);
            paramers.addUrl(url);
            paramers.setJsonParamer(JSON.toJSONString(request));
            String http = HttpClient.doService(paramers, null, 1000 * getTimeOut());
            if (StringUtils.isNotEmpty(http)) {
                // 先尝试解析为Map，检查是否是异步响应
                try {
                    Map<String, Object> responseMap = JSON.parseObject(http, Map.class);
                    if (responseMap.containsKey("taskId") && responseMap.containsKey("status")) {
                        // 异步模式，返回任务信息
                        return responseMap;
                    } else {
                        // 同步模式，返回完整结果
                        return JSON.parseObject(http, MonthlyReportResponseDTO.class);
                    }
                } catch (Exception e) {
                    // 如果解析为Map失败，尝试解析为MonthlyReportResponseDTO
                    return JSON.parseObject(http, MonthlyReportResponseDTO.class);
                }
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }

    /**
     * 轮询月度报告总结任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态响应
     */
    public Object pollMonthlySummary(String taskId) {
        try {
            if (StringUtils.isEmpty(taskId)) {
                return null;
            }
            // 调用轮询接口
            String url = baseUrl + "/monthly-report/summary?taskId=" + taskId;
            HttpParamers paramers = new HttpParamers(HttpMethod.POST);
            paramers.addUrl(url);
            // 设置JSON Content-Type和空的JSON请求体
            paramers.setJsonParamer("{}");
            String http = HttpClient.doService(paramers, null, 1000 * getTimeOut());
            if (StringUtils.isNotEmpty(http)) {
                // 解析为Map返回
                return JSON.parseObject(http, Map.class);
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }
}
