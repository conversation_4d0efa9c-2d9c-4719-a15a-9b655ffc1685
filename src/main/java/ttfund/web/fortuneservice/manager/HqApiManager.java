package ttfund.web.fortuneservice.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ttfund.web.base.helper.CommonHelper;
import com.ttfund.web.base.helper.DateHelper;
import com.ttfund.web.base.helper.HttpHelper;
import com.ttfund.web.core.annotation.AopCache;
import com.ttfund.web.core.model.ApiResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import ttfund.web.fortuneservice.config.CommonConfig;
import ttfund.web.fortuneservice.model.bo.TradeDayApiBo;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className HqApiManager
 * @date 2023/4/7 14:53
 */
@Service
public class HqApiManager {

    @Resource
    private CommonConfig config;

    /**
     * 根据给定时间查询指定时间的交易日
     * <AUTHOR>
     * @date 2023/4/10 15:02
     * @param dt dt
     * @param t t
     * @return java.util.Date
     */
    @AopCache(fieldorder = {0, 1}, cache1expire = 30 * 60 * 1000)
    public Date getTradeDayPre(Date dt, int t) {
        Date result = null;
        if (dt != null) {
            //调行情接口查列表
            String url = config.hqBaseUrl + "/mm/FundSpecical/TradeDayInfoList";
            HashMap<String, Object> map = new HashMap<>();
            map.put("authkey", config.authKey);
            map.put("endDate", URLEncoder.encode(DateHelper.dateToStr(dt, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS)));
            map.put("endDateContains", false);
            map.put("limit", t);
            map.put("sortType", -1);
            TypeReference<ApiResponse<List<TradeDayApiBo>,?>> typeReference = new TypeReference<ApiResponse<List<TradeDayApiBo>,?>>(){};
            String http = HttpHelper.requestGet(url, CommonHelper.getPostFromStr(map), true);
            if (StringUtils.isNotEmpty(http)) {
                List<TradeDayApiBo> data = JSON.parseObject(http, typeReference).getData();
                if (data != null && data.size() >= t) {
                    String datetime = data.get(t - 1).getDATETIME();
                    result = DateHelper.stringToDate2(datetime, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
                }
            }
        }
        return result;
    }
}
