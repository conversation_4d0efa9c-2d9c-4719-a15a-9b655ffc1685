package ttfund.web.fortuneservice.constant;

/**
 * 核心配置常量
 * <p>只配置连接串</p>
 * <AUTHOR>
 * @version 1.0.0
 * @className AppConfigConstant
 */
public class AppConfigConstant {

    /**
     * CFH mongodb 写
     */
    public static final String MONGODB_CFH_CONN_WRITE = "mongodb.cfh.connsr.write";

    /**
     * CFH mongodb 读
     */
    public static final String MONGODB_CFH_CONN_READ = "mongodb.cfh.connsr.read";

    /**
     * HQ mongodb 读
     */
    public static final String MONGODB_HQ_CONN_READ = "mongodb.hq.connsr.read";
    public static final String MONGODB_TG_CONN_READ = "mongodb.tg.connsr.read";

    /**
     * 东财 sqlserver
     */
    public static final String SQLSERVER_CFH_CONN_WRITE = "sqlserver.cfh.connsr.write";

    /**
     * 东财 kafka配置：properties
     */
    public static final String KAFKA_EAST_MONEY_CONSUMER_CONFIG = "kafka.eastmoney.consumer.config";
    public static final String KAFKA_WEB_CONSUMER_CONFIG = "kafka.web.consumer.config";
    public static final String KAFKA_WEB_CONSUMER_CONFIG_URL = "kafka.web.consumer.url";

    /**
     * 东财 kafka配置：并发数
     */
    public static final String KAFKA_EAST_MONEY_LISTENER_CONCURRENCY = "kafka.eastmoney.consumer.listener.concurrency";
    public static final String KAFKA_WEB_LISTENER_CONCURRENCY = "kafka.web.consumer.listener.concurrency";
    /**
     * 浪客Kafka配置
     */
    public static final String KAFKA_LANGKE_LISTENER_CONCURRENCY = "kafka.langke.consumer.listener.concurrency";
    public static final String KAFKA_LANGKE_CONSUMER_CONFIG = "kafka.langke.consumer.config";
    /**
     * 正在进行的活动id列表
     */
    public static final String ACTIVITY_ALIVE_ID = "activity.alive.id";

    /**
     * vertica读
     */
    public static final String VERTICA_CONN_READ = "vertica.connsr.read";
    public static final String SHORT_VIDEO_RELEASE_DATA = "short.video.release.data";
    /**
     * 读写
     */
    public static final String VERTICA_CONN_READ_WRITE = "vertica.connsr.read.write";

    public static final String API_CFH_URL = "api.cfh.url";
    public static final String API_QS_URL = "api.qs.url";
    public static final String API_QS_TASK_URL = "api.qs.task.url";
    public static final String API_QS_ALARM_URL = "api.qs.alarm.url";

    public final static String kafka_fundweb_connsr = "kafka.fundweb.connsr";

    /**
     * 万国88A写
     */
    public static final String REDIS_CFH_CONNSR_WRITE = "redis.cfh.connsr.write";
    public static final String REDIS_CFH_CONNSR_WRITE_PJ = "redis.cfh.connsr.write.pj";

    public static final String MONGO_CFH_URL = "mongo.cfh.connsr.write.url";

    public static final String VIP_MEETING_LIB_MONTH = "vip.meeting.lib.month";

    private AppConfigConstant() {
        throw new IllegalStateException(CommonConstant.UTILITY_CLASS);
    }

    public static final String FINANCE_AI_URL = "finance.ai.url";
}
