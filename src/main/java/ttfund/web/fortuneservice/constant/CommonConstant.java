package ttfund.web.fortuneservice.constant;

/**
 * 通用常量
 * <AUTHOR>
 * @version 1.0.0
 * @className CommonConstant
 * @date 2023/4/8 8:34
 */
public class CommonConstant {

    /**
     * 私有构造函数抛出异常信息
     */
    public static final String UTILITY_CLASS = "Utility class";

    /**
     * 行情接口url
     */
    public static final String API_HQ_URL = "api.hq.url";

    /**
     * 行情接口key
     */
    public static final String API_HQ_AUTH_KEY = "api.hq.authkey";

    /**
     * 向前查询交易日天数配置
     */
    public static final String SELECT_DAY_MANAGER = "select.day.manager";

    /**
     * 向前查询交易日天数配置
     */
    public static final String SELECT_DAY_LABEL = "select.day.label";

    /**
     * 期间 观点条数配置
     */
    public static final String QUICK_REVIEW_NUM = "quickReview.gte.num";

    /**
     * 热门标签 mongodb-主键
     */
    public static final String HOT_LABEL_LIST = "fortune.service.hot.plate.new";

    /**
     * 人气大咖缓存 mongodb-主键
     */
    public static final String HOT_MANAGER_LIST = "fortune.service.hot.manager.new";

    /**
     * mongo库单次最大读取量
     */
    public static final int MONGO_READ_MAX_COUNT = 1000;

    /**
     * 行情 - 公告表 - mongo fundCodes数组最大长度
     */
    public static final int HQ_NOTICE_FUND_CODES_ARRAY_SIZE = 800;

    /**
     * 东财推送来源
     * <br/>
     * 对接人：袁桃
     */
    public static final String TOPIC_EAST_MONEY_CFH_ACCOUNT_UPDATE = "ttjj_fund_account_update";
    /**
     * 短视频topic
     */
    public static final String TOPIC_EAST_MONEY_EMAV_AV_COMMON_STATISTIC = "EMAV_AV_Common_Statistic";
    public static final String TOPIC_WEB_MEETING = "meeting_lib_message";

    public static final String LOG_TOPIC = "receive_log_notice";
    public static final String FUND_CFH_QUARTERLY_REPORT = "fund_cfh_quarterly_report";
    public static final String FUND_CFH_TRANSACTION_MESSAGE = "fund_cfh_transaction_message";

    /**
     * 组id
     */
    public static final String GROUP_ID_CFH = "fortuneconsumeservice";

    /**
     * Kafka配置
     */
    public static final String FACTORY_EAST_MONEY = "EastMoneyKafkaFactory";
    /**
     * Kafka配置 web
     */
    public static final String FACTORY_WEB = "WebKafkaFactory";
    public static final String WEB_TRAD_CONSUMER = "WebKafkaTradConsumer";
    public static final String FACTORY_LANGKE = "LangkeKafkaFactory";

    /**
     * 默认 img地址
     */
    public static final String DEFAULT_HEADER_IMG = "cfh.header.img.path.default.url";

    /**
     * 服务上线时间
     */
    public static final String DEFAULT_BREAK_POINT = "2023-04-10 00:00:00";

    /**
     * 最小时间
     */
    public static final String MIN_BREAK_POINT = "1970-01-01 00:00:00";

    public static final String MIN_BREAK_POINT_ARRAY = "[{time:'1970-01-01 00:00:00,000'}]";

    /**
     * 私募基金类型
     */
    public static final String PRIVATE_FUND_TYPE = "003003";

    public static final String API_YUYAN_URL = "api.yuyan.url";
    public static final String LABEL_ACTIVITY = "fortune.service.label.activity.%s";
    public static final String LABEL_OPINION = "fortune.service.label.opinion.%s.%s";
    public static final String LABEL_DATA = "fortune.service.label.data.%s.%s";

    /**
     * 时间转换类型
     */
    public static final String FORMAT_yyyyMMddHHmmss = "yyyyMMddHHmmss";

    /**
     * 精品私募 产品类型
     */
    public static final Integer PRIVATE_PRODUCT = 1;

    /**
     * 咚咚推送地址
     */
    public static final String DONG_DONG_URL = "api.dongdong.send.url";

    /**
     * 【合规】产品报告披露检验 咚咚通知人员名单配置
     */
    public static final String INSPECTION_REPORT_RECEIVING = "inspection.report.receiving";
    /**
     * 【合规】产品报告披露检验 清算配置ID
     */
    public static final String INSPECTION_REPORT_QS_CONFIG = "inspection.report.qs.config";
    public static final String SYSTEM_REPORT_QS_CONFIG = "system.report.qs.config";
    public static final String TASK_REPORT_QS_CONFIG = "task.report.qs.config";

    /**
     * 时间格式：yyyy/M/d H:mm:ss
     */
    public static final String FORMAT_YYYY_M_D_H_MM_SS = "yyyy/M/d H:mm:ss";

    /**
     * 理财产品报告披露检查时间、文件名配置
     */
    public static final String PRODUCT_REPORT_CHECK_CONFIG = "product.report.check.config";

    /**
     * 得到作者的详细信息(包括订阅数和文章数)
     */
    public static String ASP_NET_FUND_API_CFH_AUTHOR_DETAIL_CFHID = "asp_java_fund_api_cfh_authordetail_%s";

    public static final String GIFT_REWARD_HEADER_ERROR_MSG = "gift.reward.header.errMsg";

    /**
     * 服务告警通知人员
     */
    public static final String SERVICE_ALARM_OA = "service.alarm.oa";

    /**
     * 调用接口请求交易数据的最大重试次数
     */
    public static final int TRADE_DATA_GET_RETRY_TIMES = 5;

    /**
     * 调用接口请求财富号交易数据间隔时间
     */
    public static final long TRADE_DATA_API_WAIT_TIME = 10 * 1000L;

    private CommonConstant() {
        throw new IllegalStateException(CommonConstant.UTILITY_CLASS);
    }
}
