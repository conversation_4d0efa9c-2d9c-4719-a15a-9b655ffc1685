package ttfund.web.fortuneservice.constant;

/**
 * 行情 mongodb相关
 * <AUTHOR>
 * @version 1.0.0
 * @className CFHMongodbConstant
 */
public class HqMongodbConstant {

    /**
     * 投顾库
     */
    public static final String DB_TGSTGINFO = "TGSTGInfo";
    public static final String TABLE_TGSTGJBXX = "TGSTGJBXX";
    public static final String TABLE_TGSTG_INDICTPRD = "TGSTG_INDICTPRD";
    /**
     * 行情-公告库
     */
    public static final String DB_FUND_ANNOUNCEMENT = "FundAnnouncement";

    /**
     * 行情-基金档案库
     */
    public static final String DB_FUND_ARCHIVES = "FundArchives";

    /**
     * 公募公告表
     */
    public static final String TB_FUND_NOTICE = "FundNotice";

    /**
     * 私募公告表
     */
    public static final String TB_FUND_PRIVATE_NOTICE = "FundPrivateNotice";

    /**
     * 行情-基金基本信息表
     */
    public static final String TB_FUND_JBXX = "FundJBXX";

    /**
     * 通用 字段名称
     */
    public static class HqCommonField {

        public static final String UPDATE_TIME = "UPDATETIME";

        public static final String EID = "EID";

        public static final String BEGIN_TIME = "BEGINTIME";

        public static final String END_TIME = "ENDTIME";

        private HqCommonField() {
            throw new IllegalStateException(CommonConstant.UTILITY_CLASS);
        }
    }

    /**
     * FundNotice 字段名称
     */
    public static class FundNoticeField {

        public static final String MATCH_TYPE = "MATCHTYPE";

        public static final String FUND_TYPE = "FUNDTYPE";

        public static final String F_CODE = "FCODE";

        public static final String FEATURE = "FEATURE";

        public static final String B_TYPE = "BTYPE";

        private FundNoticeField() {
            throw new IllegalStateException(CommonConstant.UTILITY_CLASS);
        }
    }

    private HqMongodbConstant() {
        throw new IllegalStateException(CommonConstant.UTILITY_CLASS);
    }
}
