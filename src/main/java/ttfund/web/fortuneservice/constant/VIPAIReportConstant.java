package ttfund.web.fortuneservice.constant;

/**
 * VIP AI报告相关常量
 */
public class VIPAIReportConstant {

    /**
     * 报告类型常量
     */
    public static final class ReportType {
        /** 多机构报告 */
        public static final String MULTI_ORG = "MULTI_ORG";
        
        /** 单机构报告 */
        public static final String SINGLE_ORG = "SINGLE_ORG";
        
        /** 整体报告 */
        public static final String OVERALL = "OVERALL";
    }

    /**
     * JSON相关常量
     */
    public static final class Json {
        /** 空数组JSON字符串 */
        public static final String EMPTY_ARRAY = "[]";
        
        /** 空对象JSON字符串 */
        public static final String EMPTY_OBJECT = "{}";
    }

    /**
     * 时间相关常量
     */
    public static final class Time {
        /** 全量同步的起始时间 */
        public static final String FULL_SYNC_START_TIME = "2020-01-01 00:00:00";
    }

    /**
     * 报告状态常量
     */
    public static final class Status {
        /** 待审核 */
        public static final String PENDING = "PENDING";
        
        /** 审核通过 */
        public static final String APPROVED = "APPROVED";
        
        /** 审核未通过 */
        public static final String REJECTED = "REJECTED";
        
        /** 下架 */
        public static final String OFFLINE = "OFFLINE";
    }
}
