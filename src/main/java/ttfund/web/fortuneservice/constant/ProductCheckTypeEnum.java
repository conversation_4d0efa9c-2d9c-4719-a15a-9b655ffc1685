package ttfund.web.fortuneservice.constant;

/**
 * <AUTHOR>
 * @date 2023/5/12 15:01
 * 产品披露报告检查类型
 */
public enum ProductCheckTypeEnum {
    /**
     * 1-季报
     */
    QUARTER(1),
    /**
     * 2-净值
     */
    NAV(2),
    /**
     * 3-年报
     */
    YEAR(3),
    ;

    private int type;

    ProductCheckTypeEnum(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }
}
