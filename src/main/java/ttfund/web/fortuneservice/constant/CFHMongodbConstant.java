package ttfund.web.fortuneservice.constant;

/**
 * 财富号 mongodb相关
 * <AUTHOR>
 * @version 1.0.0
 * @className CFHMongodbConstant
 */
public class CFHMongodbConstant {

    /**
     * 天天基金 财富号Mongo库
     */
    public static final String DB_TT_FUND_CFH = "TTFundCFHDB";

    /**
     * 天天基金 财富号交易mongo库
     */
    public static final String DB_CFH_TRADE = "CFHTradeDB";

    /**
     * 天天快评-集合
     */
    public static final String TB_CFH_QUICK_REVIEW = "Tb_CFHQuickReview";

    /**
     * 列表-集合
     */
    public static final String TB_CFH_LIST = "TB_CfhList";
    /**
     * （财富号IM数据表）
     */
    public static final String TB_COMMUNITY_DATA_DAY = "TB_CFHIMData";

    /**
     * 标签-集合
     */
    public static final String TB_CFH_FUND_THEME = "Tb_CFHFundTheme";

    public static final String TB_CFH_PRODUCT_READ = "TB_CFHProductRead";
    /**
     * kafka消息
     */
    public static final String TB_KAFKA_MESSAGE = "TB_Kafka_message";

    /**
     * VIP AI报告-集合
     */
    public static final String TB_VIP_AI_REPORT = "VIPAIReport";
    public static final String TB_SHORTVIDEO = "TB_ShortVideo";
    public static final String TB_SHORTVIDEO_DAY = "TB_ShortVideoDay";

    /**
     * 产品解读公告-集合
     */
    public static final String TB_CFH_PRODUCT_READ_HQ = "TB_CFHProductReadHq";

    /**
     * 通用数据-集合
     */
    public static final String TB_CFH_GENERAL_DATA = "TB_CFHGeneralData";

    /**
     * 财富号文章表
     */
    public static final String TB_CFH_ARTICLE = "TB_CFHArticle";
    public static final String TB_MOUDLECACHE   = "TB_MoudleCache";

    /**
     * 交易日期表
     */
    public static final String TB_TRADE_DATE = "TRADEDATE";

    /**
     * 报告解读表
     */
    public static final String TB_PRODUCT_READ = "TB_CFHProductRead";

    /**
     * mongo表 高端理财-定期检测报告内容
     */
    public static final String REGULAR_REPORT_CONTENT = "RegularInspectionReportContent";

    /**
     * mongo表 高端理财-定期检测报告
     */
    public static final String REGULAR_REPORT = "RegularInspectionReport";

    /**
     * 配置白名单
     */
    public static final String TB_CFHCardWhitelist = "TB_CFHCardWhitelist";
    public static final String TB_CFHCardLog = "TB_CFHCardReciveLog";
    public static final String TB_CFHCardBadLog = "TB_CFHCardReciveBadLog";

    /**
     * 交易统计表
     */
    public static final String TB_REFERENCEFUND_DAY = "REFERENCEFUND_DAY";
    public static final String TB_REFERENCEFUND_JJGS = "REFERENCEFUND_JJGS";
    public static final String TB_REFERENCEFUND_MONTH = "REFERENCEFUND_MONTH";

    public static final String TB_INTERFACEFUND_DAY = "INTERFACEFUND_DAY";
    public static final String TB_INTERFACEFUND_JJGS = "INTERFACEFUND_JJGS";
    public static final String TB_FUNDCUSTNO_FEATURE = "FUNDCUSTNO_FEATURE";
    public static final String TB_COMCUSTNO_FEATURE = "COMCUSTNO_FEATURE";

    public static final String TB_CFHTRADE_RANK = "CFHTRADE_RANK";

    public static final String TB_LIVE_SCORE = "TB_LiveScore";
    public static final String TB_LIVE_FUND_SCORE = "TB_LiveFundScore";
    public static final String TB_CFHSCORE = "TB_CFHSCORE";

    /**
     * 投顾持有数据统计
     */
    public static final String TB_INVESTMENT_HOLD = "TB_InvestmentHold";
    public static final String TB_INVESTMENT_HOLD_RANK = "TB_InvestmentHoldRank";
    public static final String TB_INVESTMENT_USER_DATA = "TB_InvestmentUserData";
    public static final String TB_INVESTMENT_USER_DATA_OPTION = "TB_InvestmentUserDataOption";
    public static final String TB_INVESTMENT_USER_DATA_SUBSCRIBE = "TB_InvestmentUserDataSubscribe";
    public static final String TB_INVESTMENT_USER_DATA_RETENTION = "TB_InvestmentUserDataRetention";
    public static final String TB_INVESTMENT_USER_DATA_TARGETPROFIT = "TB_InvestmentUserDataTargetProfit";
    public static final String TB_INVESTMENT_USER_DATA_FGRATIO = "TB_InvestmentUserDataFgratio";
    public static final String TB_CFH_TRADE_STAT = "TB_CFHTradeStat";


    /**
     * mongo 通用数据表数据字段名
     */
    public static final String RESULT = "result";
    public static final String TB_VIP_MEETING_SUMMARY_LIB = "TB_VIPMeetingLib";

    /**
     * QuickReviewDto 字段名称
     */
    public static class QuickReviewField {

        public static final String TIME_POINT = "timepoint";

        public static final String IS_DEL = "IsDel";

        public static final String STATUS = "Status";

        public static final String LABEL_TYPE = "LabelType";

        private QuickReviewField() {
            throw new IllegalStateException(CommonConstant.UTILITY_CLASS);
        }
    }

    /**
     * CfhListDto 字段名称
     */
    public static class CfhListField {

        public static final String CFH_ID = "CFHID";

        public static final String STATUS = "Status";

        private CfhListField() {
            throw new IllegalStateException(CommonConstant.UTILITY_CLASS);
        }
    }

    /**
     * FundLabelDto 字段名称
     */
    public static class FundLabelField {

        public static final String STATUS = "Status";

        private FundLabelField() {
            throw new IllegalStateException(CommonConstant.UTILITY_CLASS);
        }
    }

    /**
     * CFHGeneralData 字段名称
     */
    public static class CFHGeneralDataField {

        public static final String UPDATE_TIME = "updateTime";

        public static final String CREATE_TIME = "createTime";

        public static final String ID = "_id";

        private CFHGeneralDataField() {
            throw new IllegalStateException(CommonConstant.UTILITY_CLASS);
        }
    }

    /**
     * CFHProductReadHq 字段名称
     */
    public static class CFHProductReadHqField {

        public static final String SALE_TYPE = "saleType";

        public static final String SOURCE_NAME = "sourceName";

        private CFHProductReadHqField() {
            throw new IllegalStateException(CommonConstant.UTILITY_CLASS);
        }
    }

    /**
     * RegularInspectionReportContentField 字段名称
     */
    public static class RegularInspectionReportContentField {

        public static final String REPORT_CONTENT_ID = "reportContentId";

        public static final String REPORT_ID = "reportId";

        public static final String PRODUCT_CODE = "productCode";

        public static final String PRODUCT_NAME = "productName";

        public static final String FUND_SIZE = "fundSize";

        public static final String MISSING_INFORMATION_TYPE = "missingInformationType";

        public static final String CREATE_TIME = "createTime";

        public static final String IS_CLOSE = "isClose";
        public static final String IS_HOLD = "isHold";
        public static final String  STABILSH_DATE= "stabilshDate";
        public static final String MANAGER = "manager";
        public static final String P_DATE = "pDate";

        private RegularInspectionReportContentField() {
            throw new IllegalStateException(CommonConstant.UTILITY_CLASS);
        }
    }

    /**
     * RegularInspectionReportField 字段名称
     */
    public static class RegularInspectionReportField {

        public static final String REPORT_ID = "reportId";

        public static final String REPORT_NAME = "reportName";

        public static final String REPORT_TYPE = "reportType";

        public static final String CHECK_TIME = "checkTime";
        public static final String UPDATE_TIME = "updateTime";
        /**
         * 需要触发立即检查
         */
        public static final String REFRESH_THE_MARKER = "TTFund.CFHCache.GDLCReportCheck";
        /**
         * 同步交易数据时间断点
         */
        public static final String TRADE_DATA_HOUR_POINT = "TTFund.CFHCache.CFHTradeDataOnHourRelocateService";

        private RegularInspectionReportField() {
            throw new IllegalStateException(CommonConstant.UTILITY_CLASS);
        }
    }

    /**
     * CFHTradeDB -> TRADEDATE 字段名称
     */
    public static class TradeDateField {
        public static final String DATE_TIME ="DATETIME";

        public static final String IS_TRADE_DATE = "ISTDATE";

        public static final String _ID = "_id";

        private TradeDateField() {
            throw new IllegalStateException(CommonConstant.UTILITY_CLASS);
        }
    }

    private CFHMongodbConstant() {
        throw new IllegalStateException(CommonConstant.UTILITY_CLASS);
    }
}
