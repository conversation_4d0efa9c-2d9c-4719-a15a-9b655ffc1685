package ttfund.web.fortuneservice.constant;

/**
 * <AUTHOR>
 * 检查的报告类型 枚举
 * @date 2023/5/12 14:52
 */
public enum ReportTypeEnum {
    /**
     * 1-私募逾期报告
     */
    SIMU_YQ(1),
    /**
     * 2-私募检查报告
     */
    SIMU_JC(2),
    /**
     * 3-资管产品检查报告
     */
    ZIGUAN_JC(3),
    /**
     * 4-资管产品预期报告
     */
    ZIGUAN_YQ(4),
    /**
     * 5-私募净值逾期报告
     */
    SIMUJZ_YQ(5),
    /**
     * 1-私募连续逾期报告
     */
    SIMU_YQ2(6),
    ;

    private int type;

    ReportTypeEnum(int type){
        this.type = type;
    }

    public int getType() {
        return type;
    }
}
