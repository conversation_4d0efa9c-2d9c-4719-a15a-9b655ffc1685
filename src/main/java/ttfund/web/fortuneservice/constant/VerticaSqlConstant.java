package ttfund.web.fortuneservice.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/2/25 16:37
 */
public class VerticaSqlConstant {
   public static final String CALCULATE_TRADE_ROUTE_INFO_ROUTE = "insert into  Trade_Route_FundInfo_Day\n" +
           "(Calcday,Infocode,Shortname,FundType,RouteName,RouteChineseName,C_APP_SUM,C_APPAMOUNT_SUM,IsStockDay,GX_Rate,createtime) \n" +
           "with tmp as (\n" +
           "    select C_CUSTOMERNO,\n" +
           "           C_FUNDCODE,\n" +
           "           C_BUSINTYPE,\n" +
           "           C_APPAMOUNT,\n" +
           "           C_APPTIME,\n" +
           "           C_TRANSACTIONDATE,\n" +
           "           C_PARENTBUSINTYPE,\n" +
           "           C_REFERENCE,\n" +
           "           C_TRACENO,\n" +
           "           C_PAYTYPE,\n" +
           "           C_SUBACCOUNTNO,\n" +
           "           C_TRANSACTIONACCOUNTID,\n" +
           "           C_APPSHEETSERIALNO\n" +
           "    from TB_BUSINAPP_N_NEW\n" +
           "    where 1 = 1\n" +
           "      and date(C_APPTIME) = date('%s') - 1\n" +
           "      and C_REFERENCE = '2'\n" +
           "      and C_APPSTATE in ('10',\n" +
           "                         '11')\n" +
           "      and C_BUSINTYPE in ('20',\n" +
           "                          '22')\n" +
           "      and C_TRANSACTIONACCOUNTID not in (\n" +
           "        select distinct asset_tx_acct_id\n" +
           "        from ZNTG_TB_IC_DPST_RCRD)\n" +
           "      --排除投顾\n" +
           "      and (C_SUBACCOUNTNO is null\n" +
           "        or C_SUBACCOUNTNO not in (\n" +
           "            select SUBACCOUNTNO\n" +
           "            from ZZH_tb_subaccountinfo\n" +
           "            where FOLLOWEDSUBACCOUNTNO is not null))\n" +
           "      --排除跟投组合\n" +
           "      and C_CUSTOMERNO not in (\n" +
           "        select C_CUSTOMERNO\n" +
           "        from USER_TB_CUSTOMERDETAIL\n" +
           "        where nvl(C_INDORINS,\n" +
           "                  '0') != '0')\n" +
           "      --排除机构\n" +
           "    union all\n" +
           "    select C_CUSTOMERNO,\n" +
           "           C_FUNDCODE,\n" +
           "           C_BUSINTYPE,\n" +
           "           C_APPAMOUNT,\n" +
           "           C_APPTIME,\n" +
           "           C_TRANSACTIONDATE,\n" +
           "           C_PARENTBUSINTYPE,\n" +
           "           C_REFERENCE,\n" +
           "           C_TRACENO,\n" +
           "           C_PAYTYPE,\n" +
           "           C_SUBACCOUNTNO,\n" +
           "           C_TRANSACTIONACCOUNTID,\n" +
           "           C_APPSHEETSERIALNO\n" +
           "    from TB_CEM_BUSINAPP_N_NEW\n" +
           "    where 1 = 1\n" +
           "      and date(C_APPTIME) = date('%s') - 1\n" +
           "      and C_REFERENCE = '2'\n" +
           "      and C_APPSTATE in ('10',\n" +
           "                         '11')\n" +
           "      and C_BUSINTYPE in ('20',\n" +
           "                          '22')\n" +
           "      and C_TRANSACTIONACCOUNTID not in (\n" +
           "        select distinct asset_tx_acct_id\n" +
           "        from ZNTG_TB_IC_DPST_RCRD)\n" +
           "      --排除投顾\n" +
           "      and (C_SUBACCOUNTNO is null\n" +
           "        or C_SUBACCOUNTNO not in (\n" +
           "            select SUBACCOUNTNO\n" +
           "            from ZZH_tb_subaccountinfo\n" +
           "            where FOLLOWEDSUBACCOUNTNO is not null))\n" +
           "      --排除跟投组合\n" +
           "      and C_CUSTOMERNO not in (\n" +
           "        select C_CUSTOMERNO\n" +
           "        from USER_TB_CUSTOMERDETAIL\n" +
           "        where nvl(C_INDORINS,\n" +
           "                  '0') != '0')\n" +
           "    --排除机构\n" +
           "),t2 as (select EventName,PageChieseName,ModuleChineseName,EventChineseName\n" +
           " from (\n" +
           "select EventName,replace(PageChieseName,'首页瀑布流','基金首页')PageChieseName,ModuleChineseName,EventChineseName,CreateTime  from zp_app.APP_EVENT_RELATION_INFO\n" +
           "where eventname not like '%%show%%'\n" +
           "and eventname  not in  ('app.gddh.home','app.gddh.market','app.gddh.news','app.gddh.favor','app.gddh.trade')\n" +
           "union all\n" +
           "select EventName ,'基金首页' PageChieseName, '底部导航',EventChineseName ,CreateTime from  zp_app.APP_EVENT_RELATION_INFO\n" +
           " where  eventname   in  ('app.gddh.home','app.gddh.market','app.gddh.news','app.gddh.favor','app.gddh.trade')\n" +
           ")t\n" +
           "limit 1 over (partition by eventname order by CreateTime desc )\n" +
           "),\n" +
           "     tmp1 as (\n" +
           "         select C_APPSHEETSERIALNO,\n" +
           "                max(decode(rk, 2, a.pagechinesename || '.' || modulechinesename || '.' || a.eventchinesename)) ||\n" +
           "                coalesce(max(decode(rk, 3,\n" +
           "                                    '-' || a.pagechinesename || '.' || modulechinesename || '.' || a.eventchinesename)),\n" +
           "                         '') || coalesce(max(decode(rk, 4,\n" +
           "                                                    '-' || a.pagechinesename || '.' || modulechinesename || '.' ||\n" +
           "                                                    a.eventchinesename)),\n" +
           "                                         '') || coalesce(max(decode(rk, 5, '-' || a.pagechinesename || '.' ||\n" +
           "                                                                           modulechinesename || '.' ||\n" +
           "                                                                           a.eventchinesename)),\n" +
           "                                                         '') || coalesce(max(decode(rk, 6,\n" +
           "                                                                                    '-' || a.pagechinesename || '.' ||\n" +
           "                                                                                    modulechinesename || '.' ||\n" +
           "                                                                                    a.eventchinesename)),\n" +
           "                                                                         '') || coalesce(max(decode(rk, 7, '-' ||\n" +
           "                                                                                                           a.pagechinesename ||\n" +
           "                                                                                                           '.' ||\n" +
           "                                                                                                           modulechinesename ||\n" +
           "                                                                                                           '.' ||\n" +
           "                                                                                                           a.eventchinesename)),\n" +
           "                                                                                         '') || coalesce(\n" +
           "                        max(decode(rk, 8,\n" +
           "                                   '-' || a.pagechinesename || '.' || modulechinesename || '.' || a.eventchinesename)),\n" +
           "                        '') || coalesce(max(decode(rk, 9, '-' || a.pagechinesename || '.' || modulechinesename || '.' ||\n" +
           "                                                          a.eventchinesename)),\n" +
           "                                        '') || coalesce(max(decode(rk, 10, '-' || a.pagechinesename || '.' ||\n" +
           "                                                                           modulechinesename || '.' ||\n" +
           "                                                                           a.eventchinesename)),\n" +
           "                                                        '') || coalesce(max(decode(rk, 11,\n" +
           "                                                                                   '-' || a.pagechinesename || '.' ||\n" +
           "                                                                                   modulechinesename || '.' ||\n" +
           "                                                                                   a.eventchinesename)),\n" +
           "                                                                        '') || coalesce(max(decode(rk, 12, '-' ||\n" +
           "                                                                                                           a.pagechinesename ||\n" +
           "                                                                                                           '.' ||\n" +
           "                                                                                                           modulechinesename ||\n" +
           "                                                                                                           '.' ||\n" +
           "                                                                                                           a.eventchinesename)),\n" +
           "                                                                                        '') || coalesce(\n" +
           "                        max(decode(rk, 13,\n" +
           "                                   '-' || a.pagechinesename || '.' || modulechinesename || '.' || a.eventchinesename)),\n" +
           "                        '') || coalesce(max(decode(rk, 14,\n" +
           "                                                   '-' || a.pagechinesename || '.' || modulechinesename || '.' ||\n" +
           "                                                   a.eventchinesename)),\n" +
           "                                        '') || coalesce(max(decode(rk, 15, '-' || a.pagechinesename || '.' ||\n" +
           "                                                                           modulechinesename || '.' ||\n" +
           "                                                                           a.eventchinesename)),\n" +
           "                                                        '') || coalesce(max(decode(rk, 16,\n" +
           "                                                                                   '-' || a.pagechinesename || '.' ||\n" +
           "                                                                                   modulechinesename || '.' ||\n" +
           "                                                                                   a.eventchinesename)),\n" +
           "                                                                        '')  as RouteChineseName,\n" +
           "                max(decode(rk, 2, a.eventname)) || coalesce(max(decode(rk, 3, '-' || a.eventname)),\n" +
           "                                                            '') || coalesce(max(decode(rk, 4, '-' || a.eventname)),\n" +
           "                                                                            '') ||\n" +
           "                coalesce(max(decode(rk, 5, '-' || a.eventname)),\n" +
           "                         '') || coalesce(max(decode(rk, 6, '-' || a.eventname)),\n" +
           "                                         '') || coalesce(max(decode(rk, 7, '-' || a.eventname)),\n" +
           "                                                         '') || coalesce(max(decode(rk, 8, '-' || a.eventname)),\n" +
           "                                                                         '') ||\n" +
           "                coalesce(max(decode(rk, 9, '-' || a.eventname)),\n" +
           "                         '') || coalesce(max(decode(rk, 10, '-' || a.eventname)),\n" +
           "                                         '') || coalesce(max(decode(rk, 11, '-' || a.eventname)),\n" +
           "                                                         '') || coalesce(max(decode(rk, 12, '-' || a.eventname)),\n" +
           "                                                                         '') ||\n" +
           "                coalesce(max(decode(rk, 13, '-' || a.eventname)),\n" +
           "                         '') || coalesce(max(decode(rk, 14, '-' || a.eventname)),\n" +
           "                                         '') || coalesce(max(decode(rk, 15, '-' || a.eventname)),\n" +
           "                                                         '') || coalesce(max(decode(rk, 16, '-' || a.eventname)),\n" +
           "                                                                         '') as RouteName\n" +
           "         from (\n" +
           "                  select C_APPSHEETSERIALNO,\n" +
           "                         a.eventname,\n" +
           "                         a.eventchinesename,\n" +
           "                         modulechinesename,\n" +
           "                         a.pagechinesename,\n" +
           "                         case\n" +
           "                             when source = '购买' then 16\n" +
           "                             when source != '购买' then row_number() over (partition by C_APPSHEETSERIALNO\n" +
           "                                 order by\n" +
           "                                     source desc )\n" +
           "                             end as rk\n" +
           "                  from zp_app.USER_Trade_Route a\n" +
           "                           left join t2 b on\n" +
           "                      a.eventname = b.eventname\n" +
           "                  where source is not null) a\n" +
           "         group by C_APPSHEETSERIALNO),\n" +
           "     tmp2 as (\n" +
           "         Select date(eventtime)                      as Calcday,\n" +
           "                a.C_FUNDCODE                         as Infocode,\n" +
           "                Shortname,\n" +
           "                Ftype                                as FundType,\n" +
           "                RouteName,\n" +
           "                RouteChineseName,\n" +
           "                count(distinct a.C_APPSHEETSERIALNO) AS C_APP_SUM,\n" +
           "                SUM(a.C_APPAMOUNT)                   AS C_APPAMOUNT_SUM\n" +
           "         from (\n" +
           "                  select *\n" +
           "                  from zp_app.USER_Trade_Route\n" +
           "                  limit 1 over(partition by C_APPSHEETSERIALNO order by eventtime desc)) a\n" +
           "                  inner join fund_jbxx b on\n" +
           "             a.C_FUNDCODE = b.FCODE\n" +
           "                  inner join tmp1 c on\n" +
           "             a.C_APPSHEETSERIALNO = c.C_APPSHEETSERIALNO\n" +
           "                  inner join tmp d on\n" +
           "             a.C_APPSHEETSERIALNO = d.C_APPSHEETSERIALNO\n" +
           "         group by date(eventtime), Infocode, Shortname, Ftype, RouteName, RouteChineseName),\n" +
           "     tmp3 as (\n" +
           "         select date(a.C_APPTIME)  as Calcday,\n" +
           "                Sum(a.C_APPAMOUNT) as ALL_APPAMOUNT_SUM\n" +
           "         from tmp a\n" +
           "         group by date(a.C_APPTIME)),\n" +
           "     tmp4 as (\n" +
           "         Select date(a.C_APPTIME)                    as Calcday,\n" +
           "                a.C_FUNDCODE                         as Infocode,\n" +
           "                Shortname,\n" +
           "                Ftype                                as FundType,\n" +
           "                '其他'                                 as RouteChineseName,\n" +
           "                '其他'                                 as RouteName,\n" +
           "                count(distinct a.C_APPSHEETSERIALNO) AS C_APP_SUM,\n" +
           "                SUM(a.C_APPAMOUNT)                   AS C_APPAMOUNT_SUM\n" +
           "         from tmp a\n" +
           "                  left join (\n" +
           "             select *\n" +
           "             from zp_app.USER_Trade_Route\n" +
           "             limit 1 over(partition by C_APPSHEETSERIALNO order by eventtime desc)) b on\n" +
           "             a.C_APPSHEETSERIALNO = b.C_APPSHEETSERIALNO\n" +
           "                  inner join fund_jbxx c on\n" +
           "             a.C_FUNDCODE = c.FCODE\n" +
           "         where b.C_APPSHEETSERIALNO is null\n" +
           "         group by date(a.C_APPTIME), Infocode, Shortname, Ftype, RouteChineseName, RouteName)\n" +
           "select a.*,\n" +
           "       cast(b.ISTDATE as numeric(1,\n" +
           "           0)) as IsStockDay,\n" +
           "       cast(C_APPAMOUNT_SUM * 100 / ALL_APPAMOUNT_SUM as numeric(38,\n" +
           "           4)) as GX_Rate,\n" +
           "       date(sysdate)\n" +
           "from (\n" +
           "         Select *\n" +
           "         from tmp2\n" +
           "         Union all\n" +
           "         Select *\n" +
           "         from tmp4) a\n" +
           "         Inner join FUND_TRADEDATE b on\n" +
           "    a.Calcday = b.'DATETIME'\n" +
           "         Inner join tmp3 c on\n" +
           "    a.Calcday = c.Calcday;";

   public static final String CALCULATE_TRADE_EVENT = "insert into  Trade_Route_Event_Day\n" +
           "(Calcday,EventName,EventChineseName,PageChineseName,C_APP_SUM,C_APPAMOUNT_SUM,IsStockDay,ZH_Rate,GX_Rate,createtime) with tmp as (\n" +
           "select C_CUSTOMERNO,C_FUNDCODE,C_BUSINTYPE,C_APPAMOUNT,C_APPTIME,C_TRANSACTIONDATE,C_PARENTBUSINTYPE,C_REFERENCE,C_TRACENO,C_PAYTYPE,C_SUBACCOUNTNO,C_TRANSACTIONACCOUNTID,C_APPSHEETSERIALNO\n" +
           "from TB_BUSINAPP_N_NEW\n" +
           "where 1=1\n" +
           "and date(C_APPTIME)=date('%s')-1\n" +
           "and C_REFERENCE='2'\n" +
           "and C_APPSTATE in ('10','11')\n" +
           "and C_BUSINTYPE in ('20','22')\n" +
           "\n" +
           "union all\n" +
           "select C_CUSTOMERNO,C_FUNDCODE,C_BUSINTYPE,C_APPAMOUNT,C_APPTIME,C_TRANSACTIONDATE,C_PARENTBUSINTYPE,C_REFERENCE,C_TRACENO,C_PAYTYPE,C_SUBACCOUNTNO,C_TRANSACTIONACCOUNTID,C_APPSHEETSERIALNO\n" +
           "from TB_CEM_BUSINAPP_N_NEW\n" +
           "where 1=1\n" +
           "and date(C_APPTIME)=date('%s')-1\n" +
           "and C_REFERENCE='2'\n" +
           "and C_APPSTATE in ('10','11')\n" +
           "and C_BUSINTYPE in ('20','22')\n" +
           ")\n" +
           "\n" +
           ",tmp2 as\n" +
           "(select\n" +
           "date(a.C_APPTIME) as Calcday,\n" +
           "Sum(a.C_APPAMOUNT) as ALL_APPAMOUNT_SUM\n" +
           "from tmp a\n" +
           "where C_TRANSACTIONACCOUNTID not in ( select distinct asset_tx_acct_id from ZNTG_TB_IC_DPST_RCRD )--排除投顾\n" +
           "and ( C_SUBACCOUNTNO is null or C_SUBACCOUNTNO not in (select SUBACCOUNTNO from  ZZH_tb_subaccountinfo where FOLLOWEDSUBACCOUNTNO is not null ))--排除跟投组合\n" +
           "and C_CUSTOMERNO not in  (select C_CUSTOMERNO from  USER_TB_CUSTOMERDETAIL where nvl(C_INDORINS,'0') !='0')--排除机构\n" +
           "group by date(a.C_APPTIME)\n" +
           ")\n" +
           "\n" +
           "\n" +
           ",tmp1 as (Select date(eventtime) as Calcday, EventName,EventChineseName,  PageChineseName,\n" +
           "count(distinct C_APPSHEETSERIALNO) AS C_APP_SUM, count(distinct stdtradeid) AS C_APP_ID ,SUM(C_APPAMOUNT) AS C_APPAMOUNT_SUM\n" +
           "from zp_app.USER_Trade_Route where date(c_apptime) = date('%s')-1\n" +
           "group by date(eventtime), EventName,EventChineseName,  PageChineseName)\n" +
           "\n" +
           "\n" +
           "select  a.Calcday , a.EventName,a.EventChineseName,  a.PageChineseName,C_APP_SUM, C_APPAMOUNT_SUM, b.IsStockDay,\n" +
           "cast( C_APP_ID * 100/ visitnum as numeric(38,4)) as ZH_Rate ,\n" +
           "cast( C_APPAMOUNT_SUM * 100/ ALL_APPAMOUNT_SUM as numeric(38,4)) as GX_Rate ,date(sysdate)\n" +
           "from tmp1 a\n" +
           "INNER JOIN\n" +
           "(select calcday, eventname, IsStockDay, sum(eventnum)as eventnum, sum(visitnum) as visitnum from ZP_APP.T_APP_HUB_EVENT_PROD_DAY\n" +
           "group by calcday, eventname, IsStockDay) b\n" +
           "on a.Calcday = b.Calcday AND a.eventname = b. eventname\n" +
           "Inner join\n" +
           "tmp2 c\n" +
           "on a.Calcday = c.Calcday;";

   public static final String SQL_INSERT_INTO_SEARCHINDEX_BASIC_ALL_DRCT = "insert into" + " " + VerticaTableNameConstant.SEARCHINDEX_BASIC_ALL_DRCT
           + "(index_enum, index_type, keyword, index_date, index_value, index_from, updatetime)"
           + "VALUES (?,?,?,?,?,?,NOW())";

   public static final String SQL_SELECT_LATEST_WEIBO_SEARCH_INDEX = "select index_enum, index_type, index_date from" + " "
           + VerticaTableNameConstant.SEARCHINDEX_BASIC_ALL_DRCT + " " + "where index_enum = '3'"
           + " " + "order by index_date DESC LIMIT 1";

   //select keyword,MAX(index_date) from SPARK.SEARCHINDEX_BASIC_ALL_DRCT where index_enum = '3' group by keyword;
   public static final String SQL_SELECT_LATEST_SEARCH_INDEX_GROUP_BY_KEYWORD = "select keyword,MAX(index_date) AS index_date from" + " "
           + VerticaTableNameConstant.SEARCHINDEX_BASIC_ALL_DRCT + " " + "where index_enum = '%s'"
           + " " + "group by keyword";

   public static final String SQL_SELECT_LATEST_TIME_OF_PUBLIC_FUND_RETATION_SCALE = "select PYEAR, PQRT from " + VerticaTableNameConstant.ASS_RETATION_ORG_BASIC_ALL_DRCT
           + " order by  PYEAR desc, PQRT desc limit 1";

   public static final String SQL_INSERT_PUBLIC_FUND_RETATION_SCALE_DATA = "insert into " + VerticaTableNameConstant.ASS_RETATION_ORG_BASIC_ALL_DRCT
           + "(PYEAR, PQRT, PRANK, ORGNAME, PGPARC, NOMMFPARC, CREATETIME, UPDATETIME)"
           + "VALUES (?,?,?,?,?,?,NOW(),NOW())";

   public static final String SQL_SELECT_LATEST_TIME_OF_PUBLIC_FUND_INDUSTRY = "select PYEAR, PMONTH from " + VerticaTableNameConstant.ASS_RETATION_MARKET_BASIC_ALL_DRCT
           + " order by  PYEAR desc, PMONTH desc limit 1";

   public static final String SQL_INSERT_PUBLIC_FUND_INDUSTRY_DATA = "insert into " + VerticaTableNameConstant.ASS_RETATION_MARKET_BASIC_ALL_DRCT
           + "(PYEAR, PMONTH, PTYPE, FUNDNUM, FUNDSHARE, FUNDPARC, CREATETIME, UPDATETIME)"
           + "VALUES (?,?,?,?,?,?,NOW(),NOW())";

   public static final String SQL_SELECT_LATEST_TIME_OF_NON_MONETARY_SCALE_DATA = "select PYEAR, PQRT from " + VerticaTableNameConstant.ASS_MNG_AVGPARC_BASIC_ALL_DRCT
           + " order by  PYEAR desc, PQRT desc limit 1";

   public static final String SQL_INSERT_NON_MONETARY_SCALE_DATA = "insert into " + VerticaTableNameConstant.ASS_MNG_AVGPARC_BASIC_ALL_DRCT
           + "(PYEAR, PQRT, PRANK, MNGNAME, NOMMFAVGPARC, CREATETIME, UPDATETIME)"
           + "VALUES (?,?,?,?,?,NOW(),NOW())";

   public static final String SQL_DELETE_SALES_ORGANIZATION_DATA = "delete from " + VerticaTableNameConstant.ASS_ORG_JBXX_BASIC_ALL_DRCT
           + " where 1 = 1";

   public static final String SQL_INSERT_SALES_ORGANIZATION_DATA = "insert into " + VerticaTableNameConstant.ASS_ORG_JBXX_BASIC_ALL_DRCT
           + "(ORGNAME, REGDEST, ORGTYPE, APRVLTIME, CREATETIME, UPDATETIME)"
           + "VALUES (?,?,?,?,NOW(),NOW())";

   public static final String SQL_SELECT_TB_LIVESCORE_FIELDS = "SELECT A.LIVEID \n" +
           ",c.Channel_ID\n" +
           ",D.STARTTIME,D.ENDTIME,D.TITLE,D.CFHID,E.ACCOUNTNAME CFHNAME \n" +
           ",STATUS ,ISDELETE,ISHIDE\n" +
           ",C.HEAT_DEGREE,C.LIKE_NUM \n" +
           ",A.CLICKNUM*2.01 CLICKNUM,A.CLICKUSERNUM*2.01 CLICKUSERNUM,A.REPLYUSERNUM,A.AVGDURATION \n" +
           ",A.FUNDCLICKUSERNUM/A.CLICKUSERNUM FUNDCLICKRATIO  \n" +
           ",A.SHAREUSERNUM/A.CLICKUSERNUM SHARERATIO \n" +
           ",A.FOLLOWUSERNUM/A.CLICKUSERNUM FOLLOWRATIO \n" +
           ",A.REPLYUSERNUM/A.CLICKUSERNUM REPLYRATIO \n" +
           ",B.TOTAL_SCORE,B.WATCH_SCORE,B.INTER_SCORE,B.FUND_SCORE,B.UPDATETIME LIVESCORE_UPDATETIME  \n" +
           ",f.FavorCustNum,f.PlanNum,f.updatetime LIVESALE_UPDATETIME\n" +
           "FROM content.LIVE_TB_SHUYUCALC_STAT_ALL_DRCT A\n" +
           "LEFT JOIN content.LIVE_TB_LIVESCORE_STAT_ALL_APP B ON A.LIVEID=B.LIVEID\n" +
           "LEFT JOIN content.TB_LIVE_HEAT_DEGREE_STAT_ALL_DRCT C ON A.LIVEID=C.SHOW_ID\n" +
           "LEFT JOIN content.TB_LIVEBROADCAST_BASIC_ALL_SYN D ON A.LIVEID=D.C_ID\n" +
           "LEFT JOIN content.CFH_FOLLOW_BASIC_ALL_SYN E ON D.CFHID=E.ACCOUNTID\n" +
           "left join (select liveid,sum(FavorCustNum) FavorCustNum,sum(PlanNum) PlanNum,max(updatetime)updatetime from  content.LIVESALE_STAT_ALL_APP where datediff(dd,LiveStartTime,visitdate) between 0 and 2  group  by liveid) f on a.liveid=f.liveid\n" +
           "WHERE   A.CLICKUSERNUM>100 \n" +
           "AND ( A.UPDATETIME>? OR B.UPDATETIME>? OR C.UPDATETIME>? OR D.UPDATETIME>? OR F.UPDATETIME>?)";

   public static final String SQL_SELECT_MAX_UPDATTIME = "select  max(updatetime) updatetime\n" +
           "from (\n" +
           "select  updatetime from content.LIVE_TB_SHUYUCALC_STAT_ALL_DRCT\n" +
           "union select  updatetime from content.LIVE_TB_LIVESCORE_STAT_ALL_APP\n" +
           "union select  updatetime from content.TB_LIVE_HEAT_DEGREE_STAT_ALL_DRCT\n" +
           "union select  updatetime from content.TB_LIVEBROADCAST_BASIC_ALL_SYN\n" +
           "union select updatetime from content.LIVESALE_STAT_ALL_APP\n" +
           ") A\n" +
           "where updatetime>?;";

   public static final String SQL_INSERT_CFH_TRADE_DETAIL = "insert into " + VerticaTableNameConstant.TTTRADEDETAIL_BASIC_SYN
           + "(APPLYNO, TRADETYPE, PARENTTRADETYPE, PARENTAPPLYNO, TRACENO, FUNDCODE, APPLYAMOUNT, APPLYVOL, APPLYTIME, TRANSACTIONDATE, APPLYDAY, APPLYHOUR) "
           + "values (?,?,?,?,?,?,?,?,?,?,?,?)";

   public static final String SQL_STAT_TRADE_DETAIL = "with tmp as (select a.applyNo, a.applyVol * b.NAV applyAmount, a.parentApplyNo\n" +
           "             from BUSIN.TTTRADEDETAIL_BASIC_SYN a\n" +
           "                      inner join (SELECT A.FCODE, A.NAV\n" +
           "                                  FROM FUND.OFNAV_ALL_BASIC_SYN A\n" +
           "                                           INNER JOIN (select FCODE, MAX(PDATE) PDATE\n" +
           "                                                       from FUND.OFNAV_ALL_BASIC_SYN\n" +
           "                                                       WHERE PDATE >= SYSDATE - 15\n" +
           "                                                       GROUP BY FCODE) B ON A.FCODE = B.FCODE AND A.PDATE = B.PDATE\n" +
           "                                  union\n" +
           "                                  select fcode,\n" +
           "                                         case\n" +
           "                                             when feature like '%090%' then 0.01\n" +
           "                                             when feature like '%100%' then 100\n" +
           "                                             else 1 end\n" +
           "                                  from FUND.JBXX_BASIC_ALL_SYN\n" +
           "                                  where fundtype in ('004', '005')) b\n" +
           "                                 on a.fundCode = b.fcode and a.tradeType in ('24', '97') and a.transactionDate = ? and\n" +
           "                                    a.REVOKE = 0),\n" +
           "     tmpb as (select a.applyNo, b.applyAmount\n" +
           "              from BUSIN.TTTRADEDETAIL_BASIC_SYN a\n" +
           "                       inner join tmp b on a.parentApplyNo = b.parentApplyNo\n" +
           "              where a.parentTradeType in ('811', '812', '815')\n" +
           "                and a.tradeType in ('22')\n" +
           "                and a.transactionDate = ?\n" +
           "                and a.REVOKE = 0)\n" +
           "select fundcode,\n" +
           "       jjgsid,\n" +
           "       busintype,\n" +
           "       Applyday,\n" +
           "       Applyhour,\n" +
           "       sum(applyAmount) applyAmount,\n" +
           "       count(1)         applynum,\n" +
           "       shortname,\n" +
           "       transactionDate\n" +
           "from (select a.fundCode,\n" +
           "             d.jjgsid,\n" +
           "             case\n" +
           "                 when a.tradeType in ('20', '22') then 1\n" +
           "                 when a.tradeType = '39' then 2\n" +
           "                 when a.tradetype in ('24', '97') then 3 end busintype,\n" +
           "             case\n" +
           "                 when a.tradetype in ('24', '97') then b.applyAmount\n" +
           "                 when a.parentTradeType in ('811', '812', '815') and a.tradeType in ('22') then c.applyAmount\n" +
           "                 else a.applyAmount end                      applyAmount,\n" +
           "             a.Applyday,\n" +
           "             a.Applyhour,\n" +
           "             shortname,\n" +
           "             transactionDate\n" +
           "      from BUSIN.TTTRADEDETAIL_BASIC_SYN a\n" +
           "               left join tmp b on a.applyNo = b.applyNo\n" +
           "               left join tmpb c on a.applyNo = c.applyNo\n" +
           "               inner join FUND.JBXX_BASIC_ALL_SYN d on a.fundCode = d.fcode\n" +
           "      where a.tradeType in ('20', '22', '39', '24', '97')\n" +
           "        and a.transactionDate = ?\n" +
           "        and a.REVOKE = 0) a\n" +
           "group by fundcode, jjgsid, busintype, Applyday, Applyhour, shortname, transactionDate";

   public static final String SQL_DELETE_STAT_DATA_BY_APPLY_DAY = "delete from " + VerticaTableNameConstant.FUND_APPLY_DAY_STAT_ALL + " where APPLYDAY =?";

   public static final String SQL_DELETE_STAT_DATA_BY_TRANSACTION_DAY = "delete from " + VerticaTableNameConstant.FUND_APPLY_DAY_STAT_ALL + " where transactionDate =?";

   public static final String SQL_INSERT_STAT_DATA = "insert into " + VerticaTableNameConstant.FUND_APPLY_DAY_STAT_ALL +
           " (FUNDCODE, JJGSID, BUSINTYPE, APPLYDAY, APPLYHOUR, APPLYAMOUNT, APPLYNUM, SHORTNAME, transactionDate) " +
           "values(?, ?, ?, ?, ?, ?, ?, ?, ?)";

   public static final String SQL_DELETE_TRADE_DETAILS_BY_TRANSACTION_DATE = "delete from " + VerticaTableNameConstant.TTTRADEDETAIL_BASIC_SYN + " where TRANSACTIONDATE = ?";

   public static final String SQL_CHECK_FUND_VISIT_DATA_IS_UPDATE = "SELECT count(1) as updateCount FROM BACKSTAGE.KETTLE_UPDATE_RECORD  a inner join\n" +
           "FUND.TRADEDATE_BASIC_ALL_SYN b on a.CALENDARDATE =b.datetime+1\n" +
           "where tablename in ( 'FUND_SEARCHSTAT_STAT_ALL_APP', 'T_APP_HUB_EVENT_INFOCODE_DAY_STAT_ALL_SYN')\n" +
           "AND CALENDARDATE =TRUNC(SYSDATE) and b.istdate=1 HAVING COUNT(1)=2";

   public static final String SQL_GET_FUND_VISIT_DATA = "with tmp as (\n" +
           "select belongday,lastday from fund.TRADEDATE_BASIC_ALL_SYN\n" +
           "where datetime =trunc(sysdate) - 1)\n" +
           ",tmp1 as (\n" +
           "select b.infocode ,sum(b.visitnum) zxvisit\n" +
           "from tmp a inner join behavior.T_APP_HUB_EVENT_INFOCODE_DAY_STAT_ALL_SYN b on a.belongday=b.calcday\n" +
           "and b.eventname ='pz.link'  group by  b.infocode\n" +
           ")\n" +
           ",tmp2 as (\n" +
           "select b.infocode ,sum(b.visitnum) zrvisit\n" +
           "from tmp a inner join behavior.T_APP_HUB_EVENT_INFOCODE_DAY_STAT_ALL_SYN b on a.lastday=b.calcday\n" +
           "and b.eventname ='pz.link' group by  b.infocode\n" +
           ")\n" +
           "select b.infocode as fundCode, b.zxvisit as visitSumBelongDay,c.zrvisit as visitSumLastDay\n" +
           ",case when c.zrvisit is null then null else ROUND (100*(b.zxvisit/c.zrvisit -1),2) end pzChange\n" +
           ",d.searchuv as searchSumBelongDay,e.searchuv as searchSumLastDay,\n" +
           "case when e.searchuv is null then null else ROUND (100*(d.searchuv/e.searchuv -1),2) end ssChange\n" +
           "from tmp  a\n" +
           "inner join tmp1  b on 1=1\n" +
           "left join  tmp2 c on b.infocode=c.infocode\n" +
           "left join BEHAVIOR.FUND_SEARCHSTAT_STAT_ALL_APP d on b.infocode= d.fcode and d.pdate=a.belongday\n" +
           "left join BEHAVIOR.FUND_SEARCHSTAT_STAT_ALL_APP e on b.infocode= e.fcode and e.pdate=a.lastday";

   private static final String FUNDCUSTNO_FEATURE_AGE = "select  'AGE' FEATURETYPE,b.TTJJ_FUNDCODE FUNDCODE,c.shortname SHORTNAME, c.jjgsid JJGSID, \n" +
           "case when   round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365)  <20 then  '20岁以下'\n" +
           "         when  round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365) >=20 and round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365) <30 then '20到30岁'\n" +
           "          when round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365) >=30 and round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365) <40 then '30到40岁'\n" +
           "         when  round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365)>=40 and round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365) <50 then '40到50岁'\n" +
           "         when  round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365)  >=50    then '50岁以上'  else '未知'\n" +
           "         end  FEATURE,count(1)  NUM from\n" +
           " EMUSER.STTJJ_AGG_NORMALINFO a inner join  RETENTION.STTJJ_CUSTFUNDPOSIDETAILS_BASIC_ALL_SYN    b\n" +
           "  on a.FUNDCUSTNO=b.ttjj_custid \n" +
           "  inner join   fund.CODEALL_ALL_BASIC_APP c on b.TTJJ_FUNDCODE=c.fcode\n" +
           "   where b.ttjj_iscurposi=1   and  b.ttjj_custid  not in (select C_CUSTOMERNO from emuser.TB_CUSTOMERDETAIL_BASIC_ALL_SYN\n" +
           "where C_INDORINS in ('1','2') and C_ISDEL =0)\n" +
           "   group by b.TTJJ_FUNDCODE,case when   round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365)  <20 then  '20岁以下'\n" +
           "         when  round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365) >=20 and round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365) <30 then '20到30岁'\n" +
           "          when round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365) >=30 and round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365) <40 then '30到40岁'\n" +
           "         when  round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365)>=40 and round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365) <50 then '40到50岁'\n" +
           "         when  round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365)  >=50    then '50岁以上'  else '未知'\n" +
           "         end ,c.shortname,c.jjgsid";
   private static final String FUNDCUSTNO_FEATURE_PROVINCE = "select   'PROVINCE' FEATURETYPE,b.TTJJ_FUNDCODE FUNDCODE,c.shortname SHORTNAME, c.jjgsid JJGSID, \n" +
           "a.telPROVINCE  FEATURE,count(1)  NUM from\n" +
           "  EMUSER.STTJJ_AGG_NORMALINFO a inner join   RETENTION.STTJJ_CUSTFUNDPOSIDETAILS_BASIC_ALL_SYN  b\n" +
           "  on a.FUNDCUSTNO=b.ttjj_custid \n" +
           "  inner join   fund.CODEALL_ALL_BASIC_APP  c on b.TTJJ_FUNDCODE=c.fcode\n" +
           "   where b.ttjj_iscurposi=1 \n" +
           "   group by b.TTJJ_FUNDCODE,a.telPROVINCE,c.shortname,c.jjgsid";
   private static final String FUNDCUSTNO_FEATURE_POSIVOL = "select 'POSIVOL' FEATURETYPE, b.TTJJ_FUNDCODE FUNDCODE,c.shortname SHORTNAME, c.jjgsid JJGSID,\n" +
           "   case when   TTJJ_POSI_AMT <10 then '10以下'\n" +
           "                   when TTJJ_POSI_AMT >=10 and TTJJ_POSI_AMT <100   then '10-100'\n" +
           "            when TTJJ_POSI_AMT >=100 and TTJJ_POSI_AMT <1000   then '100-1000'\n" +
           "            when TTJJ_POSI_AMT >=1000 and TTJJ_POSI_AMT <5000   then '1000-5000'\n" +
           "            when TTJJ_POSI_AMT >=5000 and TTJJ_POSI_AMT <10000   then '5000-10000'\n" +
           "            when TTJJ_POSI_AMT >=10000 and TTJJ_POSI_AMT <100000   then '10000-100000'\n" +
           "             when TTJJ_POSI_AMT >=100000    then '100000以上'    end FEATURE ,count(1) NUM\n" +
           "             from   RETENTION.STTJJ_CUSTFUNDPOSIDETAILS_BASIC_ALL_SYN  b\n" +
           "             inner join  fund.CODEALL_ALL_BASIC_APP   c on b.ttjj_fundcode=c.fcode\n" +
           "       where b.ttjj_iscurposi=1 and  b.ttjj_custid  not in (select C_CUSTOMERNO from emuser.TB_CUSTOMERDETAIL_BASIC_ALL_SYN\n" +
           "where C_INDORINS in ('1','2') and C_ISDEL =0)\n" +
           "         group by b.TTJJ_FUNDCODE,c.shortname, c.jjgsid,case when  TTJJ_POSI_AMT <10 then '10以下'\n" +
           "                   when TTJJ_POSI_AMT >=10 and TTJJ_POSI_AMT <100   then '10-100'\n" +
           "            when TTJJ_POSI_AMT >=100 and TTJJ_POSI_AMT <1000   then '100-1000'\n" +
           "            when TTJJ_POSI_AMT >=1000 and TTJJ_POSI_AMT <5000   then '1000-5000'\n" +
           "            when TTJJ_POSI_AMT >=5000 and TTJJ_POSI_AMT <10000   then '5000-10000'\n" +
           "            when TTJJ_POSI_AMT >=10000 and TTJJ_POSI_AMT <100000   then '10000-100000'\n" +
           "             when TTJJ_POSI_AMT >=100000    then '100000以上'    end";
   public static final String FUNDCUSTNO_FEATURE_PRODIS = "select 'PRODIS' FEATURETYPE, a.fundcode FUNDCODE,c.shortname  SHORTNAME, c.jjgsid JJGSID,  case when ClearReturn2<-0.2 then '-20%以下'\n" +
           "                   when ClearReturn2>=-0.2 and ClearReturn2 < -0.1 then '-20%到-10%'\n" +
           "                  when ClearReturn2>=-0.1 and ClearReturn2 < -0.05 then '-10%到-5%'\n" +
           "                  when ClearReturn2>=-0.05 and ClearReturn2 < -0.03 then '-5%到-3%'\n" +
           "                  when ClearReturn2>=-0.03 and ClearReturn2 < -0.01 then '-3%到-1%'\n" +
           "                  when ClearReturn2>=-0.01 and ClearReturn2 < 0.01 then '-1%到1%'\n" +
           "                  when ClearReturn2>=0.01 and ClearReturn2 < 0.03 then '1%到3%'\n" +
           "                  when ClearReturn2>=0.03 and ClearReturn2 < 0.05 then '3%到5%'\n" +
           "                  when ClearReturn2>=0.05 and ClearReturn2 < 0.1 then '5%到10%'\n" +
           "                  when ClearReturn2>=0.1 and ClearReturn2 < 0.2 then '10%到20%'\n" +
           "                  when ClearReturn2>=0.2 then '20%以上' end FEATURE,count(1)  NUM               \n" +
           "                   \n" +
           "from emuser.USER_CYCLE_ASSETRETURN2_ONGOING_BASIC_ALL_APP a\n" +
           "   inner join  fund.CODEALL_ALL_BASIC_APP  c on a.fundcode=c.fcode\n" +
           " where customernotype ='个人'\n" +
           "  group by a.fundcode,c.shortname,c.jjgsid,case when ClearReturn2<-0.2 then '-20%以下'\n" +
           "                   when ClearReturn2>=-0.2 and ClearReturn2 < -0.1 then '-20%到-10%'\n" +
           "                  when ClearReturn2>=-0.1 and ClearReturn2 < -0.05 then '-10%到-5%'\n" +
           "                  when ClearReturn2>=-0.05 and ClearReturn2 < -0.03 then '-5%到-3%'\n" +
           "                  when ClearReturn2>=-0.03 and ClearReturn2 < -0.01 then '-3%到-1%'\n" +
           "                  when ClearReturn2>=-0.01 and ClearReturn2 < 0.01 then '-1%到1%'\n" +
           "                  when ClearReturn2>=0.01 and ClearReturn2 < 0.03 then '1%到3%'\n" +
           "                  when ClearReturn2>=0.03 and ClearReturn2 < 0.05 then '3%到5%'\n" +
           "                  when ClearReturn2>=0.05 and ClearReturn2 < 0.1 then '5%到10%'\n" +
           "                  when ClearReturn2>=0.1 and ClearReturn2 < 0.2 then '10%到20%'\n" +
           "                  when ClearReturn2>=0.2 then '20%以上' end";
   private static final String FUNDCUSTNO_FEATURE_PRODISTIME = "select 'PRODISTIME' FEATURETYPE, a.fundcode FUNDCODE,c.shortname  SHORTNAME, c.jjgsid JJGSID,\n" +
           "case when  enddate-startdate >30 and   enddate-startdate <=90 then '1-3个月'\n" +
           " when  enddate-startdate >90 and   enddate-startdate <=180 then '3-6个月'\n" +
           "  when  enddate-startdate >180 and   enddate-startdate <=365 then '6-12个月'\n" +
           "   when  enddate-startdate >365 and   enddate-startdate <=1080 then '1-3年'\n" +
           "    when  enddate-startdate >1080 then '超过3年' end CYSC,\n" +
           "case when ClearReturn2<-0.2 then '-20%以下'\n" +
           "                   when ClearReturn2>=-0.2 and ClearReturn2 < -0.1 then '-20%到-10%'\n" +
           "                  when ClearReturn2>=-0.1 and ClearReturn2 < -0.05 then '-10%到-5%'\n" +
           "                  when ClearReturn2>=-0.05 and ClearReturn2 < -0.03 then '-5%到-3%'\n" +
           "                  when ClearReturn2>=-0.03 and ClearReturn2 < -0.01 then '-3%到-1%'\n" +
           "                  when ClearReturn2>=-0.01 and ClearReturn2 < 0.01 then '-1%到1%'\n" +
           "                  when ClearReturn2>=0.01 and ClearReturn2 < 0.03 then '1%到3%'\n" +
           "                  when ClearReturn2>=0.03 and ClearReturn2 < 0.05 then '3%到5%'\n" +
           "                  when ClearReturn2>=0.05 and ClearReturn2 < 0.1 then '5%到10%'\n" +
           "                  when ClearReturn2>=0.1 and ClearReturn2 < 0.2 then '10%到20%'\n" +
           "                  when ClearReturn2>=0.2 then '20%以上' end FEATURE,count(1)  NUM\n" +
           "\n" +
           "from emuser.USER_CYCLE_ASSETRETURN2_ONGOING_BASIC_ALL_APP a\n" +
           "   inner join  fund.CODEALL_ALL_BASIC_APP  c on a.fundcode=c.fcode\n" +
           " where customernotype ='个人'\n" +
           "  group by a.fundcode,c.shortname,c.jjgsid,case when  enddate-startdate >30 and   enddate-startdate <=90 then '1-3个月'\n" +
           " when  enddate-startdate >90 and   enddate-startdate <=180 then '3-6个月'\n" +
           "  when  enddate-startdate >180 and   enddate-startdate <=365 then '6-12个月'\n" +
           "   when  enddate-startdate >365 and   enddate-startdate <=1080 then '1-3年'\n" +
           "    when  enddate-startdate >1080 then '超过3年' end, case when ClearReturn2<-0.2 then '-20%以下'\n" +
           "                   when ClearReturn2>=-0.2 and ClearReturn2 < -0.1 then '-20%到-10%'\n" +
           "                  when ClearReturn2>=-0.1 and ClearReturn2 < -0.05 then '-10%到-5%'\n" +
           "                  when ClearReturn2>=-0.05 and ClearReturn2 < -0.03 then '-5%到-3%'\n" +
           "                  when ClearReturn2>=-0.03 and ClearReturn2 < -0.01 then '-3%到-1%'\n" +
           "                  when ClearReturn2>=-0.01 and ClearReturn2 < 0.01 then '-1%到1%'\n" +
           "                  when ClearReturn2>=0.01 and ClearReturn2 < 0.03 then '1%到3%'\n" +
           "                  when ClearReturn2>=0.03 and ClearReturn2 < 0.05 then '3%到5%'\n" +
           "                  when ClearReturn2>=0.05 and ClearReturn2 < 0.1 then '5%到10%'\n" +
           "                  when ClearReturn2>=0.1 and ClearReturn2 < 0.2 then '10%到20%'\n" +
           "                  when ClearReturn2>=0.2 then '20%以上' end";
   private static final String FUNDCUSTNO_FEATURE_HOLDTIME = "select 'HOLDTIME' FEATURETYPE, a.fundcode FUNDCODE,c.shortname  SHORTNAME, c.jjgsid JJGSID, \n" +
           "case when  enddate-startdate <=30 then '1个月以内' \t\n" +
           "\twhen  enddate-startdate >30 and   enddate-startdate <=90 then '1-3个月'\n" +
           " when  enddate-startdate >90 and   enddate-startdate <=180 then '3-6个月'\n" +
           "  when  enddate-startdate >180 and   enddate-startdate <=365 then '6-12个月'\n" +
           "   when  enddate-startdate >365 and   enddate-startdate <=1080 then '1-3年'\n" +
           "    when  enddate-startdate >1080 then '超过3年' end FEATURE,count(1)  NUM    \n" +
           "from emuser.USER_CYCLE_ASSETRETURN2_ONGOING_BASIC_ALL_APP a\n" +
           "inner join  fund.CODEALL_ALL_BASIC_APP  c on a.fundcode=c.fcode\n" +
           " where customernotype ='个人'\n" +
           " group by  a.fundcode ,c.shortname  , c.jjgsid , \n" +
           "case when  enddate-startdate <=30 then '1个月以内' \t\n" +
           "\twhen  enddate-startdate >30 and   enddate-startdate <=90 then '1-3个月'\n" +
           " when  enddate-startdate >90 and   enddate-startdate <=180 then '3-6个月'\n" +
           "  when  enddate-startdate >180 and   enddate-startdate <=365 then '6-12个月'\n" +
           "   when  enddate-startdate >365 and   enddate-startdate <=1080 then '1-3年'\n" +
           "    when  enddate-startdate >1080 then '超过3年' end";
   public static final String FUNDCUSTNO_FEATURE_POSNUM = "with tmp as  (\n" +
           "select a.ttjj_fundcode,c.jjgsid,a.ttjj_custid  FUNDCUSTNO,c.shortname\n" +
           "      from   RETENTION.STTJJ_CUSTFUNDPOSIDETAILS_BASIC_ALL_SYN   a \n" +
           "      inner join  fund.CODEALL_ALL_BASIC_APP c on a.ttjj_fundcode=c.FCODE\n" +
           "        where a.ttjj_iscurposi=1  and a.ttjj_custid  not in \n" +
           "        (select C_CUSTOMERNO from emuser.TB_CUSTOMERDETAIL_BASIC_ALL_SYN\n" +
           "\t\twhere C_INDORINS in ('1','2') and C_ISDEL =0)\n" +
           "\t\tgroup by a.ttjj_fundcode,c.jjgsid,a.ttjj_custid,c.shortname\n" +
           "),\n" +
           "tmpcon as (\n" +
           "select  jjgsid,FUNDCUSTNO ,count(1) POSIVOL_num from tmp group by jjgsid,FUNDCUSTNO)\n" +
           "select 'POSNUM' FEATURETYPE,a.TTJJ_FUNDCODE FUNDCODE,a.shortname SHORTNAME, a.JJGSID ,\n" +
           " case when POSIVOL_num =1 then '1只'\n" +
           "            when POSIVOL_num =2  then '2只'\n" +
           "            when b.POSIVOL_num =3  then '3只'\n" +
           "            when POSIVOL_num >=4 and POSIVOL_num <6   then '4-5只'\n" +
           "            when POSIVOL_num >=6 and POSIVOL_num <11   then '6-10只'\n" +
           "            when POSIVOL_num >=11  then  '10只以上'    end  FEATURE ,count(1) NUM\n" +
           "            from tmp a inner join tmpcon  b on a.jjgsid=b.jjgsid and a.FUNDCUSTNO=b.FUNDCUSTNO\n" +
           "  group by a.TTJJ_FUNDCODE,a.shortname, a.JJGSID ,\n" +
           " case when POSIVOL_num =1 then '1只'\n" +
           "            when POSIVOL_num =2  then '2只'\n" +
           "            when b.POSIVOL_num =3  then '3只'\n" +
           "            when POSIVOL_num >=4 and POSIVOL_num <6   then '4-5只'\n" +
           "            when POSIVOL_num >=6 and POSIVOL_num <11   then '6-10只'\n" +
           "            when POSIVOL_num >=11  then  '10只以上'    end";
   private static final String COMCUSTNO_FEATURE_AGE = "select  'AGE' FEATURETYPE, a.jjgsid JJGSID, \n" +
           "case when   round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365)  <20 then  '20岁以下'\n" +
           "         when  round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365) >=20 and round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365) <30 then '20到30岁'\n" +
           "          when round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365) >=30 and round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365) <40 then '30到40岁'\n" +
           "         when  round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365)>=40 and round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365) <50 then '40到50岁'\n" +
           "         when  round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365)  >=50    then '50岁以上'  else '未知'\n" +
           "         end  FEATURE,count(1) NUM        from (         \n" +
           "select distinct  b.ttjj_custid, a.CARDBIRTHDAY ,c.jjgsid from       \n" +
           "  EMUSER.STTJJ_AGG_NORMALINFO a inner join  RETENTION.STTJJ_CUSTFUNDPOSIDETAILS_BASIC_ALL_SYN  b\n" +
           "  on a.FUNDCUSTNO=b.ttjj_custid \n" +
           "  inner join   fund.CODEALL_ALL_BASIC_APP c on b.TTJJ_FUNDCODE=c.fcode\n" +
           "   where b.ttjj_iscurposi=1   and  b.ttjj_custid  not in (select C_CUSTOMERNO from emuser.TB_CUSTOMERDETAIL_BASIC_ALL_SYN\n" +
           "where C_INDORINS in ('1','2') and C_ISDEL =0)\n" +
           "  ) a\n" +
           "   group by  case when   round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365)  <20 then  '20岁以下'\n" +
           "         when  round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365) >=20 and round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365) <30 then '20到30岁'\n" +
           "          when round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365) >=30 and round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365) <40 then '30到40岁'\n" +
           "         when  round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365)>=40 and round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365) <50 then '40到50岁'\n" +
           "         when  round(DATEDIFF(d,a.CARDBIRTHDAY,trunc(sysdate))/365)  >=50    then '50岁以上'  else '未知'\n" +
           "         end ,a.jjgsid";
   public static final String COMCUSTNO_FEATURE_PROVINCE = "select   'PROVINCE' FEATURETYPE, c.jjgsid JJGSID, \n" +
           "a.telPROVINCE  FEATURE,count(1)  NUM from\n" +
           "   EMUSER.STTJJ_AGG_NORMALINFO a inner join  RETENTION.STTJJ_CUSTFUNDPOSIDETAILS_BASIC_ALL_SYN   b\n" +
           "  on a.FUNDCUSTNO=b.ttjj_custid \n" +
           "  inner join   fund.CODEALL_ALL_BASIC_APP  c on b.TTJJ_FUNDCODE=c.fcode\n" +
           "   where b.ttjj_iscurposi=1 \n" +
           "   group by  a.telPROVINCE, c.jjgsid";
   public static final String COMCUSTNO_FEATURE_POSIVOL = "select 'POSIVOL' FEATURETYPE, jjgsid JJGSID,\n" +
           "   case when TTJJ_POSI_AMT <10 then '10以下'\n" +
           "                   when TTJJ_POSI_AMT >=10 and TTJJ_POSI_AMT <100   then '10-100'\n" +
           "            when TTJJ_POSI_AMT >=100 and TTJJ_POSI_AMT <1000   then '100-1000'\n" +
           "            when TTJJ_POSI_AMT >=1000 and TTJJ_POSI_AMT <5000   then '1000-5000'\n" +
           "            when TTJJ_POSI_AMT >=5000 and TTJJ_POSI_AMT <10000   then '5000-10000'\n" +
           "            when TTJJ_POSI_AMT >=10000 and TTJJ_POSI_AMT <100000   then '10000-100000'\n" +
           "             when TTJJ_POSI_AMT >=100000    then '100000以上'    end FEATURE ,count(1) NUM\n" +
           "            from  (\n" +
           "     select b.ttjj_custid ,c.jjgsid ,sum(TTJJ_POSI_AMT) TTJJ_POSI_AMT\n" +
           "             from    RETENTION.STTJJ_CUSTFUNDPOSIDETAILS_BASIC_ALL_SYN   b\n" +
           "             inner join  fund.CODEALL_ALL_BASIC_APP  c on b.ttjj_fundcode=c.fcode\n" +
           "       where b.ttjj_iscurposi=1 and  b.ttjj_custid  not in (select C_CUSTOMERNO from emuser.TB_CUSTOMERDETAIL_BASIC_ALL_SYN\n" +
           "where C_INDORINS in ('1','2') and C_ISDEL =0)\n" +
           "          group by  b.ttjj_custid ,c.jjgsid ) a\n" +
           "         group by  jjgsid,case when TTJJ_POSI_AMT <10 then '10以下'\n" +
           "                   when TTJJ_POSI_AMT >=10 and TTJJ_POSI_AMT <100   then '10-100'\n" +
           "            when TTJJ_POSI_AMT >=100 and TTJJ_POSI_AMT <1000   then '100-1000'\n" +
           "            when TTJJ_POSI_AMT >=1000 and TTJJ_POSI_AMT <5000   then '1000-5000'\n" +
           "            when TTJJ_POSI_AMT >=5000 and TTJJ_POSI_AMT <10000   then '5000-10000'\n" +
           "            when TTJJ_POSI_AMT >=10000 and TTJJ_POSI_AMT <100000   then '10000-100000'\n" +
           "             when TTJJ_POSI_AMT >=100000    then '100000以上'    end ";
   public static final String COMCUSTNO_FEATURE_POSNUM = "select 'POSNUM' FEATURETYPE, JJGSID ,case when POSIVOL_num =1 then '1只'\n" +
           "            when POSIVOL_num =2  then '2只'\n" +
           "            when POSIVOL_num =3  then '3只'\n" +
           "            when POSIVOL_num >=4 and POSIVOL_num <6   then '4-5只'\n" +
           "            when POSIVOL_num >=6 and POSIVOL_num <11   then '6-10只'\n" +
           "            when POSIVOL_num >=11  then  '10只以上'    end   FEATURE ,count(1) NUM from (\n" +
           "  select c.jjgsid,a.ttjj_custid  FUNDCUSTNO,count(1) POSIVOL_num\n" +
           "      from   RETENTION.STTJJ_CUSTFUNDPOSIDETAILS_BASIC_ALL_SYN   a \n" +
           "      inner join  fund.CODEALL_ALL_BASIC_APP c on a.ttjj_fundcode=c.FCODE\n" +
           "        where a.ttjj_iscurposi=1  and a.ttjj_custid  not in (select C_CUSTOMERNO from emuser.TB_CUSTOMERDETAIL_BASIC_ALL_SYN\n" +
           "where C_INDORINS in ('1','2') and C_ISDEL =0)  group by  c.jjgsid,a.ttjj_custid )a\n" +
           "         group by jjgsid ,case when POSIVOL_num =1 then '1只'\n" +
           "            when POSIVOL_num =2  then '2只'\n" +
           "            when POSIVOL_num =3  then '3只'\n" +
           "            when POSIVOL_num >=4 and POSIVOL_num <6   then '4-5只'\n" +
           "            when POSIVOL_num >=6 and POSIVOL_num <11   then '6-10只'\n" +
           "            when POSIVOL_num >=11  then  '10只以上'    end";

   //   CFHTradeDB.COMCUSTNO_FEATURE表
   public static final String COMCUSTNO_FEATURE_AGE_FILED = "COMCUSTNO_FEATURE_AGE";
   public static final String COMCUSTNO_FEATURE_PROVINCE_FILED = "COMCUSTNO_FEATURE_PROVINCE";
   public static final String COMCUSTNO_FEATURE_POSIVOL_FILED = "COMCUSTNO_FEATURE_POSIVOL";
   public static final String COMCUSTNO_FEATURE_POSNUM_FILED = "COMCUSTNO_FEATURE_POSNUM";
   private static final Map<String,String> COMCUSTNO_FEATURE_MAP = new HashMap<>();
   static {
      COMCUSTNO_FEATURE_MAP.put(COMCUSTNO_FEATURE_AGE_FILED, COMCUSTNO_FEATURE_AGE);
      COMCUSTNO_FEATURE_MAP.put(COMCUSTNO_FEATURE_PROVINCE_FILED, COMCUSTNO_FEATURE_PROVINCE);
      COMCUSTNO_FEATURE_MAP.put(COMCUSTNO_FEATURE_POSIVOL_FILED, COMCUSTNO_FEATURE_POSIVOL);
      COMCUSTNO_FEATURE_MAP.put(COMCUSTNO_FEATURE_POSNUM_FILED, COMCUSTNO_FEATURE_POSNUM);
   }
   public static Map<String, String> getComCustNoFeature() {
      return COMCUSTNO_FEATURE_MAP;
   }
   //CFHTradeDB.FUNDCUSTNO_FEATURE表
   public static final String FUNDCUSTNO_FEATURE_AGE_FILED = "AGE";
   public static final String FUNDCUSTNO_FEATURE_PROVINCE_FILED = "PROVINCE";
   public static final String FUNDCUSTNO_FEATURE_POSIVOL_FILED = "POSIVOL";
   public static final String FUNDCUSTNO_FEATURE_PRODIS_FILED = "PRODIS";
   public static final String FUNDCUSTNO_FEATURE_PRODISTIME_FILED = "PRODISTIME";
   public static final String FUNDCUSTNO_FEATURE_HOLDTIME_FILED = "HOLDTIME";

   private static final Map<String,String> FUNDCUSTNO_FEATURE_MAP = new HashMap<>();
   static {
      FUNDCUSTNO_FEATURE_MAP.put(FUNDCUSTNO_FEATURE_AGE_FILED, FUNDCUSTNO_FEATURE_AGE);
      FUNDCUSTNO_FEATURE_MAP.put(FUNDCUSTNO_FEATURE_PROVINCE_FILED, FUNDCUSTNO_FEATURE_PROVINCE);
      FUNDCUSTNO_FEATURE_MAP.put(FUNDCUSTNO_FEATURE_POSIVOL_FILED, FUNDCUSTNO_FEATURE_POSIVOL);
      FUNDCUSTNO_FEATURE_MAP.put(FUNDCUSTNO_FEATURE_PRODIS_FILED, FUNDCUSTNO_FEATURE_PRODIS);
      FUNDCUSTNO_FEATURE_MAP.put(FUNDCUSTNO_FEATURE_PRODISTIME_FILED, FUNDCUSTNO_FEATURE_PRODISTIME);
      FUNDCUSTNO_FEATURE_MAP.put(FUNDCUSTNO_FEATURE_HOLDTIME_FILED, FUNDCUSTNO_FEATURE_HOLDTIME);
   }
   public static Map<String, String> getFundCustNoFeature() {
      return FUNDCUSTNO_FEATURE_MAP;
   }

   public static final String INTERFACE_FUND_DAY_RUN = "SELECT count(1) as count  FROM    BACKSTAGE.KETTLE_UPDATE_RECORD" +
           " where upper(tablename) in ('USER_FCODE_FIN_STAT_ALL_APP', 'STTJJ_CUSTFUNDPOSIDETAILS_BASIC_ALL_SYN'," +
           " 'STTJJ_INTERFACEFUND_STAT_ALL_SYN','STTJJ_INTERFACEJJGS_STAT_ALL_SYN')" +
           "  and CalendarDate =trunc(sysdate)" +
           "  having count(1)=4  ";

   public static final String REFERENCE_FUND_DAY_RUN = "SELECT count(1) as count\n" +
           "FROM BACKSTAGE.KETTLE_UPDATE_RECORD\n" +
           "where upper(tablename) = upper('TTJJ_REFERENCEFUND_STAT_SYN_ALL')\n" +
           "  and CalendarDate = trunc(sysdate)";
   public static final String LIVE_TB_CFHSCORE = "SELECT PDATE\n" +
           "     , PTYPE\n" +
           "     , CFHID\n" +
           "     , CFHNAME\n" +
           "     , LIVENUM\n" +
           "     , CFHHEAT\n" +
           "     , CFHLIKE\n" +
           "     , CFHSCORE\n" +
           "     , UPDATETIME\n" +
           "FROM content.LIVE_TB_CFHSCORE_STAT_ALL_APP\n" +
           "WHERE ptype != 2;";

   public static final String INVESTMENT_HOLD_TOP = "select pdate                                                  ,\n" +
           "       PARTNER                                                         as partner,\n" +
           "       PARTNERNAME                                                     as partnerName,\n" +
           "       cast(nullif(nullif(sum(market_value), 0), 0) as decimal(20, 4)) as endAsset,\n" +
           "       count(distinct INVESTOR_ID)                                     as endUser\n" +
           "from (select b.PARTNERNAME, a.*, case when pdate = c.maxdate then 1 else 0 end ismonth\n" +
           "      from (select investor_id,\n" +
           "                   sign_no,\n" +
           "                   ic_strategy_id,\n" +
           "                   market_value,\n" +
           "                   profit_date pdate,\n" +
           "                   partner,\n" +
           "                   ic_strategy_name,\n" +
           "                   day_yield,\n" +
           "                   IC_ACCT_ID\n" +
           "            from TGZH.TB_IC_INVSTR_ASST_HIS_BASIC_ALL_SYN\n" +
           "            where market_value > 0\n" +
           "              and ASSET_TX_ACCT_ID not in\n" +
           "                  (select ASSET_TX_ACCT_ID from tgzh.TB_IC_SGN_RCRD_BASIC_ALL_SYN where INVESTOR_TYPE != 0)\n" +
           "              and IS_DEL = 0) a\n" +
           "               left join TGZH.TB_PARTNER_ENUMTYPE_PARA_ALL_SYN b on a.partner = b.partner\n" +
           "               left join (select trunc(PROFIT_DATE, 'mm') pmonth, max(profit_date) maxdate\n" +
           "                          from TGZH.TB_IC_INVSTR_ASST_HIS_BASIC_ALL_SYN\n" +
           "                          where market_value > 0\n" +
           "                            and IS_DEL = 0\n" +
           "                          group by trunc(PROFIT_DATE, 'mm')) c on trunc(a.pdate, 'mm') = c.pmonth) a\n" +
           "where pdate between '%s' and '%s' %s\n" +
           "group by pdate, PARTNERNAME, PARTNER;";
   public static final String INVESTMENT_HOLD_RANKING = "with t2 as (select TRIM(ic_strategy_id) as                                    ic_strategy_id,\n" +
           "                   investor_id,\n" +
           "                   ic_strategy_name,\n" +
           "                   partner,\n" +
           "                   sum(case when profit_date = enddate then market_value end) endasset\n" +
           "            from TGZH.TB_IC_INVSTR_ASST_HIS_BASIC_ALL_SYN a\n" +
           "                     left join (select min(profit_date) startdate, max(profit_date) enddate\n" +
           "                                from TGZH.TB_IC_INVSTR_ASST_HIS_BASIC_ALL_SYN\n" +
           "                                where profit_date between '%s' and '%s') b\n" +
           "                               on (profit_date = startdate or profit_date = enddate)\n" +
           "            where ASSET_TX_ACCT_ID not in\n" +
           "                  (select ASSET_TX_ACCT_ID from tgzh.TB_IC_SGN_RCRD_BASIC_ALL_SYN where INVESTOR_TYPE != 0)\n" +
           "            group by TRIM(ic_strategy_id), investor_id, ic_strategy_name, partner),\n" +
           "     t3 as (select TRIM(ic_strategy_id) as ic_strategy_id, ic_strategy_name, partner, sum(endasset) endasset\n" +
           "            from t2\n" +
           "            group by TRIM(ic_strategy_id), ic_strategy_name, partner)\n" +
           "select PARTNER                                  as partner,\n" +
           "       TRIM(b.ic_strategy_id)                   as strategyId,\n" +
           "       ic_strategy_name                         as strategyName,\n" +
           "       cast(endasset / 10000 as decimal(10, 2)) as posVolAmt,\n" +
           "       date('%s')                               as pdate\n" +
           "from t3 b";
   public static final String INVESTMENT_USER = "select FCODE as strategyId, IC_STRATEGY_NAME as strategyName, count(MUID) as optionNum, PARTNER as partner\n" +
           "from (with tmp1 as (select IC_STRATEGY_ID, IC_STRATEGY_NAME, PARTNER\n" +
           "                    from TGZH.TB_IC_STRTGY_INF_PARA_ALL_SYN\n" +
           "                    limit 1 over(partition by IC_STRATEGY_ID order by UPDATE_TIME desc )),\n" +
           "           tmp2 as (select MUID, FCODE, date(EITIME) pdate from FAVOR.TG_BASIC_ALL_SYN where status = '0')\n" +
           "      select a.MUID, a.FCODE, a.pdate, b.IC_STRATEGY_NAME, b.PARTNER\n" +
           "      from tmp2 a\n" +
           "               left join tmp1 b on a.FCODE = b.IC_STRATEGY_ID) t\n" +
           "group by FCODE, IC_STRATEGY_NAME, PARTNER\n" +
           "order by optionNum desc;";
   public static final String INVESTMENT_USER_SUBSCRIBE = "select IC_STRATEGY_NAME as strategyName, a.fcode as strategyId, a.hisSubNum, a.currSubNum, PARTNER as partner\n" +
           "from (select fcode,\n" +
           "             count(distinct deviceid)                               as hisSubNum,\n" +
           "             count(distinct case when isopen = 1 then deviceid end) as currSubNum\n" +
           "      from content.REMINDUSERSET_BASIC_SYN_ALL\n" +
           "      where remindtype = 'DEPARTBUYINVESTMENT'\n" +
           "      group by 1) a\n" +
           "         left join tgzh.TB_IC_STRTGY_INF_PARA_ALL_SYN b on a.fcode = b.IC_STRATEGY_ID;";
   public static final String INVESTMENT_USER_RETENTION = "select TRIM(a.ic_strategy_id) strategyId,\n" +
           "       a.ic_strategy_name     strategyName,\n" +
           "       sum(a.retention_rate)  retentionRate,\n" +
           "       b.PARTNER as           partner\n" +
           "from tgzh.IC_STRATEGY_FEATURED_STAT_ALL_APP a\n" +
           "         left join tgzh.TB_IC_STRTGY_INF_PARA_ALL_SYN b on a.ic_strategy_id = TRIM(b.IC_STRATEGY_ID)\n" +
           "where a.ic_strategy_name is not null\n" +
           "group by a.ic_strategy_name, TRIM(a.ic_strategy_id), b.PARTNER;";

   public static final String CFH_TRADE_STAT = "select a.calc_date         CALCDAY,\n" +
           "       a.ttjj_fundcode     FUNDCODE,\n" +
           "       a.TTJJ_PER_POSI_AMT POSIAMT,\n" +
           "       b.CFMSAMOUNTSG,\n" +
           "       b.CFMSAMOUNTSH,\n" +
           "       b.CFMSAMOUNTDT\n" +
           "From retention.FUNDPOSI_DAY_STAT_SYN_ALL a\n" +
           "         left join (select fundcode,\n" +
           "                           transactioncfmdate,\n" +
           "                           sum(case when BUSINTYPE in ('120', '122') then cfmsamount else 0 end) cfmsamountsg,\n" +
           "                           sum(case when BUSINTYPE in ('124') then cfmsamount else 0 end)        cfmsamountsh,\n" +
           "                           sum(case when BUSINTYPE in ('139') then cfmsamount else 0 end)        cfmsamountdt\n" +
           "                    from busin.TTJJ_REFERENCEFUND_STAT_SYN_ALL\n" +
           "                    where orgtypename = '个人'\n" +
           "                      and BUSINTYPE in ('120', '122', '139', '124')\n" +
           "                      and transactioncfmdate = trunc(sysdate) - 1\n" +
           "                      and iszntg = 0\n" +
           "                    group by transactioncfmdate, fundcode) b\n" +
           "                   on a.calc_date = b.transactioncfmdate and a.TTJJ_FUNDCODE = b.fundcode\n" +
           "where a.calc_date = trunc(sysdate) - 1\n" +
           "order by a.TTJJ_FUNDCODE, a.calc_date;";

   public static final String CFH_TRADE_RANK_STAT = "WITH TMP AS (select a.fcode FUNDCODE, JJGSID, b.CLASS1_VAL fundtype\n" +
           "             from fund.CODEALL_ALL_BASIC_APP a\n" +
           "                      inner join fund.RSBTYPE_ENUMRELATION_BASIC_SYN_ALL b on a.rsbtype = b.CLASS2_CODE\n" +
           "             where jjgsid is not null\n" +
           "               and ptypename <> '高端理财'\n" +
           "             union\n" +
           "             select a.fcode FUNDCODE, JJGSID, '全部' fundtype\n" +
           "             from fund.CODEALL_ALL_BASIC_APP a\n" +
           "             where jjgsid is not null\n" +
           "               and ptypename <> '高端理财'\n" +
           "             union\n" +
           "             select a.fcode FUNDCODE, JJGSID, '非货' fundtype\n" +
           "             from fund.CODEALL_ALL_BASIC_APP a\n" +
           "                      inner join fund.RSBTYPE_ENUMRELATION_BASIC_SYN_ALL b on a.rsbtype = b.CLASS2_CODE\n" +
           "             where jjgsid is not null\n" +
           "               and ptypename <> '高端理财'\n" +
           "               and b.CLASS1_VAL <> '货币型'),\n" +
           "     tmp1 as (select max(datetime) date1\n" +
           "              From fund.TRADEDATE_BASIC_ALL_SYN\n" +
           "              where istdate = 1\n" +
           "                AND trunc(datetime) <= trunc(sysdate) - 1),\n" +
           "     tmp2 as (select max(datetime) date2\n" +
           "              From fund.TRADEDATE_BASIC_ALL_SYN a\n" +
           "                       inner join tmp1 b on datetime < b.date1\n" +
           "              where istdate = 1\n" +
           "                AND TO_CHAR(datetime, 'yyyymm') = TO_CHAR(ADD_MONTHS(TRUNC(b.date1, 'MM'), -1), 'yyyymm')),\n" +
           "     tmp3 as (select to_char(a.calc_date, 'yyyymm') calc_date, TTJJ_FUNDCODE, TTJJ_PER_POSI_AMT, TTJJ_PER_POSI_CUSTNUM\n" +
           "              from RETENTION.FUNDPOSI_DAY_STAT_SYN_ALL a\n" +
           "                       inner join tmp1 b on a.calc_date = date1\n" +
           "              where TTJJ_PER_POSI_AMT > 0),\n" +
           "     tmp4 as (select TTJJ_FUNDCODE, TTJJ_PER_POSI_AMT, TTJJ_PER_POSI_CUSTNUM\n" +
           "              from RETENTION.FUNDPOSI_DAY_STAT_SYN_ALL a\n" +
           "                       inner join tmp2 b on a.calc_date = date2\n" +
           "              where TTJJ_PER_POSI_AMT > 0)\n" +
           "select a.JJGSID,\n" +
           "       a.FUNDTYPE,\n" +
           "       b.calc_date,\n" +
           "       sum(b.TTJJ_PER_POSI_AMT)                                                                                                            POSI_AMT,\n" +
           "       rank()\n" +
           "       over (partition by a.fundtype,b.calc_date order by sum(b.TTJJ_PER_POSI_AMT) desc nulls last)                                        POSI_AMT_RANK,\n" +
           "       sum(b.TTJJ_PER_POSI_CUSTNUM)                                                                                                        POSI_NUM,\n" +
           "       rank()\n" +
           "       over (partition by a.fundtype,b.calc_date order by sum(b.TTJJ_PER_POSI_CUSTNUM) desc nulls last)                                    POSI_NUM_RANK,\n" +
           "       round(sum(b.TTJJ_PER_POSI_AMT) / sum(b.TTJJ_PER_POSI_CUSTNUM), 2)                                                                   POSI_PER,\n" +
           "       rank()\n" +
           "       over (partition by a.fundtype,b.calc_date order by sum(b.TTJJ_PER_POSI_AMT) / sum(b.TTJJ_PER_POSI_CUSTNUM) desc nulls last)         POSI_PER_RANK,\n" +
           "       round(100 * (sum(b.TTJJ_PER_POSI_AMT) / sum(c.TTJJ_PER_POSI_AMT) - 1),\n" +
           "             2)                                                                                                                            POSI_AMT_RATE,\n" +
           "       rank()\n" +
           "       over (partition by a.fundtype,b.calc_date order by 100 * (sum(b.TTJJ_PER_POSI_AMT) / sum(c.TTJJ_PER_POSI_AMT) - 1) desc nulls last) POSI_AMT_RATE_RANK\n" +
           "from tmp a\n" +
           "         inner join tmp3 b on a.FUNDCODE = b.TTJJ_FUNDCODE\n" +
           "         left join tmp4 c on a.FUNDCODE = c.TTJJ_FUNDCODE\n" +
           "group by a.JJGSID, a.fundtype, b.calc_date";

}
