package ttfund.web.fortuneservice.constant;

/**
 * ResearchTaskCyclesEnum.java
 *
 * <AUTHOR>
 * @date 2023/4/27 9:31
 */
public enum ResearchTaskCyclesEnum {

    /**
     * 给机构下发的任务类型
     **/
    ONCE(0, "一次性任务"),
    WEEK(1, "每周任务"),
    MONTH(2, "每月任务"),
    QUARTER(3, "每季度任务");

    private final int code;

    private final String note;

    ResearchTaskCyclesEnum(int code, String note) {
        this.code = code;
        this.note = note;
    }

    public int getCode() {
        return code;
    }

    public String getNote() {
        return note;
    }
}
