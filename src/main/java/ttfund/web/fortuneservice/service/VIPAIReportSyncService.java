package ttfund.web.fortuneservice.service;

import java.util.Date;

/**
 * VIP AI报告数据同步服务接口
 */
public interface VIPAIReportSyncService {

    /**
     * 同步VIP AI报告数据到MongoDB
     *
     * @param breakpointTime 断点时间，用于增量同步
     * @return 同步是否成功
     */
    boolean syncVIPAIReportToMongoDB(Date breakpointTime);

    /**
     * 获取最新的更新时间作为下次同步的断点
     *
     * @return 最新更新时间
     */
    Date getLatestUpdateTime();
}
