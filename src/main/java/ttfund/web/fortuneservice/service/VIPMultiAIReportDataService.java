package ttfund.web.fortuneservice.service;

import ttfund.web.fortuneservice.model.dto.MultiMonthlyReportRequestDTO;
import ttfund.web.fortuneservice.model.dto.MultiMonthlyReportResponseDTO;

/**
 * VIP多机构AI报告数据服务接口
 */
public interface VIPMultiAIReportDataService {
    
    /**
     * 保存多机构报告到数据库
     *
     * @param request 请求数据
     * @param response 响应数据
     * @param taskConfigId 任务配置ID
     * @param taskCycleMark 任务周期标记
     */
    void saveMultiReportToDatabase(MultiMonthlyReportRequestDTO request, MultiMonthlyReportResponseDTO response, String taskConfigId, String taskCycleMark);
}
