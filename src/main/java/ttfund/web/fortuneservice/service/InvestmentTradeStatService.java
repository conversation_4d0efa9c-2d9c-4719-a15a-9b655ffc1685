package ttfund.web.fortuneservice.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.constant.CFHMongodbConstant;
import ttfund.web.fortuneservice.constant.HqMongodbConstant;
import ttfund.web.fortuneservice.constant.VerticaSqlConstant;
import ttfund.web.fortuneservice.dao.*;
import ttfund.web.fortuneservice.manager.TGApiManager;
import ttfund.web.fortuneservice.model.dto.*;
import ttfund.web.fortuneservice.utils.CommonUtil;
import ttfund.web.fortuneservice.utils.TimeUtil;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class InvestmentTradeStatService {


    @Autowired
    private VerticaDao verticaDao;

    @Autowired
    private TGApiManager tgApiManager;

    @Autowired
    private CfhMongodbDao cfhMongodbDao;

    @Autowired
    private CfhSqlserverDao cfhSqlserverDao;

    @Autowired
    private TgMongodbDao tgMongodbDao;

    public void businessdInvestmentTradeHoldStat(String dateStr) {
        Date endTime = TimeUtil.getTodayEndAsDate(DateUtils.addDays(TimeUtil.getNowDate(), -1));

        //获取开始时间
        Date startTime = getStartTime(dateStr, endTime);

        //投顾机构号，转换用
        Map<String, String> cfhIdMap = getPartnerJjgsIdMap();
        while (startTime.before(endTime)) {
            //保有数据顶部按天统计 保有数据按月统计
            statHoldTop(startTime, cfhIdMap);
            startTime = DateUtils.addDays(startTime, 1);
        }
    }

    public void businessdInvestmentTradeHoldRankStat(String dateStr) {
        Date endTime = TimeUtil.getTodayEndAsDate(DateUtils.addDays(TimeUtil.getNowDate(), -1));

        //获取开始时间
        Date startTime = getStartTime(dateStr, endTime);

        //投顾机构号，转换用
        Map<String, String> cfhIdMap = getPartnerJjgsIdMap();
        while (startTime.before(endTime)) {
            //保有数据按天排行榜
            statHoldRanking(startTime, cfhIdMap);
            startTime = DateUtils.addDays(startTime, 1);
        }
    }

    private Map<String, String> getPartnerJjgsIdMap() {
        Map<String, String> cfhIdMap = new HashMap<>();
        List<InvestSpecialDTO> alFundListByCfhId = tgApiManager.getAlFundListByCfhId(null);
        if (!CollectionUtils.isEmpty(alFundListByCfhId)) {
            cfhIdMap = alFundListByCfhId.stream().collect(Collectors.toMap(InvestSpecialDTO::getPARTNER, InvestSpecialDTO::getACCOUNTID, (v1, v2) -> v1));
        }
        //查财富号信息
        List<CFHInfoDto> cfhList = cfhSqlserverDao.selectAllCFH();
        Map<String, String> cfhMap = cfhList.stream()
                .filter(o -> StringUtils.isNotEmpty(o.getCFHID()) && StringUtils.isNotEmpty(o.getCommpanyCode()))
                .collect(Collectors.toMap(CFHInfoDto::getCFHID, CFHInfoDto::getCommpanyCode));

        //公司真实ID转换
        List<CFHWhiteSheetDTO> cfhWhiteSheetDTOS = cfhSqlserverDao.selectAllCFHRealCompany();
        Map<String, String> cfhWhiteSheetMap = cfhWhiteSheetDTOS.stream().collect(Collectors.toMap(CFHWhiteSheetDTO::getCFHID, CFHWhiteSheetDTO::getRealCompanyCode));

        for (Map.Entry<String, String> entry : cfhIdMap.entrySet()) {
            String cfhId = entry.getValue();
            if (StringUtils.isEmpty(cfhId)) {
                continue;
            }
            entry.setValue(cfhWhiteSheetMap.getOrDefault(cfhId, cfhMap.get(cfhId)));
        }
        return cfhIdMap;
    }

    private Map<String, String> getCfhJjgsIdMap() {
        //查财富号信息
        List<CFHInfoDto> cfhList = cfhSqlserverDao.selectAllCFH();
        Map<String, String> cfhMap = cfhList.stream()
                .filter(o -> StringUtils.isNotEmpty(o.getCFHID()) && StringUtils.isNotEmpty(o.getCommpanyCode()))
                .collect(Collectors.toMap(CFHInfoDto::getCFHID, CFHInfoDto::getCommpanyCode));

        //公司真实ID转换
        List<CFHWhiteSheetDTO> cfhWhiteSheetDTOS = cfhSqlserverDao.selectAllCFHRealCompany();
        Map<String, String> cfhWhiteSheetMap = cfhWhiteSheetDTOS.stream().collect(Collectors.toMap(CFHWhiteSheetDTO::getCFHID, CFHWhiteSheetDTO::getRealCompanyCode));

        for (Map.Entry<String, String> entry : cfhMap.entrySet()) {
            String cfhId = entry.getKey();
            entry.setValue(cfhWhiteSheetMap.getOrDefault(cfhId, cfhMap.get(cfhId)));
        }
        return cfhMap;
    }

    private void statHoldRanking(Date startTime, Map<String, String> cfhIdMap) {
        try {
            String sql = String.format(VerticaSqlConstant.INVESTMENT_HOLD_RANKING
                    , TimeUtil.dateToStr(startTime, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS)
                    , TimeUtil.dateToStr(TimeUtil.getTodayEndAsDate(startTime), TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS)
                    , TimeUtil.dateToStr(startTime, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS)
            );
            doStatHold(startTime, cfhIdMap, sql, null, CFHMongodbConstant.TB_INVESTMENT_HOLD_RANK);
        } catch (Exception e) {
            log.warn("统计数据异常", e.getMessage(), e);
        }
    }

    private void statHoldTop(Date startTime, Map<String, String> cfhIdMap) {
        try {
            String sql = String.format(VerticaSqlConstant.INVESTMENT_HOLD_TOP
                    , TimeUtil.dateToStr(startTime, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS)
                    , TimeUtil.dateToStr(TimeUtil.getTodayEndAsDate(startTime), TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS)
                    , ""
            );
            doStatHold(startTime, cfhIdMap, sql, 0, CFHMongodbConstant.TB_INVESTMENT_HOLD);
            //每月第1天处理上月数据
            if (isFirstDayOfMonth(startTime)) {
                sql = String.format(VerticaSqlConstant.INVESTMENT_HOLD_TOP
                        , TimeUtil.dateToStr(DateUtils.addMonths(startTime, -1), TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS)
                        , TimeUtil.dateToStr(startTime, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS)
                        , " and ismonth = 1"
                );
                doStatHold(startTime, cfhIdMap, sql, 1, CFHMongodbConstant.TB_INVESTMENT_HOLD);
            }
        } catch (Exception e) {
            log.warn("统计数据异常", e.getMessage(), e);
        }
    }

    private boolean doStatHold(Date startTime, Map<String, String> cfhIdMap, String sql, Integer isMonth, String collectionName) {
        List<InvestmentHoldStatDTO> list = verticaDao.selectSql(sql, InvestmentHoldStatDTO.class);
        log.info("表：{}，日期：{}，数据量：{}", collectionName, TimeUtil.dateToStr(startTime, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS), list != null ? list.size() : 0);
        if (CollectionUtils.isEmpty(list)) {
            log.warn("没有数据，请注意");
            return true;
        }
        list.forEach(item -> {
            item.setIsMonth(isMonth);
            item.setJjgsId(cfhIdMap.get(item.getPartner()));
            String id = (StringUtils.isNotEmpty(item.getPartner()) ? item.getPartner() : "")
                    + (item.getIsMonth() != null ? item.getIsMonth().toString() : "")
                    + (StringUtils.isNotEmpty(item.getStrategyName()) ? item.getStrategyName() : "")
                    + TimeUtil.dateToStr(item.getPdate(), TimeUtil.FORMAT_YYYYMMDD);
            item.set_id(id);
        });
        //删除2年前数据
        int rowDelete = cfhMongodbDao.deleteByTime(CFHMongodbConstant.DB_CFH_TRADE, collectionName, "pdate", -2 * 365);
        int rowInsert = cfhMongodbDao.insertOrUpdate(list, CFHMongodbConstant.DB_CFH_TRADE, collectionName);
        log.info("表：{}，删除{}条，插入{}条", collectionName, rowDelete, rowInsert);
        return false;
    }

    private static Date getStartTime(String dateStr, Date endTime) {

        Date startTime = TimeUtil.getTodayStartAsDate(endTime);
        if (StringUtils.isNotEmpty(dateStr)) {
            try {
                startTime = TimeUtil.stringToDate2(dateStr, TimeUtil.FORMAT_YYYYMMDD);
            } catch (Exception e) {
                log.info("时间格式错误,参数：{}，要求格式：{}", dateStr, TimeUtil.FORMAT_YYYYMMDD);
            }
        }
        return startTime;
    }

    public static boolean isFirstDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        int lastDayOfMonth = calendar.getActualMinimum(Calendar.DAY_OF_MONTH);

        int currentDay = calendar.get(Calendar.DAY_OF_MONTH);

        return currentDay == lastDayOfMonth;
    }

    public void businessdInvestmentTradeUserStat() {

        //查用户加自选
        List<InvestmentUserStatDTO> userOptionList = verticaDao.selectSql(VerticaSqlConstant.INVESTMENT_USER, InvestmentUserStatDTO.class);
        Map<String, InvestmentUserStatDTO> optionMap = userOptionList.stream().filter(e -> StringUtils.isNotEmpty(e.getStrategyId())).collect(Collectors.toMap(InvestmentUserStatDTO::getStrategyId, Function.identity(), (t1, t2) -> t1));
        //用户订阅
        List<InvestmentUserStatDTO> userSubscribeList = verticaDao.selectSql(VerticaSqlConstant.INVESTMENT_USER_SUBSCRIBE, InvestmentUserStatDTO.class);
        Map<String, InvestmentUserStatDTO> subscribeMap = userSubscribeList.stream().filter(e -> StringUtils.isNotEmpty(e.getStrategyId())).collect(Collectors.toMap(InvestmentUserStatDTO::getStrategyId, Function.identity(), (t1, t2) -> t1));
        //近1年留存率
        List<InvestmentUserStatDTO> userRetentionList = verticaDao.selectSql(VerticaSqlConstant.INVESTMENT_USER_RETENTION, InvestmentUserStatDTO.class);
        Map<String, InvestmentUserStatDTO> retentionMap = userRetentionList.stream().filter(e -> StringUtils.isNotEmpty(e.getStrategyId())).collect(Collectors.toMap(InvestmentUserStatDTO::getStrategyId, Function.identity(), (t1, t2) -> t1));
        //目标盈年化收益率、目标盈目标收益
        List<InvestmentUserStatDTO> userTargetProfitList = tgMongodbDao.selectCollection(HqMongodbConstant.DB_TGSTGINFO, HqMongodbConstant.TABLE_TGSTGJBXX, InvestmentUserStatDTO.class);
        Map<String, InvestmentUserStatDTO> profitMap = userTargetProfitList.stream().filter(e -> StringUtils.isNotEmpty(e.getTGCODE())).collect(Collectors.toMap(InvestmentUserStatDTO::getTGCODE, Function.identity(), (t1, t2) -> t1));
        //策略复购率、用户持仓正收益占比、用户平均持有时长
        List<InvestmentUserStatDTO> userFgratioList = tgMongodbDao.selectCollection(HqMongodbConstant.DB_TGSTGINFO, HqMongodbConstant.TABLE_TGSTG_INDICTPRD, InvestmentUserStatDTO.class);
        Map<String, InvestmentUserStatDTO> userDataMap = userFgratioList.stream().collect(Collectors.toMap(InvestmentUserStatDTO::getTGCODE, Function.identity(), (t1, t2) -> t1));
        userOptionList.forEach(item->item.set_id(item.getStrategyId()));
        userSubscribeList.forEach(item->item.set_id(item.getStrategyId()));
        userRetentionList.forEach(item->item.set_id(item.getStrategyId()));
        userTargetProfitList.forEach(item->item.set_id(item.getTGCODE()));
        userFgratioList.forEach(item->item.set_id(item.getTGCODE()));
        cfhMongodbDao.insertOrUpdate(userOptionList, CFHMongodbConstant.DB_CFH_TRADE, CFHMongodbConstant.TB_INVESTMENT_USER_DATA_OPTION);
        cfhMongodbDao.insertOrUpdate(userSubscribeList, CFHMongodbConstant.DB_CFH_TRADE, CFHMongodbConstant.TB_INVESTMENT_USER_DATA_SUBSCRIBE);
        cfhMongodbDao.insertOrUpdate(userRetentionList, CFHMongodbConstant.DB_CFH_TRADE, CFHMongodbConstant.TB_INVESTMENT_USER_DATA_RETENTION);
        cfhMongodbDao.insertOrUpdate(userTargetProfitList, CFHMongodbConstant.DB_CFH_TRADE, CFHMongodbConstant.TB_INVESTMENT_USER_DATA_TARGETPROFIT);
        cfhMongodbDao.insertOrUpdate(userFgratioList, CFHMongodbConstant.DB_CFH_TRADE, CFHMongodbConstant.TB_INVESTMENT_USER_DATA_FGRATIO);
        //策略ID
        Set<String> strategyIdSet = new HashSet<>(optionMap.keySet());
        strategyIdSet.addAll(subscribeMap.keySet());
        strategyIdSet.addAll(retentionMap.keySet());
        strategyIdSet.addAll(profitMap.keySet());
        strategyIdSet.addAll(userDataMap.keySet());
        if (CollectionUtils.isEmpty(strategyIdSet)) {
            log.info("没有数据，请注意");
        }

        //投顾机构号，转换用
        Map<String, String> partnerJjgsIdMap = getPartnerJjgsIdMap();
        Map<String, String> cfhJjgsIdMap = getCfhJjgsIdMap();
        List<InvestmentUserStatDTO> userData = new ArrayList<>();
        for (String strategyId : strategyIdSet) {
            InvestmentUserStatDTO tgOptionDTO = optionMap.get(strategyId);
            InvestmentUserStatDTO tgSubscribeDTO = subscribeMap.get(strategyId);
            InvestmentUserStatDTO tgRetentionDTO = retentionMap.get(strategyId);
            InvestmentUserStatDTO tgTargetProfitDTO = profitMap.get(strategyId);
            InvestmentUserStatDTO tgUserDataDTO = userDataMap.get(strategyId);

            InvestmentUserStatDTO tgData = new InvestmentUserStatDTO();
            tgData.setStrategyId(strategyId);
            tgData.setUpdateTIme(TimeUtil.getNowDate());
            Optional.ofNullable(tgOptionDTO).ifPresent(value -> {
                tgData.setJjgsId(tgData.getJjgsId() == null ? partnerJjgsIdMap.get(value.getPartner()) : tgData.getJjgsId());
                tgData.setPartner(tgData.getPartner() == null ? value.getPartner() : tgData.getPartner());
                tgData.setOptionNum(value.getOptionNum());
                tgData.setStrategyName(tgData.getStrategyName() == null ? value.getStrategyName() : tgData.getStrategyName());
            });
            Optional.ofNullable(tgSubscribeDTO).ifPresent(value -> {
                tgData.setJjgsId(tgData.getJjgsId() == null ? partnerJjgsIdMap.get(value.getPartner()) : tgData.getJjgsId());
                tgData.setPartner(tgData.getPartner() == null ? value.getPartner() : tgData.getPartner());
                tgData.setCurrSubNum(value.getCurrSubNum());
                tgData.setHisSubNum(value.getHisSubNum());
                tgData.setStrategyName(tgData.getStrategyName() == null ? value.getStrategyName() : tgData.getStrategyName());
            });
            Optional.ofNullable(tgRetentionDTO).ifPresent(value -> {
                tgData.setJjgsId(tgData.getJjgsId() == null ? partnerJjgsIdMap.get(value.getPartner()) : tgData.getJjgsId());
                tgData.setPartner(tgData.getPartner() == null ? value.getPartner() : tgData.getPartner());
                tgData.setRetentionRate(value.getRetentionRate());
                tgData.setStrategyName(tgData.getStrategyName() == null ? value.getStrategyName() : tgData.getStrategyName());
            });
            Optional.ofNullable(tgTargetProfitDTO).ifPresent(value -> {
                tgData.setACCOUNTID(value.getACCOUNTID());
                tgData.setJjgsId(StringUtils.isNotEmpty(value.getACCOUNTID()) && StringUtils.isEmpty(tgData.getJjgsId()) ? cfhJjgsIdMap.get(value.getACCOUNTID()) : tgData.getJjgsId());
                tgData.setTARGETPROFIT(value.getTARGETPROFIT());
                tgData.setTARGETANNYIELD(value.getTARGETANNYIELD());
                tgData.setStrategyName(tgData.getStrategyName() == null ? value.getTGNAME() : tgData.getStrategyName());
            });
            Optional.ofNullable(tgUserDataDTO).ifPresent(value -> {
                tgData.setACCOUNTID(tgData.getACCOUNTID() == null ? value.getACCOUNTID() : tgData.getACCOUNTID());
                tgData.setJjgsId(StringUtils.isNotEmpty(value.getACCOUNTID()) && StringUtils.isEmpty(tgData.getJjgsId()) ? cfhJjgsIdMap.get(value.getACCOUNTID()) : tgData.getJjgsId());
                tgData.setUSER_FGRATIO(value.getUSER_FGRATIO());
                tgData.setUSER_POSITIVE_RATIO(value.getUSER_POSITIVE_RATIO());
                tgData.setUSER_AVGDAY(value.getUSER_AVGDAY());
                tgData.setStrategyName(tgData.getStrategyName() == null ? value.getTGNAME() : tgData.getStrategyName());
            });
            userData.add(tgData);
        }
        userData.forEach(item -> item.set_id(
                (StringUtils.isNotEmpty(item.getStrategyId()) ? item.getStrategyId() : "")
                        + (StringUtils.isNotEmpty(item.getStrategyName()) ? item.getStrategyName() : "")
                        + (StringUtils.isNotEmpty(item.getJjgsId()) ? item.getJjgsId() : "")
        ));
        int rowInsert = cfhMongodbDao.insertOrUpdate(userData, CFHMongodbConstant.DB_CFH_TRADE, CFHMongodbConstant.TB_INVESTMENT_USER_DATA);
    }
}
