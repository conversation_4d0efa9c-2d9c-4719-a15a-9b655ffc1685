package ttfund.web.fortuneservice.service;

import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.DateHelper;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ttfund.web.fortuneservice.constant.CFHMongodbConstant;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.dao.VerticaDao;
import ttfund.web.fortuneservice.model.dto.LiveScoreFieldsSynModel;
import ttfund.web.fortuneservice.model.dto.LiveScoreFundSynModel;
import ttfund.web.fortuneservice.utils.TimeUtil;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
public class LiveScoreFieldsSyncService {

    public static final String LIVE_SCORE_POINT_KEY = "TTFund.CFHCache.LiveScoreFieldsSyncService.liveScore";
    public static final String LIVE_SCORE_FUND_POINT_KEY = "TTFund.CFHCache.LiveScoreFieldsSyncService.liveScoreFund";

    private static final Logger logger = LoggerFactory.getLogger(LiveScoreFieldsSyncService.class);

    @Autowired
    private CfhMongodbDao cfhMongodbDao;

    @Autowired
    private VerticaDao verticaDao;

    public void sync() {
        //断点时间
        Date breakPoint = cfhMongodbDao.getBreakPoint(LIVE_SCORE_POINT_KEY, "2024-09-04 00:00:00,000",TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS_SSS);
        logger.info("最近一次表字段同步时间为：{}", TimeUtil.dateToStr(breakPoint, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS_SSS));
        XxlJobLogger.log("最近一次表字段同步时间为：{}", TimeUtil.dateToStr(breakPoint, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS_SSS));

        // 判断本次是否该执行数据库同步
        Date maxUpdateTime = verticaDao.getLiveScoreFieldsSyncMaxLastUpdateTime(breakPoint);
        if (maxUpdateTime == null) {
            logger.info("Vertica数据库中相应表的最新更新时间为：null，无需进行表字段同步操作");
            XxlJobLogger.log("Vertica数据库中相应表的最新更新时间为：null，无需进行表字段同步操作");
            return;
        }
        //查数据
        logger.info("Vertica数据库中相应表的最新更新时间为：{}", TimeUtil.dateToStr(maxUpdateTime, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS_SSS));
        XxlJobLogger.log("Vertica数据库中相应表的最新更新时间为：{}", TimeUtil.dateToStr(maxUpdateTime, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS_SSS));
        // 从Vertica取出相应表字段
        List<LiveScoreFieldsSynModel> liveScoreFieldsSynModelList = verticaDao.getLiveScoreFields(breakPoint);
        liveScoreFieldsSynModelList.forEach(model -> model.set_id(String.valueOf(model.getLIVEID())));

        // 表字段同步到mongodb
        int row = cfhMongodbDao.insertOrUpdate(liveScoreFieldsSynModelList, CFHMongodbConstant.DB_CFH_TRADE, CFHMongodbConstant.TB_LIVE_SCORE);
        logger.info("CFHTradeDB.TB_LIVE_SCORE, 数据插入成功，源数据行数：{}，插入行数：{}", liveScoreFieldsSynModelList.size(), row);
        XxlJobLogger.log("CFHTradeDB.TB_LIVE_SCORE, 数据插入成功，源数据行数：{}，插入行数：{}", liveScoreFieldsSynModelList.size(), row);

        //更新断点
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("time", DateHelper.dateToStr(maxUpdateTime, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS_SSS));
        cfhMongodbDao.upsertCFHGeneralData(LIVE_SCORE_POINT_KEY, Arrays.asList(jsonObject));
    }

    public void syncFund(String param) {

        //查数据
        List<LiveScoreFundSynModel> liveScoreFieldsSynModelList = verticaDao.getLiveScoreFundFields();
        liveScoreFieldsSynModelList.forEach(model -> model.set_id(model.getLiveid() + model.getFundcode()));

        // 表字段同步到mongodb
        int row = cfhMongodbDao.insertOrUpdate(liveScoreFieldsSynModelList, CFHMongodbConstant.DB_CFH_TRADE, CFHMongodbConstant.TB_LIVE_FUND_SCORE);
        logger.info("CFHTradeDB.TB_LiveFundScore, 数据插入成功，源数据行数：{}，插入行数：{}", liveScoreFieldsSynModelList.size(), row);
        XxlJobLogger.log("CFHTradeDB.TB_LIVE_SCORE, 数据插入成功，源数据行数：{}，插入行数：{}", liveScoreFieldsSynModelList.size(), row);
    }
}
