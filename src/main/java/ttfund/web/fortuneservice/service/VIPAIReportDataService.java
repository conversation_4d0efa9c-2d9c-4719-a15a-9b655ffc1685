package ttfund.web.fortuneservice.service;

import ttfund.web.fortuneservice.model.dto.MonthlyReportRequestDTO;
import ttfund.web.fortuneservice.model.dto.MonthlyReportResponseDTO;

/**
 * VIP AI报告数据服务接口
 */
public interface VIPAIReportDataService {
    
    /**
     * 保存报告到数据库
     *
     * @param request 请求数据
     * @param response 响应数据
     * @param cfhid 财富号ID
     * @param taskConfigId 任务配置ID
     * @param taskCycleMark 任务周期标记
     * @param index 批次内序号（每个批次从1开始）
     */
    void saveReportToDatabase(MonthlyReportRequestDTO request, MonthlyReportResponseDTO response, String cfhid, String taskConfigId, String taskCycleMark, int index);
}
