package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.constant.VerticaSqlConstant;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.dao.VerticaDao;
import ttfund.web.fortuneservice.model.dto.CFHTradeRankDataDTO;
import ttfund.web.fortuneservice.model.dto.DaysInfoMongoDto;
import ttfund.web.fortuneservice.model.dto.InterfaceFundDayDTO;
import ttfund.web.fortuneservice.model.dto.ReferenceFundDayDTO;
import ttfund.web.fortuneservice.utils.CommonUtil;
import ttfund.web.fortuneservice.utils.ProductCheckUtil;
import ttfund.web.fortuneservice.utils.TimeUtil;

import java.util.*;

@Slf4j
@Service
public class TradeStatServiceImpl {

    private static final String CFH_TRAD_DATA_STAT_KEY = "cfh_trad_data_stat";
    private static final String CFH_TRAD_DATA_STAT_BusinessdInterFaceFundNew_KEY = "cfh_trad_data_stat_BusinessdInterFaceFundNew";
    private static final String CFH_TRAD_DATA_STAT_BusinessdCFHTradeRankJob_KEY = "cfh_trad_data_stat_BusinessdCFHTradeRankJob";
    private static final String CFH_TRAD_DATA_STAT_BusinessdCFHTradeLastRankJob_KEY = "cfh_trad_data_stat_BusinessdCFHTradeLastRankJob";
    private static final String[] TRADE_RANK_TYPE = {"所有", "指数型", "股票型", "混合型", "债券型", "货币型", "QDII", "FOF"};

    @Autowired
    CfhMongodbDao cfhMongodbDao;

    @Autowired
    private VerticaDao verticaDao;


    public void BusinessdReferenceFundNew(String dateStr) {
        Date nowDate = this.getNowDate(dateStr, 17, 0, 0);
        Date todaySixteenDate = TimeUtil.getTodayTimeAsDate(nowDate, 16, 0, 0, 0);
        Boolean checkTime = checkInterFaceFundTime(nowDate, todaySixteenDate, CFH_TRAD_DATA_STAT_KEY, 1,StringUtils.isNotEmpty(dateStr));
        if (!checkTime) {
            return;
        }
        //读ScriptCFH.REFERENCEFUND_DAY_DATE日期calDay
        Date calDay;
        if (StringUtils.isNotEmpty(dateStr)) {
            calDay = TimeUtil.getTodayStartAsDate(DateUtils.addDays(nowDate, -1));
        }else {
            calDay = verticaDao.selectCalDay();
        }
        //读ScriptCFH.REFERENCEFUND_DAY, calDay数据
        List<ReferenceFundDayDTO> dataTable = verticaDao.selectReferenceFundDayData(calDay);
        int referenceFundDayRow = cfhMongodbDao.insertReferenceFundDayData(dataTable);
        log.info("CFHTradeDB.REFERENCEFUND_DAY, {}数据插入成功，源数据行数：{}，插入行数：{}", TimeUtil.dateToStr(calDay, TimeUtil.FORMAT_YYYY_MM_DD), dataTable.size(), referenceFundDayRow);

        //读ScriptCFH.REFERENCEFUND_JJGS, calDay数据
        dataTable = verticaDao.selectReferenceFundJJGSData(calDay);
        int referenceFundJJGSRow = cfhMongodbDao.insertReferenceFundJJGSData(dataTable);
        log.info("CFHTradeDB.REFERENCEFUND_JJGS, {}数据插入成功，源数据行数：{}，插入行数：{}", TimeUtil.dateToStr(calDay, TimeUtil.FORMAT_YYYY_MM_DD), dataTable.size(), referenceFundJJGSRow);

        //读ScriptCFH.REFERENCEFUND_MONTH, calDay数据
        dataTable = verticaDao.selectReferenceFundMonthData(calDay);
        int referenceFundMonthRow = cfhMongodbDao.insertReferenceFundMonthData(dataTable);
        log.info("CFHTradeDB.REFERENCEFUND_MONTH, {}数据插入成功，源数据行数：{}，插入行数：{}", TimeUtil.dateToStr(calDay, TimeUtil.FORMAT_YYYY_MM_DD), dataTable.size(), referenceFundMonthRow);

//        更新断点（当前时间）
        if (StringUtils.isEmpty(dateStr)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("time", DateHelper.dateToStr(TimeUtil.getNowDate(), DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS));
            cfhMongodbDao.upsertCFHGeneralData(CFH_TRAD_DATA_STAT_KEY, Arrays.asList(jsonObject));
        }
    }

    public void businessdInterFaceFundNew(String dateStr) {
        Date nowDate = this.getNowDate(dateStr, 17, 0, 0);
        if (nowDate == null) {
            return;
        }
        Date todaySixteenDate = TimeUtil.getTodayTimeAsDate(nowDate, 16, 0, 0, 0);
        Boolean checkTime = this.checkInterFaceFundTime(nowDate, todaySixteenDate, CFH_TRAD_DATA_STAT_BusinessdInterFaceFundNew_KEY, 4, StringUtils.isNotEmpty(dateStr));
        if (!checkTime) {
            return;
        }
        //读ScriptCFH.REFERENCEFUND_DAY_DATE日期calDay
        Date calDay;
        if (StringUtils.isNotEmpty(dateStr)) {
            calDay = TimeUtil.getTodayStartAsDate(DateUtils.addDays(nowDate, -1));
        }else {
            calDay = verticaDao.selectCalDay();
        }
        Date lastCalDay;
        String lastTradeDay = cfhMongodbDao.getLastTradeDay(DateUtils.addSeconds(calDay, -1));
        if (StringUtils.isEmpty(lastTradeDay)) {
            log.warn("未找到上一交易日");
            lastCalDay = null;
        }else {
            lastCalDay = DateHelper.stringToDate2(lastTradeDay, CommonConstant.FORMAT_YYYY_M_D_H_MM_SS);
        }
        //查上一天交易日数据
        Map<String, Integer> lastDayZeroNums = cfhMongodbDao.getLastInterfaceFundDayS(lastCalDay);
        //ScriptCFH.INTERFACEFUND_DAY
        List<InterfaceFundDayDTO> dataTable = verticaDao.selectInterfaceFundDay(calDay);
        for (InterfaceFundDayDTO dto : dataTable) {
            Integer zeroNum = lastDayZeroNums.get(dto.getFUNDCODE());
            if (zeroNum != null && dto.getZERONUM() != null && dto.getDAYZEROOLD() != null) {
                dto.setDAYZERONUM(dto.getZERONUM()+dto.getDAYZEROOLD()-zeroNum);
            }
        }
        //写INTERFACEFUND_DAY
        int row = cfhMongodbDao.insertInterfaceFundDayData(dataTable);
        log.info("CFHTradeDB.INTERFACEFUND_DAY, {}数据插入成功，源数据行数：{}，插入行数：{}", TimeUtil.dateToStr(calDay, TimeUtil.FORMAT_YYYY_MM_DD), dataTable.size(), row);

        Map<String, Integer> lastJJGSZeroNums = cfhMongodbDao.getLastInterfaceFundJJGSS(lastCalDay);
        //ScriptCFH.INTERFACEFUND_JJGS
        dataTable = verticaDao.selectInterfaceFundJJGS(calDay);
        for (InterfaceFundDayDTO dto : dataTable) {
            Integer zeroNum = lastJJGSZeroNums.get(dto.getJJGSID());
            if ("全部".equals(dto.getFUNDTYPE()) && zeroNum != null && dto.getZERONUM() != null && dto.getDAYZEROOLD() != null) {
                dto.setDAYZERONUM(dto.getZERONUM() + dto.getDAYZEROOLD() - zeroNum);
            }
        }
        //INTERFACEFUND_JJGS
        row = cfhMongodbDao.insertInterfaceFundJJGSData(dataTable);
        log.info("CFHTradeDB.INTERFACEFUND_JJGS, {}数据插入成功，源数据行数：{}，插入行数：{}", TimeUtil.dateToStr(calDay, TimeUtil.FORMAT_YYYY_MM_DD), dataTable.size(), row);

        //CFHTradeDB.FUNDCUSTNO_FEATURE表
        Map<String, String> fundCustNoFeatureSqlMap = VerticaSqlConstant.getFundCustNoFeature();
        for (Map.Entry<String, String> entry : fundCustNoFeatureSqlMap.entrySet()) {
            String name = entry.getKey();
            String sql = entry.getValue();
            dataTable = verticaDao.selectSql(sql);
            int delRow = cfhMongodbDao.deleteFundCustnoFeatureByFeatureType(name);
            log.info("CFHTradeDB.FUNDCUSTNO_FEATURE_{}, {}删除成功，行数：{}", name, TimeUtil.dateToStr(calDay, TimeUtil.FORMAT_YYYY_MM_DD), delRow);
            row = cfhMongodbDao.insertFundCustnoFeaturEData(dataTable);
            log.info("CFHTradeDB.FUNDCUSTNO_FEATURE_{}, {}数据插入成功，源数据行数：{}，插入行数：{}", name, TimeUtil.dateToStr(calDay, TimeUtil.FORMAT_YYYY_MM_DD), dataTable.size(), row);
        }
        dataTable = verticaDao.selectSql(VerticaSqlConstant.FUNDCUSTNO_FEATURE_PRODIS);
        int delRow = cfhMongodbDao.deleteFundCustnoFeatureByFeatureType("PRODIS");
        log.info("CFHTradeDB.FUNDCUSTNO_FEATURE_PRODIS, {}删除成功，行数：{}", TimeUtil.dateToStr(calDay, TimeUtil.FORMAT_YYYY_MM_DD), delRow);

        row = cfhMongodbDao.insertFundCustnoFeaturEData(dataTable);
        log.info("CFHTradeDB.FUNDCUSTNO_FEATURE_PRODIS, {}数据插入成功，源数据行数：{}，插入行数：{}", TimeUtil.dateToStr(calDay, TimeUtil.FORMAT_YYYY_MM_DD), dataTable.size(), row);

        //CFHTradeDB.COMCUSTNO_FEATURE表
        Map<String, String> comCustNoFeatureSqlMap = VerticaSqlConstant.getComCustNoFeature();
        for (Map.Entry<String, String> entry : comCustNoFeatureSqlMap.entrySet()) {
            String name = entry.getKey();
            String sql = entry.getValue();
            dataTable = verticaDao.selectSql(sql);
            row = cfhMongodbDao.insertCustnoFeaturEData(dataTable);
            log.info("CFHTradeDB.{}, {}数据插入成功，源数据行数：{}，插入行数：{}", name, TimeUtil.dateToStr(calDay, TimeUtil.FORMAT_YYYY_MM_DD), dataTable.size(), row);
        }
//        更新断点（当前时间）
        if (StringUtils.isEmpty(dateStr)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("time", DateHelper.dateToStr(TimeUtil.getNowDate(), DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS));
            cfhMongodbDao.upsertCFHGeneralData(CFH_TRAD_DATA_STAT_BusinessdInterFaceFundNew_KEY, Arrays.asList(jsonObject));
        }
    }

    public void cfhTradeRankJob(String dateStr) {
        Date nowDate = this.getNowDate(dateStr,17,0,0);
        if (nowDate == null) {
            return;
        }
        Date todaySixteenDate = TimeUtil.getTodayTimeAsDate(nowDate, 16, 0, 0, 0);
        Boolean checkTime = this.checkCfhTradeRankTime(nowDate, todaySixteenDate, CFH_TRAD_DATA_STAT_BusinessdCFHTradeRankJob_KEY, "2024-08-29 00:00:00", null);

        if (!checkTime) {
            return;
        }
        List<CFHTradeRankDataDTO> dataTable = verticaDao.selectSql(VerticaSqlConstant.CFH_TRADE_RANK_STAT, CFHTradeRankDataDTO.class);

        int CFTTradeRankDataRow = cfhMongodbDao.insertCFTTradeRankData(dataTable);
        log.info("CFHTradeDB.TB_CFHTRADE_RANK, {}数据插入成功，源数据行数：{}，插入行数：{}",
                TimeUtil.dateToStr(TimeUtil.getNowDate(), TimeUtil.FORMAT_YYYY_MM)
                , dataTable.size(), CFTTradeRankDataRow);

//        更新断点（当前时间）
        if (StringUtils.isEmpty(dateStr)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("time", DateHelper.dateToStr(TimeUtil.getNowDate(), DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS));
            cfhMongodbDao.upsertCFHGeneralData(CFH_TRAD_DATA_STAT_BusinessdCFHTradeRankJob_KEY, Arrays.asList(jsonObject));
        }
    }

    private Boolean checkCfhTradeRankTime(Date nowDate, Date nowAfterDate, String breakPointKey, String defaultValue, Integer tradeDayNum) {
        //是否校验当前时间
        if (nowDate == null) {
            log.info("当前时间未获取到，不执行");
            return false;
        }
        if (nowAfterDate != null && nowDate.before(nowAfterDate)) {
            log.info("当前时间:{},小于:{}，不执行"
                    , TimeUtil.dateToStr(nowDate, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS)
                    , TimeUtil.dateToStr(nowAfterDate, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS));
            return false;
        }
        //获取配置断点时间
        if (StringUtils.isNotEmpty(breakPointKey)) {
            Date breakPoint = cfhMongodbDao.getBreakPoint(breakPointKey, defaultValue);
            //当前时间大于断点时间才执行
            if (nowDate.before(breakPoint)) {
                log.info("当前时间:{},小于断点时间:{}，不执行", TimeUtil.dateToStr(nowDate, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS)
                        , TimeUtil.dateToStr(breakPoint, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS));
                return false;
            }
        }
        if (tradeDayNum != null) {
            nowDate = TimeUtil.getTodayStartAsDate(nowDate);
            //本月第X个交易日执行
            Calendar instance = Calendar.getInstance();
            instance.setTime(nowDate);
            //昨天所在月的日期
            List<DaysInfoMongoDto> daysInfoList = cfhMongodbDao.getDaysInfoByTimeRange(CommonUtil.getFirstDayOfMonth(instance), CommonUtil.getLastDayOfMonth(instance));
            int dayNumOfMonth = ProductCheckUtil.calculateTradeDayNumber(CommonUtil.getCurrentBelongDay(nowDate), daysInfoList, false);
            if (dayNumOfMonth != tradeDayNum) {
                log.info("当前时间：{}，为本月第{}个交易日，不是本月第{}个交易日，不执行。"
                        , DateHelper.dateToStr(nowDate, DateHelper.FORMAT_YYYY_MM_DD)
                        , dayNumOfMonth, tradeDayNum
                );
                return false;
            }
        }
        return true;
    }
    private Boolean checkInterFaceFundTime(Date nowDate, Date nowAfterDate, String breakPointKey, int run, boolean ifRun) {
        if (ifRun) {
            return true;
        }
        //是否校验当前时间
        if (nowDate == null) {
            log.info("当前时间未获取到，不执行");
            return false;
        }
        if (nowAfterDate != null && nowDate.before(nowAfterDate)) {
            log.info("当前时间:{},小于:{}，不执行"
                    , TimeUtil.dateToStr(nowDate, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS)
                    , TimeUtil.dateToStr(nowAfterDate, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS));
            return false;
        }
        //获取配置断点时间
        if (StringUtils.isNotEmpty(breakPointKey)) {
            Date breakPoint = cfhMongodbDao.getBreakPoint(breakPointKey, "2024-09-26 00:00:00");
            //当前时间大于断点时间才执行
            if (nowDate.before(breakPoint)) {
                log.info("当前时间:{},小于断点时间:{}，不执行", TimeUtil.dateToStr(nowDate, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS)
                        , TimeUtil.dateToStr(breakPoint, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS));
                return false;
            }
        }
        if (run == 4) {
            //读ScriptCFH.INTERFACEFUND_DAY_RUN 为4才执行
            int runDB = verticaDao.selectRun(VerticaSqlConstant.INTERFACE_FUND_DAY_RUN);
            if (runDB != run) {
                log.info("ScriptCFH.INTERFACEFUND_DAY_RUN响应为：{}，不为4，不执行统计", run);
                return false;
            }
        }
        if (run == 1) {
            //读ScriptCFH.REFERENCEFUND_DAY_RUN响应1才执行
            int runDB = verticaDao.selectRun(VerticaSqlConstant.REFERENCE_FUND_DAY_RUN);
            if (runDB != run) {
                log.info("ScriptCFH.REFERENCEFUND_DAY_RUN响应为：{}，不为1，不执行统计", run);
                return false;
            }
        }
        //当前时间-1天为交易日才执行
        List<DaysInfoMongoDto> daysInfoList = cfhMongodbDao.getDaysInfoByTimeRange(DateUtils.addDays(nowDate, -1), nowDate);
        if (!ProductCheckUtil.checkIsTradeDay(DateUtils.addDays(nowDate, -1), daysInfoList)) {
            log.info("当前时间为{}，上一天非交易日，不需要执行统计", DateHelper.dateToStr(nowDate, DateHelper.FORMAT_YYYY_MM_DD));
            return false;
        }
        return true;
    }

    private Date getNowDate(String dateStr, int addHours, int addMinutes, int addSeconds) {
        Date nowDate = TimeUtil.getNowDate();
        if (StringUtils.isNotEmpty(dateStr)) {
            try {
                nowDate = TimeUtil.stringToDate2(dateStr, TimeUtil.FORMAT_YYYYMMDD);
                nowDate = DateUtils.addHours(nowDate, addHours);
                nowDate = DateUtils.addMinutes(nowDate, addMinutes);
                nowDate = DateUtils.addSeconds(nowDate, addSeconds);
            } catch (Exception e) {
                log.warn("日期格式错误,要求格式：{}，参数:{}", TimeUtil.FORMAT_YYYYMMDD, dateStr);
                return null;
            }
        }
        return nowDate;
    }

    public void cfhTradeRankLastMonthJob(String dateStr) {
        Date nowDate = this.getNowDate(dateStr,17,0,0);
        if (nowDate == null) {
            return;
        }
        Date todaySixteenDate = TimeUtil.getTodayTimeAsDate(nowDate, 16, 0, 0, 0);
        Boolean checkTime = this.checkCfhTradeRankTime(nowDate, todaySixteenDate, CFH_TRAD_DATA_STAT_BusinessdCFHTradeLastRankJob_KEY, "2024-08-29 00:00:00", 3);

        if (!checkTime) {
            return;
        }
        //上月统计日期参数
        Date dt = DateUtils.addMonths(nowDate, -1);
        List<CFHTradeRankDataDTO> dataTable = verticaDao.selectCFHTradeRankData(dt, DateUtils.addMonths(dt, -1));

        int CFTTradeRankDataRow = cfhMongodbDao.insertCFTTradeRankData(dataTable);
        log.info("CFHTradeDB.TB_CFHTRADE_RANK, {}数据插入成功，源数据行数：{}，插入行数：{}",
                TimeUtil.dateToStr(DateUtils.addMonths(nowDate, -1), TimeUtil.FORMAT_YYYY_MM)
                , dataTable.size(), CFTTradeRankDataRow);

//        更新断点（当前时间）
        if (StringUtils.isEmpty(dateStr)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("time", DateHelper.dateToStr(TimeUtil.getNowDate(), DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS));
            cfhMongodbDao.upsertCFHGeneralData(CFH_TRAD_DATA_STAT_BusinessdCFHTradeLastRankJob_KEY, Arrays.asList(jsonObject));
        }
    }
}
