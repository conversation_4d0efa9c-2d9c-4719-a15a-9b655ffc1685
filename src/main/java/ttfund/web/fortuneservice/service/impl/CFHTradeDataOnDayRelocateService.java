package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.DateHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import ttfund.web.fortuneservice.config.App;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.constant.VerticaSqlConstant;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.dao.CfhTradeMongodbDao;
import ttfund.web.fortuneservice.dao.VerticaDao;
import ttfund.web.fortuneservice.manager.TTTradeDetailApi;
import ttfund.web.fortuneservice.model.dto.FundApplyDataStatModel;
import ttfund.web.fortuneservice.model.dto.TradeDetailRecord;
import ttfund.web.fortuneservice.utils.DateUtils;
import ttfund.web.fortuneservice.utils.TimeUtil;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/2/7 13:09
 * 财富号交易数据全天数据迁移
 */
@Service
public class CFHTradeDataOnDayRelocateService {
    private static Logger logger = LoggerFactory.getLogger(CFHTradeDataOnDayRelocateService.class);

    /**
     * 全天交易数据是否迁移成功
     */
    private static final String CFH_DATA_RELOCATE_KEY = "cfh_data_relocate";

    @Autowired
    CfhMongodbDao cfhMongodbDao;

    @Resource
    private TTTradeDetailApi tradeDetailApi;

    @Resource
    private VerticaDao verticaDao;

    @Resource
    private CfhTradeMongodbDao cfhTradeMongodbDao;

    @Resource
    private App app;

    public ReturnT<String> sync() {
        XxlJobLogger.log("start to relocate data on day······");
        //判断是否为交易日，只有交易日才执行; 16点之前不执行
        Date dt = new Date();
        if (!tradeDetailApi.isTradeDay(dt) || DateUtils.getHour(dt) < 16) {
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "当前全天数据迁移无需执行");
        }

        //当mongoDB中CFH_DATA_RELOCATE_KEY值为value时，不执行
        String value = DateHelper.dateToStr(dt, DateHelper.FORMAT_YYYYMMDD);
        String cfhGeneralData = cfhMongodbDao.getCFHGeneralData(CFH_DATA_RELOCATE_KEY, "false");
        if (String.format("[{\"time\":\"%s\"}]", value).equals(cfhGeneralData)) {
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "全天数据迁移已经成功，此次无需执行");
        }

        //1、获取全天的交易数据
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("获取全天的交易数据_耗时");
        List<TradeDetailRecord> dayData = getDayData(dt);
        stopWatch.stop();
        if (CollectionUtils.isEmpty(dayData)) {
            logger.error("--------获取全天交易数据失败--------");
            return new ReturnT<>(ReturnT.FAIL_CODE, String.format("本次获取全天交易数据未成功，耗时详情:%s", JSON.toJSONString(stopWatch.getTaskInfo())));
        }

        int dayDataSize = dayData.size();
        TradeDetailRecord firstRecord = dayData.get(0);
        Date transDate = firstRecord.getTransactionDate() == null? dt: firstRecord.getTransactionDate();

        String yesterdayKey = "trade805_" + TimeUtil.dateToStr(DateUtils.addDays(TimeUtil.getNowDate(), -1), DateHelper.FORMAT_YYYYMMDD);
        String todayKey = "trade805_" + TimeUtil.dateToStr(TimeUtil.getNowDate(), DateHelper.FORMAT_YYYYMMDD);

        Iterator<TradeDetailRecord> iterator = dayData.iterator();
        while (iterator.hasNext()) {
            TradeDetailRecord record = iterator.next();
            String jsonString = JSON.toJSONString(record);
            logger.info("全天交易数据：{}", jsonString);
            //805类型校验缓存，保存今天的缓存
            if ("805".equals(record.getParentTradeType())) {
                app.cfhrediswrite.hSet(todayKey, record.getApplyNo(), jsonString);
                //昨天已经给了的不要
                String s = app.cfhrediswrite.hGet(yesterdayKey, record.getApplyNo());
                if (StringUtils.isNotEmpty(s)) {
                    dayData.remove(record);
                    logger.info("全天交易数据重复，本次跳过：{}", jsonString);
                }
            }
        }
        app.cfhrediswrite.expire(todayKey, 60 * 60 * 24 * 2L);
        //2、按照交易日删除交易数据并插入全天交易数据
        stopWatch.start("按照交易日期删除分时交易数据并插入全天交易数据_耗时");
        boolean flag1 = verticaDao.delByDayAndInsertData(VerticaSqlConstant.SQL_DELETE_TRADE_DETAILS_BY_TRANSACTION_DATE
                , VerticaSqlConstant.SQL_INSERT_CFH_TRADE_DETAIL, transDate, CFHTradeDataRelocateCommonService.getInsertTradeDataParamList(dayData));
        stopWatch.stop();
        if (!flag1) {
            logger.error("按照交易日删除分时交易数据并插入全天交易数据失败");
            return new ReturnT<>(ReturnT.FAIL_CODE, String.format("按照交易日删除分时交易数据并插入全天交易数据失败，耗时详情：%s", JSON.toJSONString(stopWatch.getTaskInfo())));
        }

        dayData = null;
        //3、执行sql统计全天数据
        stopWatch.start("统计全天交易数据_耗时");
        List<FundApplyDataStatModel> dataStatModels = verticaDao.statTradeData(transDate);
        stopWatch.stop();
        if (CollectionUtils.isEmpty(dataStatModels)) {
            logger.error("统计全天交易数据结果为空");
            return new ReturnT<>(ReturnT.FAIL_CODE, String.format("统计整个交易日的交易数据结果为空，耗时详情：%s", JSON.toJSONString(stopWatch.getTaskInfo())));
        }

        //4、按照交易日期删除统计数据并插入全天统计数据
        stopWatch.start("按照交易日期删除并插入全天统计后的数据_耗时");
        boolean flag2 = verticaDao.delByDayAndInsertData(VerticaSqlConstant.SQL_DELETE_STAT_DATA_BY_TRANSACTION_DAY
                , VerticaSqlConstant.SQL_INSERT_STAT_DATA, transDate, CFHTradeDataRelocateCommonService.getInsertStatDataParamList(dataStatModels));
        stopWatch.stop();
        if (!flag2) {
            logger.error("按照交易日期删除统计数据并插入全天统计数据失败");
            return new ReturnT<>(ReturnT.FAIL_CODE, String.format("按照交易日期删除统计数据并插入全天统计数据失败，耗时详情：%s", JSON.toJSONString(stopWatch.getTaskInfo())));
        }

        //5、将统计数据写入mongodb中
        stopWatch.start("将全天统计数据更新到mongodb_耗时");
        cfhTradeMongodbDao.updateAndInsertCFHData(transDate, dataStatModels);
        stopWatch.stop();
        //更新断点
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("time", value);
        cfhMongodbDao.upsertCFHGeneralData(CFH_DATA_RELOCATE_KEY, Arrays.asList(jsonObject));
        return new ReturnT<>(ReturnT.SUCCESS_CODE, String.format("插入全天交易数据共%s条，插入全天统计数据共%s条，全天数据迁移总耗时：%s(秒)，耗时详情为：%s",
                dayDataSize, dataStatModels.size(), stopWatch.getTotalTimeSeconds(), JSON.toJSONString(stopWatch.getTaskInfo())));
    }

    /**
     * 获取全天数据 失败则重试，重试失败则调用补充数据接口
     *
     * @return
     */
    private List<TradeDetailRecord> getDayData(Date dt) {
        int retryTimes = CommonConstant.TRADE_DATA_GET_RETRY_TIMES;
        List<TradeDetailRecord> dayData = null;
        while (retryTimes > 0) {
            dayData = tradeDetailApi.getDayData();
            if (!CollectionUtils.isEmpty(dayData)) {
                break;
            }
            retryTimes--;
            try {
                Thread.sleep(CommonConstant.TRADE_DATA_API_WAIT_TIME);
            } catch (InterruptedException e) {
                logger.error("等待获取全天交易数据线程被打断，{},{}", e.getMessage(), e);
                Thread.currentThread().interrupt();
            }
        }
        //兜底走补充数据接口
        if (retryTimes <= 0) {
            dayData = getLoseDayData(dt);
        }
        return dayData;
    }

    /**
     * 补充数据接口获取全天数据 1h只能获取一次
     *
     * @return
     */
    private List<TradeDetailRecord> getLoseDayData(Date dt) {
        int retryTimes = CommonConstant.TRADE_DATA_GET_RETRY_TIMES;
        List<TradeDetailRecord> dayData = null;
        while (retryTimes > 0) {
            dayData = tradeDetailApi.getLoseData(dt);
            if (!CollectionUtils.isEmpty(dayData)) {
                break;
            }
            retryTimes--;
            try {
                Thread.sleep(CommonConstant.TRADE_DATA_API_WAIT_TIME);
            } catch (InterruptedException e) {
                logger.error("等待获取补充数据线程被打断，{},{}", e.getMessage(), e);
                Thread.currentThread().interrupt();
            }
        }
        return dayData;
    }

    public ReturnT<String> syncBack(String param) {
        StopWatch stopWatch = new StopWatch();

        Date dt = null;
        try {
            if (StringUtils.isNotEmpty(param)) {
                dt = TimeUtil.stringToDate2(param, TimeUtil.FORMAT_YYYYMMDD);
            }
        } catch (Exception e) {
            return new ReturnT<>(ReturnT.FAIL_CODE, String.format("参数解析交易日异常：%s", param));
        }
        if (dt == null || !tradeDetailApi.isTradeDay(dt)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, String.format("参数不是交易日：%s", param));
        }
        if (org.apache.commons.lang3.time.DateUtils.isSameDay(dt, TimeUtil.getNowDate()) && DateUtils.getHour(dt) < 16) {
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "补录当天数据需要到16点以后");
        }
        //获取当天数据
        List<TradeDetailRecord> loseDayData = getLoseDayData(dt);
        if (CollectionUtils.isEmpty(loseDayData)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, String.format("查询当天交易数据失败！"));
        }
        //按照交易日删除交易数据并插入全天交易数据
        boolean flag1 = verticaDao.delByDayAndInsertData(VerticaSqlConstant.SQL_DELETE_TRADE_DETAILS_BY_TRANSACTION_DATE
                , VerticaSqlConstant.SQL_INSERT_CFH_TRADE_DETAIL, dt, CFHTradeDataRelocateCommonService.getInsertTradeDataParamList(loseDayData));
        if (!flag1) {
            logger.error("按照交易日删除分时交易数据并插入全天交易数据失败");
            return new ReturnT<>(ReturnT.FAIL_CODE, String.format("按照交易日删除分时交易数据并插入全天交易数据失败"));
        }
        stopWatch.start("统计全天交易数据_耗时");
        List<FundApplyDataStatModel> dataStatModels = verticaDao.statTradeData(dt);
        stopWatch.stop();
        if (CollectionUtils.isEmpty(dataStatModels)) {
            logger.error("统计全天交易数据结果为空");
            return new ReturnT<>(ReturnT.FAIL_CODE, String.format("统计整个交易日的交易数据结果为空，耗时详情：%s", JSON.toJSONString(stopWatch.getTaskInfo())));
        }
        //写vertica
        boolean flag2 = verticaDao.delByDayAndInsertData(VerticaSqlConstant.SQL_DELETE_STAT_DATA_BY_TRANSACTION_DAY
                , VerticaSqlConstant.SQL_INSERT_STAT_DATA, dt, CFHTradeDataRelocateCommonService.getInsertStatDataParamList(dataStatModels));

        //写mongoDB
        stopWatch.start("将全天统计数据更新到mongodb_耗时");
        cfhTradeMongodbDao.deleteAndInsertCFHData(dt, dataStatModels);
        stopWatch.stop();
        int dayDataSize = dataStatModels.size();
        return new ReturnT<>(ReturnT.SUCCESS_CODE, String.format("【补录】插入全天交易数据共%s条，插入全天统计数据共%s条，全天数据迁移总耗时：%s(秒)，耗时详情为：%s",
                dayDataSize, dataStatModels.size(), stopWatch.getTotalTimeSeconds(), JSON.toJSONString(stopWatch.getTaskInfo())));
    }
}
