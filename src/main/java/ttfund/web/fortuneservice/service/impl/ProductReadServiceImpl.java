package ttfund.web.fortuneservice.service.impl;

import com.mongodb.BasicDBObject;
import com.mongodb.client.model.UpdateOptions;
import com.mongodb.client.result.UpdateResult;
import com.ttfund.web.base.helper.CommonHelper;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import ttfund.web.fortuneservice.config.App;
import ttfund.web.fortuneservice.constant.CFHMongodbConstant;
import ttfund.web.fortuneservice.constant.CFHMongodbConstant.CFHProductReadHqField;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.constant.HqMongodbConstant;
import ttfund.web.fortuneservice.constant.HqMongodbConstant.FundNoticeField;
import ttfund.web.fortuneservice.constant.HqMongodbConstant.HqCommonField;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.model.dto.FundNoticeDto;
import ttfund.web.fortuneservice.service.ProductReadService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 读取公募公告、私募公告，写入财富号产品解读表
 * <AUTHOR>
 * @date 2022/10/11 17:04
 * @version 1.0
 */
@Service
public class ProductReadServiceImpl implements ProductReadService {

    private static final Logger logger = LoggerFactory.getLogger(ProductReadServiceImpl.class);

    @Resource
    private App app;

    @Resource
    private CfhMongodbDao cfhMongodbDao;

    @Override
    public void writeProductReadIntoHq(Date nowDate) {
        // 公共查询条件
        BasicDBObject noticeFields = new BasicDBObject(CommonHelper.fieldDic(FundNoticeDto.class));
        BasicDBObject noticeSort = new BasicDBObject(HqCommonField.UPDATE_TIME, -1);

        // 1、读取私募公告表
        this.readPrivateNotice(nowDate, noticeFields, noticeSort);
        // 2、读取公募公告表, MATCHTYPE=1
        this.readNoticeMatchType1(nowDate, noticeFields, noticeSort);
        // 3、读取公募公告表, MATCHTYPE=2
        this.readNoticeMatchType2(nowDate, noticeFields, noticeSort);

        // 4、清理TB_CFHProductReadHq表
        int clearCount = clearProductReadHq(nowDate);
        logger.info("财富号公告信息清理完成，共删除_{}_条数据", clearCount);
    }

    /**
     * 读取私募公告表
     * <AUTHOR>
     * @date 2023/4/10 14:30
     * @param nowDate 当前日期
     * @param noticeFields 返回字段
     * @param noticeSort 排序字段
     */
    private void readPrivateNotice(Date nowDate, BasicDBObject noticeFields, BasicDBObject noticeSort) {
        int modifyCount = 0;
        BasicDBObject privateNoticeQuery = new BasicDBObject()
                .append(HqCommonField.BEGIN_TIME, new BasicDBObject("$lt", nowDate))
                .append(HqCommonField.END_TIME, new BasicDBObject("$gte", nowDate));
        long privateNoticeCount = app.getHqMongodbRead().getMongoserver()
                .getDatabase(HqMongodbConstant.DB_FUND_ANNOUNCEMENT)
                .getCollection(HqMongodbConstant.TB_FUND_PRIVATE_NOTICE)
                .countDocuments(privateNoticeQuery);
        for (int i = 0; i < Math.ceil(privateNoticeCount / (CommonConstant.MONGO_READ_MAX_COUNT + 0.0)); i++) {
            List<Document> privateNoticeList = app.getHqMongodbRead().query(HqMongodbConstant.DB_FUND_ANNOUNCEMENT, HqMongodbConstant.TB_FUND_PRIVATE_NOTICE,
                    privateNoticeQuery, noticeFields, noticeSort, i + 1, CommonConstant.MONGO_READ_MAX_COUNT, Document.class);
            modifyCount += insertProductReadHq(privateNoticeList, 2, HqMongodbConstant.TB_FUND_PRIVATE_NOTICE,"1");
        }
        logger.info("私募公告共匹配到_{}_条数据，共更新_{}_条数据", privateNoticeCount, modifyCount);
    }

    /**
     * 读取公募公告表 MATCHTYPE=1
     * <p>如果按类型配置的公告，根据FUNDTYPE类型，在基本信息表中找出符合的基金，按照类型填充基金code，生成类型公告</p>
     * <p>查询单基匹配公告MATCHTYPE=1,需要适配旧数据（不存在MATCHTYPE）都当作单基配置</p>
     * <AUTHOR>
     * @date 2023/4/10 14:33
     * @param nowDate 当前日期
     * @param noticeFields 返回字段
     * @param noticeSort 排序字段
     */
    private void readNoticeMatchType1(Date nowDate, BasicDBObject noticeFields, BasicDBObject noticeSort) {
        int modifyCount = 0;
        BasicDBObject noticeQuery = new BasicDBObject()
                .append("$or",Arrays.asList(
                        new BasicDBObject(FundNoticeField.MATCH_TYPE, new BasicDBObject("$exists", false)),
                        new BasicDBObject(FundNoticeField.MATCH_TYPE, new BasicDBObject("$in", Arrays.asList("1", "")))
                )).append(HqCommonField.BEGIN_TIME, new BasicDBObject("$lt", nowDate))
                .append(HqCommonField.END_TIME, new BasicDBObject("$gte", nowDate));
        long noticeCount = app.getHqMongodbRead().getMongoserver()
                .getDatabase(HqMongodbConstant.DB_FUND_ANNOUNCEMENT)
                .getCollection(HqMongodbConstant.TB_FUND_NOTICE)
                .countDocuments(noticeQuery);
        for (int i = 0; i < Math.ceil(noticeCount / (CommonConstant.MONGO_READ_MAX_COUNT + 0.0)); i++) {
            List<Document> noticeList = app.getHqMongodbRead().query(HqMongodbConstant.DB_FUND_ANNOUNCEMENT, HqMongodbConstant.TB_FUND_NOTICE,
                    noticeQuery, noticeFields, noticeSort, i + 1, CommonConstant.MONGO_READ_MAX_COUNT, Document.class);
            modifyCount += insertProductReadHq(noticeList,1, HqMongodbConstant.TB_FUND_NOTICE,"1");
        }
        logger.info("公募公告(单基配置)共匹配到_{}_条数据，共更新_{}_条数据", noticeCount, modifyCount);
    }

    /**
     * 读取公募公告表 MATCHTYPE=2
     * <p>查询类型匹配公告MATCHTYPE=2，需要根据FUNDTYPE按照规则查询基本信息表中符合该规则的基金，生成公告</p>
     * <AUTHOR>
     * @date 2023/4/10 14:37
     * @param nowDate 当前日期
     * @param noticeFields 返回字段
     * @param noticeSort 排序字段
     */
    private void readNoticeMatchType2(Date nowDate, BasicDBObject noticeFields, BasicDBObject noticeSort) {
        int modifyCount = 0;
        BasicDBObject typeNoticeQuery = new BasicDBObject()
                .append(FundNoticeField.MATCH_TYPE, "2")
                .append(HqCommonField.BEGIN_TIME, new BasicDBObject("$lt", nowDate))
                .append(HqCommonField.END_TIME, new BasicDBObject("$gte", nowDate));
        long typeNoticeCount = app.getHqMongodbRead().getMongoserver()
                .getDatabase(HqMongodbConstant.DB_FUND_ANNOUNCEMENT)
                .getCollection(HqMongodbConstant.TB_FUND_NOTICE)
                .countDocuments(typeNoticeQuery);
        for (int i = 0; i < Math.ceil(typeNoticeCount / (CommonConstant.MONGO_READ_MAX_COUNT + 0.0)); i++){
            List<Document> typeNoticeList = app.getHqMongodbRead().query(HqMongodbConstant.DB_FUND_ANNOUNCEMENT, HqMongodbConstant.TB_FUND_NOTICE,
                    typeNoticeQuery, noticeFields, noticeSort, i + 1,CommonConstant.MONGO_READ_MAX_COUNT, Document.class);
            modifyCount += insertProductReadHq(convertTypeNotice(typeNoticeList), 1, HqMongodbConstant.TB_FUND_NOTICE, "2");
        }
        logger.info("公募公告(类型配置)共匹配到_{}_条数据，共更新_{}_条数据", typeNoticeCount, modifyCount);
    }

    /**
     * 将类型配置公告，转换为单基公告，写入财富号公告表，FUNDTYPE字段是多选的，逗号分隔（比如26,31,35）
     * <AUTHOR>
     * @date 2023/4/10 14:50
     * @param noticeList 公募公告数据集合
     * @return java.util.List
     */
    private List<Document> convertTypeNotice(List<Document> noticeList){
        for (Document model : noticeList) {
            String fundType = model.getString(FundNoticeField.FUND_TYPE);
            if(StringUtils.isBlank(fundType)){
                continue;
            }
            List<String> fundCodes = new ArrayList<>();
            for (String type : fundType.split(",")) {
                BasicDBObject query = new BasicDBObject();
                BasicDBObject fields = new BasicDBObject(FundNoticeField.F_CODE, 1);
                switch (type){
                    // ETF
                    case "ETF":
                        query.append(FundNoticeField.FEATURE, new BasicDBObject("$regex", "010"));
                        break;
                    // REITS
                    case "REITS":
                        query.append(FundNoticeField.B_TYPE,"09");
                        break;
                    // 未匹配到则跳过此次循环
                    default: continue;
                }
                List<Document> documentList = app.getHqMongodbRead().query(HqMongodbConstant.DB_FUND_ARCHIVES,
                        HqMongodbConstant.TB_FUND_JBXX, query, fields, null, 0, Document.class);
                List<String> codeList = documentList.stream()
                        .map(item -> item.getString(FundNoticeField.F_CODE))
                        .collect(Collectors.toList());
                fundCodes.addAll(codeList);
                // 基金code去重
                fundCodes = fundCodes.stream().distinct().collect(Collectors.toList());
            }
            model.put(FundNoticeField.F_CODE, String.join(",", fundCodes));
        }
        return noticeList;
    }

    /**
     * 插入(更新)基金公告-公募、私募信息
     * <AUTHOR>
     * @date 2023/4/10 14:57
     * @param noticeList 公募/私募公告数据集合
     * @param saleType 1-公募 2-私募
     * @param sourceName 公募/私募集合名
     * @param matchType 匹配类型：1根据基金代码匹配 2根据基金类型匹配
     * @return int
     */
    private int insertProductReadHq(List<Document> noticeList, Integer saleType, String sourceName, String matchType){
        int modifyCount = 0;
        for (Document model : noticeList) {
            if(StringUtils.isBlank(model.getString(FundNoticeField.F_CODE))){
                continue;
            }
            // 增加类型判断，saleType 1公募 2私募
            model.append(CFHProductReadHqField.SALE_TYPE, saleType);
            model.append(CFHProductReadHqField.SOURCE_NAME, sourceName);
            model.append(FundNoticeField.FUND_TYPE, model.getString(FundNoticeField.FUND_TYPE) == null ? "" : model.getString(FundNoticeField.FUND_TYPE));
            model.append(FundNoticeField.MATCH_TYPE, matchType);
            // 如果是类型匹配，则先删除MATCHTYPE=2 and EID相匹配的公告数据
            if("2".equals(matchType)){
                BasicDBObject deleteQuery = new BasicDBObject()
                        .append(FundNoticeField.MATCH_TYPE, "2")
                        .append(HqCommonField.EID, model.getString(HqCommonField.EID));
                app.getCfhMongodbWrite().getMongoserver()
                        .getDatabase(CFHMongodbConstant.DB_TT_FUND_CFH)
                        .getCollection(CFHMongodbConstant.TB_CFH_PRODUCT_READ_HQ)
                        .deleteMany(deleteQuery);
            }
            // 如果有多个code，则拆分成列表进行存储,减少存储空间，方便查询，但最大限制@CODE_ARRAY_SIZE
            List<String> fundCodes = Arrays.asList(model.getString(FundNoticeField.F_CODE).split(","));
            for (int i = 0; i < Math.ceil(fundCodes.size() / (CommonConstant.HQ_NOTICE_FUND_CODES_ARRAY_SIZE + 0.0)); i++) {
                List<String> subList = fundCodes.subList(i * CommonConstant.HQ_NOTICE_FUND_CODES_ARRAY_SIZE, Math.min((i + 1) * CommonConstant.HQ_NOTICE_FUND_CODES_ARRAY_SIZE, fundCodes.size()));
                model.append("fundCodes", subList);
                model.append(FundNoticeField.F_CODE, String.join(",", subList));
                // 如果为类型匹配，则直接插入
                if("2".equals(matchType)){
                    model.append("_id", UUID.randomUUID().toString().replace("-",""));
                    app.getCfhMongodbWrite().getMongoserver()
                            .getDatabase(CFHMongodbConstant.DB_TT_FUND_CFH)
                            .getCollection(CFHMongodbConstant.TB_CFH_PRODUCT_READ_HQ)
                            .insertOne(model);
                    modifyCount += 1;
                    continue;
                }
                // upsert构造条件
                BasicDBObject query = new BasicDBObject(HqCommonField.EID, model.getString(HqCommonField.EID));
                BasicDBObject update = new BasicDBObject("$set", model);
                UpdateResult result = app.getCfhMongodbWrite().getMongoserver()
                        .getDatabase(CFHMongodbConstant.DB_TT_FUND_CFH)
                        .getCollection(CFHMongodbConstant.TB_CFH_PRODUCT_READ_HQ)
                        .updateOne(query, update, new UpdateOptions().upsert(true));
                // 统计修改数，如果执行插入逻辑，则此值为0，所以日志可能会出现插入后更新数量为0的情况
                modifyCount += result.getModifiedCount();
            }
        }
        return modifyCount;
    }

    /**
     * 反向读取TB_CFHProductReadHq表，验证数据是否过期并删除
     * <AUTHOR>
     * @date 2023/4/10 14:55
     * @param nowDate 当前日期
     * @return int
     */
    private int clearProductReadHq(Date nowDate){
        // 查询公募、私募公告的EID
        BasicDBObject noticeFields = new BasicDBObject()
                .append(HqCommonField.EID,1);
        BasicDBObject noticeQuery = new BasicDBObject()
                .append(HqCommonField.BEGIN_TIME, new BasicDBObject("$lt", nowDate))
                .append(HqCommonField.END_TIME, new BasicDBObject("$gte", nowDate));
        List<Document> noticeList = app.getHqMongodbRead().query(HqMongodbConstant.DB_FUND_ANNOUNCEMENT,
                HqMongodbConstant.TB_FUND_NOTICE, noticeQuery, noticeFields, null, 0, Document.class);
        List<Document> privateNoticeList = app.getHqMongodbRead().query(HqMongodbConstant.DB_FUND_ANNOUNCEMENT,
                HqMongodbConstant.TB_FUND_PRIVATE_NOTICE, noticeQuery, noticeFields, null, 0, Document.class);
        noticeList.addAll(privateNoticeList);
        List<Document> filterNoticeList = noticeList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getString(HqCommonField.EID)))
                .collect(Collectors.toList());

        // 筛选出EID字段
        List<String> noticeEIDs = filterNoticeList.stream()
                .map(item -> item.getString(HqCommonField.EID))
                .collect(Collectors.toList());

        // 筛选出财富号公告表中EID不存在于源数据表中或已经过期的公告
        return cfhMongodbDao.deleteByCondition(noticeEIDs, nowDate);
    }
}
