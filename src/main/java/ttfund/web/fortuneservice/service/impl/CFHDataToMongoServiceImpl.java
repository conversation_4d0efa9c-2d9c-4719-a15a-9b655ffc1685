package ttfund.web.fortuneservice.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import ttfund.web.fortuneservice.config.CommonConfig;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.dao.CfhSqlserverDao;
import ttfund.web.fortuneservice.model.dto.CFHInfoDto;
import ttfund.web.fortuneservice.model.dto.CFHLabelDto;
import ttfund.web.fortuneservice.model.dto.CFHLabelMongoDto;
import ttfund.web.fortuneservice.service.CFHDataToMongoService;
import ttfund.web.fortuneservice.utils.CommonUtil;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className CFHDataToMongoServiceImpl
 * @date 2023/4/14 15:34
 */
@Service
@Slf4j
public class CFHDataToMongoServiceImpl implements CFHDataToMongoService {

    @Resource
    private CfhSqlserverDao sqlServer;

    @Resource
    private CommonConfig commonConfig;

    @Resource
    private CfhMongodbDao cfhMongodbDao;

    @Override
    public void handCFHData() {
        //获取所有已经认证的财富号信息（包含status=0的）
        List<CFHInfoDto> cfhInfoList = sqlServer.selectAllCFHInfo();
        //过滤无财富号id的并排序
        cfhInfoList = cfhInfoList.stream()
                .filter(o -> StringUtils.isNotEmpty(o.getCFHID()))
                .sorted(Comparator.comparing(CFHInfoDto::getUpDataTime))
                .collect(Collectors.toMap(
                        CFHInfoDto::getCFHID,
                        Function.identity(),
                        (o1, o2) -> o2
                ))
                .values()
                .stream()
                .collect(Collectors.toList());
        //获取所有标签信息列表
        List<CFHLabelDto> cfhLabelList = sqlServer.selectAllLabel();

        for (CFHInfoDto cfhInfo : cfhInfoList) {
            cfhInfo.setHeaderImgPath(CommonUtil.replaceHttp(StringUtils.isNotEmpty(cfhInfo.getHeaderImgPath()) ? cfhInfo.getHeaderImgPath() : commonConfig.defaultHeaderImg));
            cfhInfo.setID(StringUtils.isNotEmpty(cfhInfo.getCFHID()) ? cfhInfo.getCFHID() : cfhInfo.getCommpanyCode());
            List<CFHLabelMongoDto> labelList = cfhLabelList.stream()
                    .filter(o -> StringUtils.isNotEmpty(o.getCFHID()))
                    .filter(o -> o.getCFHID().equals(cfhInfo.getCFHID()))
                    .map(o -> new CFHLabelMongoDto(o.getTitle(), o.getUrlType(), o.getLinkUrl(), o.getStartTime(), o.getEndTime(), o.getShowPosition()))
                    .collect(Collectors.toList());
            cfhInfo.setLabelInfoList(labelList);
            cfhInfo.setPurview(cfhInfo.getPurview() != null ? cfhInfo.getPurview() : 0);
        }
        int result = cfhMongodbDao.saveOrUpdateCFHList(cfhInfoList);
        log.info("财富号java服务：同步财富号信息到mongoDB，执行结果：{}", result);
    }
}
