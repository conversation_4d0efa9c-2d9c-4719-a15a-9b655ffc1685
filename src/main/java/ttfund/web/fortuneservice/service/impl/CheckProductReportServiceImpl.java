package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.CommonHelper;
import com.ttfund.web.base.helper.DateHelper;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.config.CommonConfig;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.constant.ProductCheckTypeEnum;
import ttfund.web.fortuneservice.constant.ReportTypeEnum;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.dao.VerticaDao;
import ttfund.web.fortuneservice.manager.DongDongManager;
import ttfund.web.fortuneservice.manager.QSApiManager;
import ttfund.web.fortuneservice.model.dto.*;
import ttfund.web.fortuneservice.service.CheckProductReportService;
import ttfund.web.fortuneservice.utils.CommonUtil;
import ttfund.web.fortuneservice.utils.ProductCheckUtil;
import ttfund.web.fortuneservice.utils.TimeUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static ttfund.web.fortuneservice.constant.CFHMongodbConstant.RegularInspectionReportField.REFRESH_THE_MARKER;

@Service
public class CheckProductReportServiceImpl implements CheckProductReportService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CheckProductReportServiceImpl.class);

    @Resource
    private CfhMongodbDao cfhMongodbDao;

    @Resource
    private VerticaDao verticaDao;

    @Resource
    private CommonConfig commonConfig;

    @Resource
    private DongDongManager dongDongManager;

    @Autowired
    private QSApiManager qsApiManager;

    /**
     * 高端理财报告披露检查日期配置
     */
    @Value("#{${"+CommonConstant.PRODUCT_REPORT_CHECK_CONFIG +":}}")
    private Map<String, Map<String, Object>> reportCheckConfigMap;

    public static final String FIELD_MONTH = "month";

    public static final String FIELD_TRADE_DAY = "tradeDay";

    public static final String FIELD_SORT = "sort";

    public static final String FIELD_REPORT_NAME = "reportName";

    public static final String FIELD_CHECK_REPORT_NAME = "checkReportName";

    public static final String FIELD_YEAR_ADD = "yearAdd";

    public static final String FIELD_NEED_FUND_SIZE = "needFundSize";
    public static final String FIELD_LAST_REPORT_ID = "lastId";

    @Override
    public void checkReport(String param) {
        XxlJobLogger.log("start to execute CheckProductReportServiceImpl......");
        Date nowDate = null;
        if (!StringUtils.isEmpty(param)) {
            nowDate = DateHelper.stringToDate2(param, DateHelper.FORMAT_YYYY_MM_DD);
        }
        Calendar instance = Calendar.getInstance();
        if (nowDate != null) {
            instance.setTime(nowDate);
        } else {
            nowDate = DateHelper.getNowDate();
        }
        int currentMonth = CommonUtil.getCurrentMonth(instance);
        int year = instance.get(Calendar.YEAR);
        XxlJobLogger.log("当前执行时间为：{}", DateHelper.dateToStr(nowDate, DateHelper.FORMAT_YYYY_MM_DD));
        //只有配置中存在的月份才需要检查
        List<Integer> monthCollect = reportCheckConfigMap.values().stream().map(map->(int) map.get(FIELD_MONTH)).distinct().collect(Collectors.toList());
        if (!monthCollect.contains(currentMonth)) {
            XxlJobLogger.log("月份不满足报告检测需执行的时间，不需要执行报告检查");
            return;
        }
        monthCollect.clear();
        checkDayAndHandleReport(nowDate, instance, year, currentMonth);
    }

    private void checkDayAndHandleReport(Date nowDate, Calendar instance, int year, int currentMonth) {
        //1、计算当前日期在当月所处于交易日的排序
        List<DaysInfoMongoDto> daysInfoList = cfhMongodbDao.getDaysInfoByTimeRange(CommonUtil.getFirstDayOfMonth(instance), CommonUtil.getLastDayOfMonth(instance));
        if(!ProductCheckUtil.checkIsTradeDay(nowDate, daysInfoList)) {
            XxlJobLogger.log("当前时间为{}，非交易日，不需要执行报告检查", DateHelper.dateToStr(nowDate, DateHelper.FORMAT_YYYY_MM_DD));
            return;
        }
        int dayNumOfMonth = ProductCheckUtil.calculateTradeDayNumber(CommonUtil.getCurrentBelongDay(nowDate), daysInfoList, false);
        int dayNumOfMonthReversed = ProductCheckUtil.calculateTradeDayNumber(CommonUtil.getCurrentBelongDay(nowDate), daysInfoList, true);
        Set<Integer> tradeDaySet = new HashSet<>();
        Set<Integer> tradeDayReversedSet = new HashSet<>();
        reportCheckConfigMap.values().forEach(item -> {
            int tradeDay = (int) item.get(FIELD_TRADE_DAY);
            if (Boolean.TRUE.equals(item.get(FIELD_SORT))) {
                tradeDaySet.add(tradeDay);
            } else {
                tradeDayReversedSet.add(tradeDay);
            }
        });
        XxlJobLogger.log("当前时间为{}，是当月的第{}个交易日，倒数第{}个交易日",  DateHelper.dateToStr(nowDate, DateHelper.FORMAT_YYYY_MM_DD)
                , dayNumOfMonth, dayNumOfMonthReversed);
        if (!tradeDaySet.contains(dayNumOfMonth) && !tradeDayReversedSet.contains(dayNumOfMonthReversed)) {
            XxlJobLogger.log("交易日不满足条件,不需要执行报告检查");
            return;
        }
        tradeDaySet.clear();
        tradeDayReversedSet.clear();
        //满足配置的交易日 执行报告处理
        handleCheckReport(nowDate, currentMonth, year, dayNumOfMonth, dayNumOfMonthReversed);
    }

    private void handleCheckReport(Date nowDate, int currentMonth, int year, long dayNumOfMonth, long dayNumOfMonthReversed) {
        //1、获取vertica FUND.SYNC_MFINFO_ALL_BASIC_SYN中产品的信息
        List<FundHighLevelInfo> fundInfoList = verticaDao.getFundCodeType();
        if (CollectionUtils.isEmpty(fundInfoList)) {
            XxlJobLogger.log("获取高端理财产品列表失败");
            return;
        }

        //2、获取所有产品的名称信息
        Map<String, String> productNameMap = fundInfoList.stream().collect(Collectors.toMap(FundHighLevelInfo::getFundCode, FundHighLevelInfo::getFundName, (v1,v2)->v1));

        //3、区分私募基金、臻享产品
        List<List<String>> codeList = filterCodeByIsPvt(fundInfoList);
        List<String> siMuCodeList = codeList.get(0);
        List<String> seniorCodeList = codeList.get(1);
        codeList.clear();

        //4、获取关停基金名单
        List<String> closeFundList = verticaDao.getCloseFund(CommonUtil.getCurrentBelongDay(nowDate));

        //4.1 获取持仓状态基金列表
        List<String> holdFundList = qsApiManager.getHoldFundList(fundInfoList.stream().map(FundHighLevelInfo::getFundCode).distinct().collect(Collectors.toList()));

        //5、读历史报告 TB_ProductRead
        Map<String, List<String>> reportHistoryMap = cfhMongodbDao.getReportHistoryIdList();

        //6、执行报告检查
        handleReport( nowDate, currentMonth, year, productNameMap ,reportHistoryMap, siMuCodeList, seniorCodeList, closeFundList, dayNumOfMonth, dayNumOfMonthReversed,holdFundList,fundInfoList);
    }

    /**
     * 处理披露报告
     *
     * @param reportHistoryMap      历史提交报告
     * @param siMuCodeList          私募基金列表
     * @param seniorCodeList        高端臻享列表
     * @param dayNumOfMonth         当前为第几个交易日
     * @param dayNumOfMonthReversed 倒数第几个交易日
     * @param holdFundList
     * @param fundInfoList
     */
    private void handleReport(Date nowDate, int currentMonth , int year, Map<String, String> productNameMap,
                              Map<String, List<String>> reportHistoryMap, List<String> siMuCodeList,
                              List<String> seniorCodeList, List<String> closeFundList,
                              long dayNumOfMonth, long dayNumOfMonthReversed, List<String> holdFundList, List<FundHighLevelInfo> fundInfoList) {

        List<RegularInspectionReportContentDto> reportContentSum = new ArrayList<>();
        List<RegularInspectionReportDto> checkRecordSum = new ArrayList<>();
        List<FundSizeDto> fundSizeList = null;
        Map<String, BigDecimal> fundSizeDtoMap = null;
        for (Map.Entry<String, Map<String, Object>> checkConfigDtoEntry : reportCheckConfigMap.entrySet()) {
            ReportCheckConfigDto configDto = convertMapToConfigDto(checkConfigDtoEntry.getValue());
            if (configDto.getMonth() == currentMonth
                    && ((configDto.isSort() && dayNumOfMonth == configDto.getTradeDay())
                    || !configDto.isSort() && dayNumOfMonthReversed == configDto.getTradeDay())) {
                //是否需要基金规模
                if (configDto.isNeedFundSize() && fundSizeList == null) {
                    fundSizeList = verticaDao.getFundSizeInfo(siMuCodeList);
                    fundSizeDtoMap = fundSizeList.stream().collect(Collectors.toMap(FundSizeDto::getFundCode, FundSizeDto::getFundSize));
                }
                int reportType = getReportType(configDto.getCheckReportName());
                if (reportType== ReportTypeEnum.SIMU_JC.getType() || reportType == ReportTypeEnum.SIMU_YQ.getType()) {
                    //检查私募基金报告
                    String simuRecordId = CommonHelper.getGuid();
                    List<RegularInspectionReportContentDto> regularInspectionReportContentDtos = checkProductReport(configDto, siMuCodeList, productNameMap, fundSizeDtoMap, reportHistoryMap,
                            closeFundList, simuRecordId, nowDate, year, holdFundList, fundInfoList);
                    reportContentSum.addAll(regularInspectionReportContentDtos);
                    checkRecordSum.add(produceCheckRecord(simuRecordId, configDto, reportType, nowDate, year));

                    //私募逾期报告检查连续两个季度的
                    Map<String, Object> lastConfig = reportCheckConfigMap.get(configDto.getLastId());
                    if (reportType == ReportTypeEnum.SIMU_YQ.getType()) {
                        if (CollectionUtils.isEmpty(lastConfig)) {
                            LOGGER.info("未配置连续两个季度的私募逾期报告检查，检查lastId：{}", JSON.toJSONString(configDto));
                            XxlJobLogger.log("未配置连续两个季度的私募逾期报告检查");
                            continue;
                        }
                        String simuLXRecordId = CommonHelper.getGuid();
                        Date lastThreeMouth = DateUtils.addMonths(nowDate, -3);
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(lastThreeMouth);
                        int lastThreeMouthYear = calendar.get(Calendar.YEAR);
                        ReportCheckConfigDto lastConfigDto = convertMapToConfigDto(lastConfig);
                        List<RegularInspectionReportContentDto> regularLXInspectionReportContentDtos = checkProductReport(lastConfigDto, siMuCodeList, productNameMap, fundSizeDtoMap, reportHistoryMap,
                                closeFundList, simuLXRecordId, lastThreeMouth, lastThreeMouthYear, holdFundList, fundInfoList);
                        Set<String> regularInspectionReportCodes = regularInspectionReportContentDtos.stream()
                                .map(RegularInspectionReportContentDto::getProductCode)
                                .collect(Collectors.toSet());
                        regularLXInspectionReportContentDtos = regularLXInspectionReportContentDtos.stream().filter(dto -> regularInspectionReportCodes.contains(dto.getProductCode())).collect(Collectors.toList());
                        reportContentSum.addAll(regularLXInspectionReportContentDtos);
                        checkRecordSum.add(produceCheckLXRecord(simuLXRecordId, configDto, reportType, nowDate, year));
                    }
                } else {
                    //检查臻享基金报告
                    String seniorRecordId = CommonHelper.getGuid();
                    reportContentSum.addAll(checkProductReport(configDto, seniorCodeList, productNameMap, fundSizeDtoMap, reportHistoryMap,
                            closeFundList, seniorRecordId, nowDate, year, holdFundList, fundInfoList));
                    checkRecordSum.add(produceCheckRecord(seniorRecordId, configDto, reportType, nowDate, year));
                }
            }
        }
        if (CollectionUtils.isEmpty(reportContentSum) || CollectionUtils.isEmpty(checkRecordSum)) {
            XxlJobLogger.log("当前时间为{},为当月的第{}个交易日，倒数第{}个交易日，不需要执行报告检查", DateHelper.dateToStr(nowDate, DateHelper.FORMAT_YYYY_MM_DD)
                    , dayNumOfMonth, dayNumOfMonthReversed);
            return;
        }
        //将检查结果更新到mongodb
        updateInMongodb(reportContentSum, checkRecordSum);
        //调清算告警接口
//        List<String> reportNameList = checkRecordSum.stream().map(RegularInspectionReportDto::getReportName).collect(Collectors.toList());
//        boolean result = qsApiManager.sendMessage("【基金数据】高端理财有新报告", reportNameList.toString());
//        XxlJobLogger.log("调用清算告警接口结果：" + result);
//        LOGGER.info("调用清算告警接口结果：【{}】", result);
    }

    /**
     * 生成检查文件实体类
     *
     * @param reportId     报告唯一id
     * @param configDto    检查配置
     * @param reportType   报告类型
     * @return RegularInspectionReportDto
     */
    private RegularInspectionReportDto produceCheckLXRecord(String reportId, ReportCheckConfigDto configDto, int reportType, Date nowDate, int year) {
        RegularInspectionReportDto regularInspectionReportDto = new RegularInspectionReportDto();
        regularInspectionReportDto.setReportName(produceReportName(configDto.getCheckReportName(), year, configDto.getYearAdd()) + "_LX");
        regularInspectionReportDto.setReportId(reportId);
        regularInspectionReportDto.setCheckTime(nowDate);
        regularInspectionReportDto.setReportType(ReportTypeEnum.SIMU_YQ2.getType());
        return regularInspectionReportDto;
    }
    /**
     * 将apollo配置转化为配置实体类
     * @param map map
     * @return ReportCheckConfigDto
     */
    private ReportCheckConfigDto convertMapToConfigDto(Map<String, Object> map) {
        ReportCheckConfigDto reportCheckConfigDto = new ReportCheckConfigDto();
        reportCheckConfigDto.setMonth((int) map.get(FIELD_MONTH));
        reportCheckConfigDto.setTradeDay((int) map.get(FIELD_TRADE_DAY));
        reportCheckConfigDto.setSort((Boolean) map.get(FIELD_SORT));
        reportCheckConfigDto.setReportName((String) map.get(FIELD_REPORT_NAME));
        reportCheckConfigDto.setCheckReportName((String) map.get(FIELD_CHECK_REPORT_NAME));
        reportCheckConfigDto.setYearAdd((int) map.get(FIELD_YEAR_ADD));
        reportCheckConfigDto.setNeedFundSize((Boolean) map.get(FIELD_NEED_FUND_SIZE));
        reportCheckConfigDto.setLastId((String) map.get(FIELD_LAST_REPORT_ID));
        return reportCheckConfigDto;
    }

    /**
     * 将检查的具体内容和结果更新到mongodb
     *
     * @param reportContentSum 检查具体内容
     * @param checkRecordSum   检查结果
     */
    private void updateInMongodb(List<RegularInspectionReportContentDto> reportContentSum, List<RegularInspectionReportDto> checkRecordSum) {
        //将记录写入库中
        int checkRecordSize = cfhMongodbDao.batchSaveRegularReportList(checkRecordSum);
        int reportContentSize = cfhMongodbDao.batchSaveRegularReportContentList(reportContentSum);
        LOGGER.info("本次定期检查，新增{}个检查文件，新增{}条检查记录", checkRecordSize, reportContentSize);
        XxlJobLogger.log("本次定期检查，新增{}个检查文件，新增{}条检查记录", checkRecordSize, reportContentSize);
    }

    /**
     * 发送咚咚消息提醒
     *
     * @param checkRecordSum 检查结果
     */
    private void dongDongNotice(List<RegularInspectionReportDto> checkRecordSum) {
        if (CollectionUtils.isEmpty(checkRecordSum)) {
            return;
        }
        List<String> reportNameList = checkRecordSum.stream().map(RegularInspectionReportDto::getReportName).collect(Collectors.toList());
        int sendSum = dongDongManager.sendDongDongMessage(commonConfig.inspectionReportReceiving,"有新的报告需要查看", reportNameList.toString());
        LOGGER.info("共成功推送{}条咚咚消息,报告名称为：{}", sendSum, reportNameList);
        XxlJobLogger.log("共成功推送{}条咚咚消息,报告名称为：{}", sendSum, reportNameList);
    }

    /**
     * 根据isPvt字段过滤基金代码
     *
     * @param fundInfoList 基金信息
     * @return List<List < String>>
     */
    private List<List<String>> filterCodeByIsPvt(List<FundHighLevelInfo> fundInfoList) {
        List<List<String>> result = new ArrayList<>(2);
        List<String> siMuCodeList = new ArrayList<>();
        List<String> seniorCodeList = new ArrayList<>();
        for (FundHighLevelInfo fundHighLevelInfo : fundInfoList) {
            if (fundHighLevelInfo == null || StringUtils.isEmpty(fundHighLevelInfo.getFundCode())) {
                continue;
            }
            Integer isPvt = fundHighLevelInfo.getIsPvt();
            if (CommonConstant.PRIVATE_PRODUCT.equals(isPvt)) {
                siMuCodeList.add(fundHighLevelInfo.getFundCode());
            } else {
                seniorCodeList.add(fundHighLevelInfo.getFundCode());
            }
        }
        result.add(siMuCodeList);
        result.add(seniorCodeList);
        return result;
    }

    /**
     * 生成检查文件实体类
     *
     * @param reportId     报告唯一id
     * @param configDto    检查配置
     * @param reportType   报告类型
     * @return RegularInspectionReportDto
     */
    private RegularInspectionReportDto produceCheckRecord(String reportId, ReportCheckConfigDto configDto, int reportType, Date nowDate, int year) {
        RegularInspectionReportDto regularInspectionReportDto = new RegularInspectionReportDto();
        regularInspectionReportDto.setReportName(produceReportName(configDto.getCheckReportName(), year, configDto.getYearAdd()));
        regularInspectionReportDto.setReportId(reportId);
        regularInspectionReportDto.setCheckTime(nowDate);
        regularInspectionReportDto.setReportType(reportType);
        return regularInspectionReportDto;
    }

    /**
     * 判断生成报告的类型
     * @param checkReportName 生成的检查报告名称
     * @return int   报告类型  (1-私募逾期报告 /2-私募检查报告 /3-资管产品检查报告 /4-资管产品逾期报告)
     */
    private int getReportType(String checkReportName) {
        String checkReportNameTemp = checkReportName.toUpperCase();
        int result = 0;
        if (checkReportNameTemp.startsWith("JC")) {
            if (checkReportNameTemp.endsWith("SIMU")) {
                result = ReportTypeEnum.SIMU_JC.getType();
            } else if (checkReportNameTemp.endsWith("ZIGUAN")) {
                result = ReportTypeEnum.ZIGUAN_JC.getType();
            }
        } else if (checkReportNameTemp.startsWith("YQ")) {
            if (checkReportNameTemp.endsWith("SIMU")) {
                result = ReportTypeEnum.SIMU_YQ.getType();
            } else if (checkReportNameTemp.endsWith("ZIGUAN")) {
                result = ReportTypeEnum.ZIGUAN_YQ.getType();
            }
        }
        return result;
    }

    /**
     * 判断检查类型 配置中含Q的属于季报检查 不含Q为年报
     * @param reportName 需要的报告名称
     * @return int 检查类型 1：季报 / 2：净值 / 3：年报
     */
    private int getCheckType(String reportName) {
        String reportNameTemp = reportName.toUpperCase();
        return reportNameTemp.contains("Q")? ProductCheckTypeEnum.QUARTER.getType(): ProductCheckTypeEnum.YEAR.getType();
    }

    /**
     * 检查产品提交的报告生成情况
     *
     * @param codeList         基金代码列表
     * @param productNameMap   产品名称map
     * @param fundSizeDtoMap   产品规模map
     * @param reportHistoryMap 现在存在的产品报告
     * @param recordId         记录所属报告唯一id
     * @param holdFundList
     * @param fundInfoList
     * @return 检查内容列表
     */
    private List<RegularInspectionReportContentDto> checkProductReport(ReportCheckConfigDto configDto, List<String> codeList, Map<String, String> productNameMap,
                                                                       Map<String, BigDecimal> fundSizeDtoMap, Map<String, List<String>> reportHistoryMap,
                                                                       List<String> closeFundList, String recordId, Date nowDate, int year, List<String> holdFundList,
                                                                       List<FundHighLevelInfo> fundInfoList) {
        List<RegularInspectionReportContentDto> result = new ArrayList<>();
        String reportName = configDto.getReportName();
        int yearAdd = configDto.getYearAdd();

        Map<String, FundHighLevelInfo> fundInfoMap = fundInfoList.stream().collect(Collectors.toMap(FundHighLevelInfo::getFundCode, Function.identity(), (v1, v2) -> v1));
        for (String code : codeList) {
            String needReportName = produceNeedReportName(code, reportName, year, yearAdd);
            List<String> reportList = reportHistoryMap.get(code);
            if (needReportName.endsWith("Q2")) {
                //Q2校验code_yearQ2或code_yearH1
                if (!CollectionUtils.isEmpty(reportList) && (reportList.contains(needReportName) || reportList.contains(getH1Name(needReportName)))) {
                    continue;
                }
            }else if (!CollectionUtils.isEmpty(reportList) && reportList.contains(needReportName)) {
                continue;
            }
            RegularInspectionReportContentDto contentDto = new RegularInspectionReportContentDto();
            contentDto.setProductCode(code);
            contentDto.setProductName(productNameMap.get(code));
            contentDto.setReportContentId(CommonHelper.getGuid());
            contentDto.setMissingInformationType(getCheckType(reportName));
            contentDto.setReportId(recordId);
            contentDto.setCreateTime(nowDate);
            //判断是否关停
            if (closeFundList.contains(code)) {
                contentDto.setIsClose(true);
            }
            //判断是否持仓
            if (holdFundList.contains(code)) {
                contentDto.setIsHold(true);
            }
            if (configDto.isNeedFundSize() && !CollectionUtils.isEmpty(fundSizeDtoMap)) {
                contentDto.setFundSize(fundSizeDtoMap.get(code) != null? fundSizeDtoMap.get(code).doubleValue(): null);
            }
            FundHighLevelInfo fundInfo = fundInfoMap.get(code);
            contentDto.setManager(fundInfo == null ? null : fundInfo.getManager());
            contentDto.setStabilshDate(fundInfo == null ? null : fundInfo.getEstablishmentTime());
            result.add(contentDto);
        }
        return result;
    }

    /**
     * 根据时间生成产品应该生成的报告名称
     *
     * @param code         产品id
     * @param reportCase   需要检查的报告名称模板 例如%s_%sQ1
     * @param year         年份
     * @param yearAdd      年份偏移量
     * @return String
     */
    private String produceNeedReportName(String code, String reportCase, int year, int yearAdd) {
        int yearNew = year + yearAdd;
        return StringUtils.upperCase(String.format(reportCase, code, yearNew));
    }

    /**
     * 生成报告名称
     *
     * @param nameCase         报告名称
     * @param year             年份
     * @param yearAdd          年份偏移量
     * @return String
     */
    private String produceReportName(String nameCase, int year, int yearAdd) {
        int yearNew = year + yearAdd;
        return String.format(nameCase, yearNew);
    }

    public static String getH1Name(String name) {
        int lastIndex = name.lastIndexOf("Q2");
        if (lastIndex != -1) {
            name = name.substring(0, lastIndex) + "H1" + name.substring(lastIndex + 2);
        }
        return name;
    }

    /**
     * 检查逾期报告，私募和资管两类
     */
    @Override
    public void checkReportPush() {
        Date nowDate = DateHelper.getNowDate();
        Date breakPoint = cfhMongodbDao.getBreakPoint(REFRESH_THE_MARKER, CommonConstant.MIN_BREAK_POINT);
        Date checkTime = cfhMongodbDao.getMoudleCache(REFRESH_THE_MARKER);
        if (checkTime == null || checkTime.before(breakPoint) || nowDate.before(checkTime)) {
            return;
        }
//        Date checkTime = DateHelper.stringToDate2("2024-04-15", DateHelper.FORMAT_YYYY_MM_DD);//检查q1
//        Date checkTime = DateHelper.stringToDate2("2023-07-17", DateHelper.FORMAT_YYYY_MM_DD);//检查q2
//        Date checkTime = DateHelper.stringToDate2("2023-10-23", DateHelper.FORMAT_YYYY_MM_DD);//检查q3
//        Date checkTime = DateHelper.stringToDate2("2024-01-16", DateHelper.FORMAT_YYYY_MM_DD);//检查q4
//        nowDate = checkTime;
        XxlJobLogger.log("手动检查开始，检查时间：" + DateHelper.dateToStr(checkTime, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS));
        LOGGER.info("手动检查开始，检查时间：" + DateHelper.dateToStr(checkTime, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS));

        //立即检查一下YQ_simu
        //1、获取vertica FUND.SYNC_MFINFO_ALL_BASIC_SYN中产品的信息
        List<FundHighLevelInfo> fundInfoList = verticaDao.getFundCodeType();
        if (CollectionUtils.isEmpty(fundInfoList)) {
            XxlJobLogger.log("获取高端理财产品列表失败");
            LOGGER.info("获取高端理财产品列表失败");
            return;
        }
        //2、获取所有产品的名称信息
        Map<String, String> productNameMap = fundInfoList.stream().collect(Collectors.toMap(FundHighLevelInfo::getFundCode, FundHighLevelInfo::getFundName, (v1,v2)->v1));

        //3、区分私募基金、臻享产品
        List<List<String>> codeList = filterCodeByIsPvt(fundInfoList);
        List<String> siMuCodeList = codeList.get(0);//私募
        List<String> seniorCodeList = codeList.get(1);//资管
        codeList.clear();

        //4、获取关停基金名单
        List<String> closeFundList = verticaDao.getCloseFund(CommonUtil.getCurrentBelongDay(nowDate));

        //4.1 获取持仓状态基金列表
        List<String> holdFundList = qsApiManager.getHoldFundList(fundInfoList.stream().map(FundHighLevelInfo::getFundCode).distinct().collect(Collectors.toList()));

        //5、读历史报告 TB_ProductRead
        Map<String, List<String>> reportHistoryMap = cfhMongodbDao.getReportHistoryIdList();

        //6、执行报告检查
        List<RegularInspectionReportContentDto> reportContentSum = new ArrayList<>();
        List<RegularInspectionReportDto> checkRecordSum = new ArrayList<>();
        List<FundSizeDto> fundSizeList = null;
        Map<String, BigDecimal> fundSizeDtoMap = null;

        List<ReportCheckConfigDto> checkList = new LinkedList<>();
        Date date = DateUtils.addMonths(nowDate, -3);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int year = nowDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().getYear();
        int quarter = (date.getMonth() / 3) + 1;
        ReportCheckConfigDto configDtoSM = convertMapToConfigDto(reportCheckConfigMap.get(String.format("YQ_simu_Q%s", quarter)));
        ReportCheckConfigDto configDtoZG = convertMapToConfigDto(reportCheckConfigMap.get(String.format("YQ_ziguan_Q%s", quarter)));

        checkList.add(configDtoSM);
        checkList.add(configDtoZG);

        for (ReportCheckConfigDto configDto : checkList) {
            //是否需要基金规模
            if (configDto.isNeedFundSize() && fundSizeList == null) {
                fundSizeList = verticaDao.getFundSizeInfo(siMuCodeList);
                fundSizeDtoMap = fundSizeList.stream().collect(Collectors.toMap(FundSizeDto::getFundCode, FundSizeDto::getFundSize));
            }
            int reportType = getReportType(configDto.getCheckReportName());
            if (reportType== ReportTypeEnum.SIMU_JC.getType() || reportType == ReportTypeEnum.SIMU_YQ.getType()) {
                //检查私募基金报告
                String simuRecordId = CommonHelper.getGuid();
                List<RegularInspectionReportContentDto> regularInspectionReportContentDtos = checkProductReport(configDto, siMuCodeList, productNameMap, fundSizeDtoMap, reportHistoryMap,
                        closeFundList, simuRecordId, nowDate, year, holdFundList, fundInfoList);
                reportContentSum.addAll(regularInspectionReportContentDtos);
                checkRecordSum.add(produceCheckRecord(simuRecordId, configDto, reportType, checkTime, year));

                //私募逾期报告检查连续两个季度的
                Map<String, Object> lastConfig = reportCheckConfigMap.get(configDto.getLastId());
                if (reportType == ReportTypeEnum.SIMU_YQ.getType()) {
                    if (CollectionUtils.isEmpty(lastConfig)) {
                        LOGGER.info("未配置连续两个季度的私募逾期报告检查，检查lastId：{}", JSON.toJSONString(configDto));
                        XxlJobLogger.log("未配置连续两个季度的私募逾期报告检查");
                        continue;
                    }
                    String simuLXRecordId = CommonHelper.getGuid();
                    Date lastThreeMouth = DateUtils.addMonths(nowDate, -3);
//                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(lastThreeMouth);
                    int lastThreeMouthYear = calendar.get(Calendar.YEAR);
                    ReportCheckConfigDto lastConfigDto = convertMapToConfigDto(lastConfig);
                    List<RegularInspectionReportContentDto> regularLXInspectionReportContentDtos = checkProductReport(lastConfigDto, siMuCodeList, productNameMap, fundSizeDtoMap, reportHistoryMap,
                            closeFundList, simuLXRecordId, lastThreeMouth, lastThreeMouthYear, holdFundList, fundInfoList);
                    Set<String> regularInspectionReportCodes = regularInspectionReportContentDtos.stream()
                            .map(RegularInspectionReportContentDto::getProductCode)
                            .collect(Collectors.toSet());
                    regularLXInspectionReportContentDtos = regularLXInspectionReportContentDtos.stream().filter(dto -> regularInspectionReportCodes.contains(dto.getProductCode())).collect(Collectors.toList());
                    reportContentSum.addAll(regularLXInspectionReportContentDtos);
                    checkRecordSum.add(produceCheckLXRecord(simuLXRecordId, configDto, reportType, nowDate, year));
                }
            } else {
                //检查臻享基金报告
                String seniorRecordId = CommonHelper.getGuid();
                reportContentSum.addAll(checkProductReport(configDto, seniorCodeList, productNameMap, fundSizeDtoMap, reportHistoryMap,
                        closeFundList, seniorRecordId, nowDate, year, holdFundList, fundInfoList));
                checkRecordSum.add(produceCheckRecord(seniorRecordId, configDto, reportType, checkTime, year));
            }
        }
        //将检查结果更新到mongodb
        updateInMongodb(reportContentSum, checkRecordSum);
        //调清算告警接口
//        List<String> reportNameList = checkRecordSum.stream().map(RegularInspectionReportDto::getReportName).collect(Collectors.toList());
//        boolean result = qsApiManager.sendMessage("【基金数据】高端理财有新报告", reportNameList.toString());
//        XxlJobLogger.log("调用清算告警接口结果：" + result);
//        LOGGER.info("调用清算告警接口结果：【{}】", result);
        //更新断点
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("time", DateHelper.dateToStr(nowDate, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS));
        cfhMongodbDao.upsertCFHGeneralData(REFRESH_THE_MARKER, Arrays.asList(jsonObject));
    }

    @Override
    public void productReportDataSync() {
        //查数据
        Date startTime = TimeUtil.getNowDate();
        Integer count = cfhMongodbDao.selectProductReportDataSync();
        Date endTime = TimeUtil.getNowDate();
        //调接口上报
        qsApiManager.taskLog(startTime, endTime, count);
    }
}
