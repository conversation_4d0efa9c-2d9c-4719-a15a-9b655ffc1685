package ttfund.web.fortuneservice.service.impl;

import com.ttfund.web.base.helper.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.constant.CFHMongodbConstant;
import ttfund.web.fortuneservice.constant.VerticaSqlConstant;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.dao.VerticaDao;
import ttfund.web.fortuneservice.model.dto.CFHTradeStatDto;
import ttfund.web.fortuneservice.model.dto.DaysInfoMongoDto;
import ttfund.web.fortuneservice.utils.ProductCheckUtil;
import ttfund.web.fortuneservice.utils.TimeUtil;

import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class CFHTradeStatServiceImpl{

    @Autowired
    private VerticaDao verticaDao;

    @Autowired
    private CfhMongodbDao cfhMongodbDao;

    public void CFHTradeStat() {
        Date nowDate = new Date();
        List<CFHTradeStatDto> list = verticaDao.selectSql(VerticaSqlConstant.CFH_TRADE_STAT, CFHTradeStatDto.class);
        List<DaysInfoMongoDto> daysInfoList = cfhMongodbDao.getDaysInfoByTimeRange(DateUtils.addDays(nowDate, -1), nowDate);
        if (ProductCheckUtil.checkIsTradeDay(DateUtils.addDays(nowDate, -1), daysInfoList) && CollectionUtils.isEmpty(list)) {
            log.info("当前时间为{}，上一天为交易日，但无数据，异常！", DateHelper.dateToStr(nowDate, DateHelper.FORMAT_YYYY_MM_DD));
            return;
        }
        if (CollectionUtils.isEmpty(list)) {
            log.info("当前时间为{}，无数据，不执行同步", DateHelper.dateToStr(nowDate, DateHelper.FORMAT_YYYY_MM_DD));
            return;
        }
        list.forEach(o -> o.set_id(o.getFUNDCODE() + TimeUtil.dateToStr(o.getCALCDAY(),TimeUtil.FORMAT_YYYYMD_H_MM_SS)));
        cfhMongodbDao.insertOrUpdate(list, CFHMongodbConstant.DB_CFH_TRADE, CFHMongodbConstant.TB_CFH_TRADE_STAT);
    }
}
