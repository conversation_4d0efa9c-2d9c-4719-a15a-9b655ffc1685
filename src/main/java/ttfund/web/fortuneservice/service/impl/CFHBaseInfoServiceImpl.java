package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSON;
import com.ttfund.web.base.helper.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.config.CommonConfig;
import ttfund.web.fortuneservice.constant.CFHBaseInfoStatusEnum;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.dao.CfhSqlserverDao;
import ttfund.web.fortuneservice.manager.YuYanApiManager;
import ttfund.web.fortuneservice.model.bo.CFHBaseInfoBo;
import ttfund.web.fortuneservice.model.dto.CFHBaseInfoDto;
import ttfund.web.fortuneservice.model.dto.CFHInfoDto;
import ttfund.web.fortuneservice.model.dto.CFHUser;
import ttfund.web.fortuneservice.service.CFHBaseInfoService;
import ttfund.web.fortuneservice.utils.CommonUtil;
import ttfund.web.fortuneservice.utils.TimeUtil;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className CFHBaseInfoServiceImpl
 * @date 2023/4/14 15:17
 */
@Service
@Slf4j
public class CFHBaseInfoServiceImpl implements CFHBaseInfoService {

    @Resource
    private CfhSqlserverDao sqlServer;

    @Resource
    private CommonConfig commonConfig;

    @Resource
    private YuYanApiManager yuYanApiManager;

    @Override
    public CFHBaseInfoBo getCFHBaseInfo(String message) {
        CFHBaseInfoBo result = null;
        if (StringUtils.isEmpty(message)) {
            return result;
        }
        result = JSON.parseObject(message, CFHBaseInfoBo.class);
        return result;
    }

    @Override
    public int handCFHBaseInfo(CFHBaseInfoBo cfhBaseInfo) {
        int result = 0;
        try {
            if (cfhBaseInfo == null || StringUtils.isEmpty(cfhBaseInfo.getAccountId())
                    || StringUtils.isEmpty(cfhBaseInfo.getName())) {
                log.info("财富号java服务，Kafka数据异常【{}】", JSON.toJSONString(cfhBaseInfo));
                return result;
            }
            //数据库实体
            CFHBaseInfoDto cfhInfo = CFHBaseInfoDto.builder()
                    .ID(CommonUtil.getGuId(32))
                    .RelatedUid(cfhBaseInfo.getRelatedUid())
                    .Status(cfhBaseInfo.getIsDeleted() != null && cfhBaseInfo.getIsDeleted() == 1 ? 0 : cfhBaseInfo.getPageState() != null ? cfhBaseInfo.getPageState() : 0)
                    //私募公司code用privatefundparam
                    .CommpanyCode(StringUtils.equals(cfhBaseInfo.getOrganizationType(), CommonConstant.PRIVATE_FUND_TYPE) ? cfhBaseInfo.getPrivateFundParam() : cfhBaseInfo.getFundCompanyParam())
                    .CommpanyName(cfhBaseInfo.getName())
                    .OrganizationType(cfhBaseInfo.getOrganizationType())
                    .CFHID(cfhBaseInfo.getAccountId())
                    .CFHName(cfhBaseInfo.getAccountName())
                    .HeaderImgPath(StringUtils.isNotEmpty(CommonUtil.replaceHttp(cfhBaseInfo.getPortrait())) ? CommonUtil.replaceHttp(cfhBaseInfo.getPortrait()) : commonConfig.defaultHeaderImg)
                    .Summary(cfhBaseInfo.getSummary())
                    .Slogans(StringUtils.isNotEmpty(cfhBaseInfo.getBanner()) ? cfhBaseInfo.getBanner().trim() : null)
                    .CreatTime(cfhBaseInfo.getCreateTime())
                    .UpDataTime(cfhBaseInfo.getUpdateTime())
                    .build();
            CFHInfoDto cfhInfoDto = new CFHInfoDto();
            cfhInfoDto.setID(cfhBaseInfo.getAccountId());
            cfhInfoDto.setCFHID(cfhBaseInfo.getAccountId());
            cfhInfoDto.setRelatedUid(cfhBaseInfo.getRelatedUid());

            //查这一条是否存在，存在即更新，不存在则插入
            CFHBaseInfoDto cfhInfoDB = sqlServer.selectByCFHId(cfhBaseInfo.getAccountId());
            boolean success = false;
            if (cfhInfoDB == null) {
                //新增
                success = sqlServer.insert(cfhInfo);
                insertOrUpdateCfhUser(cfhInfoDto);
                // 调用雨燕接口-生成财富号首页html页面
                int isPrivate = StringUtils.equals(cfhInfo.getOrganizationType(), CommonConstant.PRIVATE_FUND_TYPE) ? 1 : 0;
                boolean status = yuYanApiManager.generateNewPage(cfhInfo.getCFHID(), cfhInfo.getCFHName(), cfhInfo.getRelatedUid(), cfhInfo.getCommpanyCode(), isPrivate);
                if (!status) {
                    log.error("调用雨燕接口生成财富号首页失败：财富号ID-{}，财富号名称-{}，财富号通行证ID-{}", cfhInfo.getCFHID(), cfhInfo.getCFHName(), cfhInfo.getRelatedUid());
                }
            } else {
                //修改,要判断状态=10的不修改status、CommpanyName字段
                cfhInfo.setUpDataTime(DateHelper.getNowDate());
                if (cfhInfoDB.getStatus().equals(CFHBaseInfoStatusEnum.TEST.getCode())) {
                    cfhInfo.setStatus(cfhInfoDB.getStatus());
                    cfhInfo.setCommpanyName(cfhInfoDB.getCommpanyName());
                }
                success = sqlServer.updateById(cfhInfo);
                insertOrUpdateCfhUser(cfhInfoDto);
            }
            log.info("财富号java服务，Kafka消费完成【{}】", JSON.toJSONString(cfhBaseInfo));
            result = success ? 1 : result;
        } catch (Exception e) {
            //SMS
            log.error("财富号java服务，东财Kafka处理异常：", e.getMessage(), e);
        }
        return result;
    }

    public void insertOrUpdateCfhUser(CFHInfoDto cfhInfoDto) {
        CFHInfoDto data = sqlServer.getCfhUserById(cfhInfoDto.getID() + "_" + "admin");
        if (data == null) {
            sqlServer.insertCFHUser(cfhInfoDto);
        } else {
            sqlServer.updateCfhUser(cfhInfoDto);
        }
    }

    @Override
    public void syncCFHAdminUser(String cfhId) {
        //查所有财富号
        List<CFHInfoDto> cfhInfoDtos = sqlServer.selectAllCFHInfo();
        cfhInfoDtos = cfhInfoDtos.stream().filter(o -> StringUtils.isNotEmpty(o.getCFHID()) && StringUtils.isNotEmpty(o.getRelatedUid())).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(cfhId)) {
            cfhInfoDtos = cfhInfoDtos.stream().filter(o -> cfhId.equals(o.getCFHID())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(cfhInfoDtos)) {
            log.info("没有数据，请注意");
            return;
        }
        //将管理员信息同步到user表
        for (CFHInfoDto cfhInfoDto : cfhInfoDtos) {
            try {
                sqlServer.insertCFHUser(cfhInfoDto);
            } catch (DuplicateKeyException e) {
                log.info("财富号【{}】已存在，无需重复添加", JSON.toJSONString(cfhInfoDto));
            } catch (Exception e) {
                log.warn("财富号java服务，同步财富号管理员信息异常：", e.getMessage(), e);
            }
        }


    }
}
