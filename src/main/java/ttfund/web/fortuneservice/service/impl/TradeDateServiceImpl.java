package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.constant.CFHMongodbConstant;
import ttfund.web.fortuneservice.constant.VerticaSqlConstant;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.manager.OpenApiInnerManager;
import ttfund.web.fortuneservice.model.dto.CFHScoreDTO;
import ttfund.web.fortuneservice.model.dto.TradeDayDTO;
import ttfund.web.fortuneservice.service.TradeDateService;
import ttfund.web.fortuneservice.utils.TimeUtil;

import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class TradeDateServiceImpl implements TradeDateService {


    @Autowired
    private OpenApiInnerManager openApiInner;

    @Autowired
    private CfhMongodbDao cfhMongodbDao;

    @Override
    public void syncTradeDate(String eseqIdParam) {
        //查最新eseqid
        Long eseqId = cfhMongodbDao.selectNewUpdateTradeDate();
        if (StringUtils.isNotEmpty(eseqIdParam)) {
            try {
                eseqId = Long.valueOf(eseqIdParam);
            } catch (NumberFormatException e) {
            }
        }

        //查数据
        List<TradeDayDTO> cfhScoreList = openApiInner.getTradeDayBySeqId(eseqId);

        if (CollectionUtils.isEmpty(cfhScoreList)) {
            log.info("未获取到数据，不执行");
            return;
        }
        cfhScoreList.sort(Comparator.comparing(TradeDayDTO::getESEQID));
        cfhScoreList.forEach(model -> model.set_id(
                TimeUtil.dateToStr(model.getDATETIME(), TimeUtil.FORMAT_YYYYMD_H_MM_SS)
        ));
        cfhMongodbDao.insertOrUpdate(cfhScoreList, CFHMongodbConstant.DB_CFH_TRADE, CFHMongodbConstant.TB_TRADE_DATE);
    }
}
