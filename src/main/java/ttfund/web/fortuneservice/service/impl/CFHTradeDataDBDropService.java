package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSON;
import com.ttfund.web.base.helper.DateHelper;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.constant.CFHTradeMongodbConstant;
import ttfund.web.fortuneservice.dao.CfhTradeMongodbDao;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/23 13:33
 */
@Slf4j
@Service
public class CFHTradeDataDBDropService {
    private static final String CFH_DATA_DROP_TABLE_DAYS_BEFORE = "cfh.data.drop.table.days.before";

    @Value("${"+CFH_DATA_DROP_TABLE_DAYS_BEFORE + ":365}")
    public int days;

    @Resource
    CfhTradeMongodbDao cfhTradeMongodbDao;

    public ReturnT<String> execute() {
        Date dt = new Date();
        //1、生成最大表名
        String maxTableName = String.format(CFHTradeMongodbConstant.TB_NAME_FUND_APPLY_DAY, DateHelper.dateToStr(DateUtils.addDays(dt, -1 * days), DateHelper.FORMAT_YYYYMMDD));
        String prefix = CFHTradeMongodbConstant.TB_NAME_FUND_APPLY_DAY.substring(0, 19);
        //2、获取数据库中的表名
        Set<String> tableNames = cfhTradeMongodbDao.getTableNames();
        //3、过滤出形如TB_Z_FUNDAPPLY_DAY_%s且表名小于最大表名
        Set<String> needDropTableNames = tableNames.stream()
                .filter(name -> name.startsWith(prefix) && maxTableName.compareTo(name) > 0)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(needDropTableNames)) {
            log.info("数据库中不存在相关table，无需删除");
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "数据库中不存在相关table，无需删除");
        }
        //4、删除满足条件的表
        boolean flag = cfhTradeMongodbDao.dropTablesByName(needDropTableNames);
        if (!flag) {
            log.error("删除table出现异常");
            return new ReturnT<>(ReturnT.FAIL_CODE, "删除table出现异常");
        }
        log.info("已经成功删除{}个表：{}", needDropTableNames.size(), JSON.toJSONString(needDropTableNames));
        return new ReturnT<>(ReturnT.SUCCESS_CODE, String.format("已经成功删除%s个表：%s", needDropTableNames.size() ,needDropTableNames.toString()));
    }
}
