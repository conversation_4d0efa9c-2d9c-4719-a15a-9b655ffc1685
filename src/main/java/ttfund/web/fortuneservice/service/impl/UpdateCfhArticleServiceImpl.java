package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ttfund.web.fortuneservice.constant.CFHBaseInfoStatusEnum;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.dao.CfhSqlserverDao;
import ttfund.web.fortuneservice.model.dto.CFHArticleDto;
import ttfund.web.fortuneservice.model.dto.CFHInfoDto;
import ttfund.web.fortuneservice.service.UpdateCfhArticleService;

import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className UpdateCfhArticleServiceImpl
 * @date 2023/4/14 15:53
 */
@Service
@Slf4j
public class UpdateCfhArticleServiceImpl implements UpdateCfhArticleService {

    @Autowired
    private CfhMongodbDao cfhMongodb;

    @Autowired
    private CfhSqlserverDao cfhSqlServer;

    @Override
    public void handCFHArticle() {
        //找断点
        String seqId = String.format("%s:%s", this.getClass().getName(), Thread.currentThread().getStackTrace()[1].getMethodName());
        Date breakPoint = getBreakPoint(seqId, CommonConstant.DEFAULT_BREAK_POINT);
        //断点之后的财富号信息
        List<CFHInfoDto> cfhInfoList = cfhSqlServer.selectCFHInfoByUpdate(breakPoint);
        Set<String> processingSucceeded = new HashSet<>();
        Set<String> processingFailed = new HashSet<>();
        Date maxTime = DateHelper.stringToDate2(CommonConstant.MIN_BREAK_POINT, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
        if (cfhInfoList != null && !cfhInfoList.isEmpty()) {
            //更新MongoDB中文章的字段
            for (CFHInfoDto cfhInfo : cfhInfoList) {
                //更新最大时间
                maxTime = maxTime.after(cfhInfo.getUpDataTime()) ? maxTime : cfhInfo.getUpDataTime();
                String cfhid = cfhInfo.getCFHID();
                Integer cfhStatus = cfhInfo.getStatus();
                //根据财富号id查财富号文章
                CFHArticleDto article = cfhMongodb.getCFHArticleByAuthor(cfhid,cfhStatus);
                if (article == null) {
                    processingFailed.add(cfhid);
                    continue;
                }
                int status = article.getCfhArticle() ? CFHBaseInfoStatusEnum.ADUIT.getCode() : CFHBaseInfoStatusEnum.INIT.getCode();
                if (cfhStatus != status) {
                    //更新SQL server财富号文章
                    boolean sqlResult = cfhSqlServer.updateCFHArticle(cfhid, cfhStatus);
                    if (sqlResult) {
                        //更新mongodb
                        cfhMongodb.updateCFHArticle(cfhid, cfhStatus);
                        processingSucceeded.add(cfhid);
                    }
                }
            }
        }
        if (!processingFailed.isEmpty()) {
            log.info("财富号java服务，更新财富号文章异常,mongoDB缺失财富号【{}】",processingFailed);
        }
        log.info("财富号java服务，断点时间【{}】，财富号更新【{}】条,更新列表【{}】", DateHelper.dateToStr(breakPoint, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS), cfhInfoList.size(),
                processingSucceeded);
        //存新断点
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("time", DateHelper.dateToStr(maxTime, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS));
        cfhMongodb.upsertCFHGeneralData(seqId, Arrays.asList(jsonObject));
    }

    /**
     * 查断点
     * <AUTHOR>
     * @date 2023/4/14 15:58
     * @param seqId seqId
     * @param defaultValue defaultValue
     * @return java.util.Date
     */
    private Date getBreakPoint(String seqId,String defaultValue) {
        Date preBreakPointDate = null;
        String preBreakPoint = cfhMongodb.getCFHGeneralData(seqId, defaultValue);
        try {
            JSONObject o = (JSONObject) JSON.parseArray(preBreakPoint).get(0);
            preBreakPointDate = DateHelper.stringToDate2(o.getString("time"), DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
        } catch (Exception e) {
            log.warn("财富号java服务，同步财富号文章断点时间转换异常，【db.getSiblingDB(\"TTFundCFHDB\").getCollection(\"TB_CFHGeneralData\").find({\"_id\": {$eq: 'ttfund.web.fortuneservice.handler.impl.UpdateCfhArticleServiceImpl:handCFHArticle'}})】");
            preBreakPointDate = DateHelper.stringToDate2(defaultValue, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
        }
        return preBreakPointDate;
    }
}
