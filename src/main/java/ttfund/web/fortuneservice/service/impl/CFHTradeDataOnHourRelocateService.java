package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.DateHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.constant.VerticaSqlConstant;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.dao.CfhTradeMongodbDao;
import ttfund.web.fortuneservice.dao.VerticaDao;
import ttfund.web.fortuneservice.manager.TTTradeDetailApi;
import ttfund.web.fortuneservice.model.dto.FundApplyDataStatModel;
import ttfund.web.fortuneservice.model.dto.TradeDetailRecord;
import ttfund.web.fortuneservice.utils.TimeUtil;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static ttfund.web.fortuneservice.constant.CFHMongodbConstant.RegularInspectionReportField.REFRESH_THE_MARKER;
import static ttfund.web.fortuneservice.constant.CFHMongodbConstant.RegularInspectionReportField.TRADE_DATA_HOUR_POINT;

/**
 * <AUTHOR>
 * @date 2023/2/3 9:13
 * 财富号分时数据迁移
 */
@Service
public class CFHTradeDataOnHourRelocateService {

    private static Logger logger = LoggerFactory.getLogger(CFHTradeDataOnHourRelocateService.class);

    @Resource
    TTTradeDetailApi tradeDetailApi;

    @Resource
    VerticaDao verticaDao;

    @Resource
    private CfhTradeMongodbDao cfhTradeMongodbDao;

    @Resource
    private CfhMongodbDao cfhMongodbDao;

    public ReturnT<String> sync() {
        XxlJobLogger.log("start to relocate data on hours······");

//        if (tradeDetailApi.isTradeDay(dt)) {
//            int nowHour = DateUtils.getHour(dt);
//            if ((nowHour >= 13 && nowHour < 15) || (nowHour >= 20 && nowHour < 22)) {
//                return new ReturnT<>(ReturnT.SUCCESS_CODE, String.format("当前为%s点，该时间段不需要执行数据迁移操作", nowHour));
//            }
//        }
        //断点时间
        Date nowDate = DateHelper.getNowDate();
        Date todayStartAsDate = TimeUtil.getTodayStartAsDate();
        Date breakPoint = cfhMongodbDao.getBreakPoint(TRADE_DATA_HOUR_POINT, TimeUtil.dateToStr(todayStartAsDate, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS));
        //断点加1s
        breakPoint = DateUtils.addSeconds(breakPoint, 1);

        //15点
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 15);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date fifteenOClock = cal.getTime();

        //开始时间取断点和当天0点中的较大值
        Date startTime = todayStartAsDate.before(breakPoint) ? breakPoint : todayStartAsDate;
        // 如果当前时间大于15:00，startTime取15:00和原startTime中的较大值
        if (nowDate.after(fifteenOClock)) {
            startTime = fifteenOClock.after(startTime) ? fifteenOClock : startTime;
            //15：00加1s
            startTime = DateUtils.addSeconds(startTime, 1);
        }

    // 结束时间获取, 当开始时间与当前时间差距大于1小时取开始时间加1小时，否则取当前时间
        Date endTime = DateUtils.addHours(startTime, 1);
        endTime = endTime.before(nowDate) ? endTime : nowDate;

        StopWatch stopWatch = new StopWatch();
        //1、调用接口获取分时数据 失败则重试3次
        stopWatch.start("获取分时交易数据_耗时");
        List<TradeDetailRecord> hourData = getZippedDataByTime(startTime, endTime);
        stopWatch.stop();
        if (!CollectionUtils.isEmpty(hourData)) {
            int hourDataSize = hourData.size();
            TradeDetailRecord firstRecord = hourData.get(0);
            Date transDate = firstRecord.getTransactionDate() == null? nowDate: firstRecord.getTransactionDate();
            Date applyDate = firstRecord.getApplyTime() == null? nowDate: firstRecord.getApplyTime();
            logger.info("获取{}-{}分时数据",TimeUtil.dateToStr(startTime,TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS),TimeUtil.dateToStr(endTime,TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS));

            breakPoint = hourData.stream().map(TradeDetailRecord::getApplyTime).max(Date::compareTo).get();
            //2、将分时交易数据写入Vertica中
            stopWatch.start("将接口返回的数据落库_耗时");
            boolean insertTradeDetailFlag = verticaDao.insertTradeDetails(hourData);
            hourData.forEach(item -> logger.info("获取到分时数据：{}", JSON.toJSONString(item)));
            stopWatch.stop();
            if (!insertTradeDetailFlag){
                logger.error("插入分时交易数据失败");
                return new ReturnT<>(ReturnT.FAIL_CODE, String.format("插入分时交易数据失败，耗时详情：%s", JSON.toJSONString(stopWatch.getTaskInfo())));
            }
            hourData = null;
            //3、执行sql统计数据
            stopWatch.start("统计交易数据_耗时");
            List<FundApplyDataStatModel> dataStatModels = verticaDao.statTradeData(transDate);
            stopWatch.stop();
            if (CollectionUtils.isEmpty(dataStatModels)) {
                logger.error(String.format("统计分时交易数据结果为空，耗时详情：%s", JSON.toJSONString(stopWatch.getTaskInfo())));
            }
            //4、根据申请时间清除统计表当天的数据并将统计结果插入Vertica
            stopWatch.start("将统计数据插入vertica数据库_耗时");
            boolean flag = verticaDao.delByDayAndInsertData(VerticaSqlConstant.SQL_DELETE_STAT_DATA_BY_APPLY_DAY
                    , VerticaSqlConstant.SQL_INSERT_STAT_DATA, applyDate, CFHTradeDataRelocateCommonService.getInsertStatDataParamList(dataStatModels));
            stopWatch.stop();
            if (!flag) {
                logger.error(String.format("按照申请日期删除分时统计数据并插入分时统计数据失败，耗时详情：%s", JSON.toJSONString(stopWatch.getTaskInfo())));
            }
            //5、将统计得到的数据更新到mongodb中
            stopWatch.start("将分时统计数据更新到mongodb_耗时");
            cfhTradeMongodbDao.updateAndInsertCFHData(transDate,dataStatModels);

            stopWatch.stop();
            logger.info(String.format("插入分时交易数据共%s条，插入分时统计数据共%s条，分时数据迁移总耗时：%s(秒)；耗时详情为：%s",
                    hourDataSize, dataStatModels.size(), stopWatch.getTotalTimeSeconds(), JSON.toJSONString(stopWatch.getTaskInfo())));
        }
        //更新断点
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("time", DateHelper.dateToStr(breakPoint, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS));
        cfhMongodbDao.upsertCFHGeneralData(TRADE_DATA_HOUR_POINT, Arrays.asList(jsonObject));
        return ReturnT.SUCCESS;
    }

    /**
     * 分时数据获取重试
     *
     * @return 交易数据list
     */
//    private List<TradeDetailRecord> getHourData() {
//        int retryTimes = CommonConstant.TRADE_DATA_GET_RETRY_TIMES;
//        List<TradeDetailRecord> hourData = null;
//        while (retryTimes > 0) {
//            hourData = tradeDetailApi.getHourData();
//            if (hourData != null) {
//                break;
//            }
//            retryTimes--;
//            try {
//                Thread.sleep(CommonConstant.TRADE_DATA_API_WAIT_TIME);
//            } catch (InterruptedException e) {
//                logger.error("等待再次调用接口获取分时数据线程被打断，{},{}", e.getMessage(), e);
//                Thread.currentThread().interrupt();
//            }
//        }
//        return hourData;
//    }
    /**
     * 根据开始结束时间获取
     *
     * @return 交易数据list
     */
    private List<TradeDetailRecord> getZippedDataByTime(Date startTime,Date endTime) {
        int retryTimes = CommonConstant.TRADE_DATA_GET_RETRY_TIMES;
        List<TradeDetailRecord> hourData = null;
        while (retryTimes > 0) {
            hourData = tradeDetailApi.getDataByTime(startTime, endTime);
            if (hourData != null) {
                break;
            }
            retryTimes--;
            try {
                Thread.sleep(CommonConstant.TRADE_DATA_API_WAIT_TIME);
            } catch (InterruptedException e) {
                logger.error("等待再次调用接口获取分时数据线程被打断，{},{}", e.getMessage(), e);
                Thread.currentThread().interrupt();
            }
        }
        return hourData;
    }
}
