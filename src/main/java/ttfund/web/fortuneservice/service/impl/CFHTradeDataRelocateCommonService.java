package ttfund.web.fortuneservice.service.impl;

import ttfund.web.fortuneservice.model.dto.FundApplyDataStatModel;
import ttfund.web.fortuneservice.model.dto.TradeDetailRecord;
import ttfund.web.fortuneservice.utils.DateUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/13 9:44
 */
public class CFHTradeDataRelocateCommonService {

    private CFHTradeDataRelocateCommonService(){
    }
    /**
     * 将交易数据转化为vertia插入的参数列表
     *
     * @param records 交易数据
     * @param t
     * @return
     */
    public static List<List<Object>> getInsertTradeDataParamList(List<TradeDetailRecord> records) {
        List<List<Object>> insertList = new ArrayList<>(records.size());
        for (TradeDetailRecord model : records) {
            List<Object> paramList = new ArrayList<>(12);
            paramList.add(model.getApplyNo());
            paramList.add(model.getTradeType());
            paramList.add(model.getParentTradeType());
            paramList.add(model.getParentApplyNo());
            paramList.add(model.getTraceNo());
            paramList.add(model.getFundCode());
            paramList.add(model.getApplyAmount());
            paramList.add(model.getApplyVol());
            Date applyTime = model.getApplyTime();
            paramList.add(applyTime);
            paramList.add(model.getTransactionDate());
            paramList.add(applyTime);
            paramList.add(DateUtils.getHour(applyTime));
            insertList.add(paramList);
        }
        return insertList;
    }

    /**
     * 将统计数据转化为vertia插入的参数列表
     *
     * @param dataStatModels 统计数据
     * @return
     */
    public static List<List<Object>> getInsertStatDataParamList(List<FundApplyDataStatModel> dataStatModels) {
        List<List<Object>> paramList = new ArrayList<>(dataStatModels.size());
        for (FundApplyDataStatModel data : dataStatModels) {
            List<Object> param = new ArrayList<>(9);
            param.add(data.getFundcode());
            param.add(data.getJjgsid());
            param.add(data.getBusintype());
            param.add(data.getApplyday());
            param.add(data.getApplyhour());
            param.add(data.getApplyAmount());
            param.add(data.getApplynum());
            param.add(data.getShortname());
            param.add(data.getTransactionDate());
            paramList.add(param);
        }
        return paramList;
    }
}
