package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import ttfund.web.fortuneservice.constant.CFHMongodbConstant;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.service.GiftReciveLogService;

@Service
@Slf4j
public class GiftReciveLogServiceImpl implements GiftReciveLogService {

    @Value("${" + CommonConstant.GIFT_REWARD_HEADER_ERROR_MSG + ":}")
    public String giftRewardHeaderErrorMsg;
    @Autowired
    CfhMongodbDao cfhMongodbDao;
    @Override
    public int handLog(String key, String value) {
        JSONObject msg = JSON.parseObject(value);
        if (msg == null) {
            return 0;
        }
        JSONObject request = msg.getJSONObject("request");
        JSONObject response = msg.getJSONObject("response");
        String giftid = msg.getString("giftid");
        Integer giftcount = msg.getInteger("giftcount");
        String requestip = msg.getString("requestip");
        Long exectime = msg.getLong("exectime");
        String userid = msg.getString("userid");
        String url = msg.getString("url");
        String passportid = msg.getString("passportid");
        String referer = msg.getString("referer");
        String time = msg.getString("time");

        //更新卡券使用时间
        if (StringUtils.isNotEmpty(referer) && StringUtils.isNotEmpty(giftid)) {
            cfhMongodbDao.updateCardWhite(referer, giftid, 0);
        }
        //写日志
        cfhMongodbDao.insertGiftLog(key, request, response, giftid, giftcount, requestip, exectime, userid, url, passportid, referer, time,CFHMongodbConstant.TB_CFHCardLog);
        //统计
        //如果是bad标记则写入badlog表
        String firstError = response.getString("firstError");
        if (giftRewardHeaderErrorMsg.equals(firstError)) {
            cfhMongodbDao.insertGiftLog(key, request, response, giftid, giftcount, requestip, exectime, userid, url, passportid, referer, time, CFHMongodbConstant.TB_CFHCardBadLog);
        }
        return 0;
    }
}
