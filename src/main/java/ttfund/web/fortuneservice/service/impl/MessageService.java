package ttfund.web.fortuneservice.service.impl;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ttfund.web.fortuneservice.config.CommonConfig;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.manager.DongDongManager;
import ttfund.web.fortuneservice.utils.DateUtils;
import ttfund.web.fortuneservice.utils.TimeUtil;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class MessageService {
    public static final String KAFKA_MESSAGE_DEL_YEAR = "kafka.message.del.year";

    @ApolloJsonValue("${" + KAFKA_MESSAGE_DEL_YEAR + ":{}}")
    public Map<String, Double> kafkaMessageDelYearMap;

    @Resource
    private DongDongManager dongDongManager;

    @Autowired
    private CfhMongodbDao cfhMongodbDao;

    @Autowired
    private CommonConfig commonConfig;

    public void deleteMessage() {
        // 获取所有 topic
        List<String> allTopic = cfhMongodbDao.getAllTopic();
        List<String> missingConfigTopics = new ArrayList<>();

        // 检查配置是否缺失
        for (String topic : allTopic) {
            Double year = kafkaMessageDelYearMap.get(topic);
            if (year == null || year <= 0) {
                missingConfigTopics.add(topic);
            }
        }

        // 发送告警消息
        if (!missingConfigTopics.isEmpty()) {
            String oas = commonConfig.alarmOaMap.getOrDefault("CFHKafkaMessageDropJob", "220089");
            String message = "以下 Topic 删除时间配置缺失：" + String.join(", ", missingConfigTopics);
            log.info(message);
            dongDongManager.sendDongDongMessage(oas, "删除Kafka消息配置缺失", message);
        }

        // 删除消息
        Date nowDate = TimeUtil.getNowDate();
        int totalDeletedRows = 0;

        for (Map.Entry<String, Double> entry : kafkaMessageDelYearMap.entrySet()) {
            String topic = entry.getKey();
            Double value = entry.getValue();

            if (value == null || value <= 0) {
                continue;
            }

            Date date = DateUtils.addYears(nowDate, -(int) (value * 365));
            try {
                int row = cfhMongodbDao.deleteMessage(date, topic);
                totalDeletedRows += row;
                log.info("删除Kafka消息表数据, topic: {}, 删除数量: {}", topic, row);
            } catch (Exception e) {
                log.error("删除Kafka消息失败, topic: {}, 错误信息: {}", topic, e.getMessage(), e);
            }
        }
        log.info("Kafka消息删除任务完成，总共删除记录数: {}", totalDeletedRows);
    }
}
