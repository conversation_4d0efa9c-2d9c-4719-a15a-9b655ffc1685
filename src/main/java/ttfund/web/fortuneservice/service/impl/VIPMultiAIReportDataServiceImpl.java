package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.dao.sqlserver.VIPAIReportDetailMapper;
import ttfund.web.fortuneservice.dao.sqlserver.VIPAIReportMapper;
import ttfund.web.fortuneservice.model.dto.MultiMonthlyReportRequestDTO;
import ttfund.web.fortuneservice.model.dto.MultiMonthlyReportResponseDTO;
import ttfund.web.fortuneservice.model.entity.VIPAIReport;
import ttfund.web.fortuneservice.model.entity.VIPAIReportDetail;
import ttfund.web.fortuneservice.service.VIPMultiAIReportDataService;
import ttfund.web.fortuneservice.utils.CommonUtil;
import ttfund.web.fortuneservice.utils.TimeUtil;
import ttfund.web.fortuneservice.constant.VIPAIReportConstant;

import java.util.ArrayList;
import java.util.List;

/**
 * VIP多机构AI报告数据服务实现类
 */
@Service
@Slf4j
public class VIPMultiAIReportDataServiceImpl implements VIPMultiAIReportDataService {

    @Autowired
    private VIPAIReportMapper vipAIReportMapper;
    
    @Autowired
    private VIPAIReportDetailMapper vipAIReportDetailMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMultiReportToDatabase(MultiMonthlyReportRequestDTO request, MultiMonthlyReportResponseDTO response, String taskConfigId, String taskCycleMark) {
        if (request == null || response == null) {
            log.error("保存多机构报告失败：请求或响应数据为空");
            throw new IllegalArgumentException("请求或响应数据不能为空");
        }
        
        if (CollectionUtils.isEmpty(request.getInstitutions())) {
            log.error("保存多机构报告失败：机构列表为空");
            throw new IllegalArgumentException("机构列表不能为空");
        }
        
        log.info("开始保存多机构报告到数据库，机构数量：{}，任务月份：{}", 
                response.getInstitutionCount(), response.getTaskMonth());
        
        try {
            // 1. 保存主报告记录（多机构版本CFHID为null）
            VIPAIReport report = new VIPAIReport();
            report.setId(CommonUtil.getGuId(32));
            report.setCfhid(null); // 多机构版为null
            report.setTaskConfigId(taskConfigId); // 从查询结果中获取
            report.setTaskCycleMark(taskCycleMark); // 任务周期标记
            report.setReportDate(response.getTaskMonth());
            report.setStatus(VIPAIReportConstant.Status.PENDING); // 待审核
            report.setCreateTime(TimeUtil.getNowDate());
            report.setUpdateTime(TimeUtil.getNowDate());
            
            vipAIReportMapper.insertSelective(report);
            
            // 2. 保存详细信息
            List<VIPAIReportDetail> details = new ArrayList<>();

            // 保存整体总结
            if (response.getOverallSummary() != null) {
                VIPAIReportDetail overallDetail = createMultiReportDetail(
                    report.getId(), "整体总结", JSON.toJSONString(response.getOverallSummary()), VIPAIReportConstant.ReportType.OVERALL);
                details.add(overallDetail);
            }

            // 保存行业分析
            if (response.getIndustryAnalysis() != null) {
                VIPAIReportDetail industryDetail = createMultiReportDetail(
                    report.getId(), "热门行业", JSON.toJSONString(response.getIndustryAnalysis()), "HOT_INDUSTRY");
                details.add(industryDetail);
            }

            // 保存非行业分析
            if (response.getNonIndustryAnalysis() != null) {
                VIPAIReportDetail nonIndustryDetail = createMultiReportDetail(
                    report.getId(), "资产研判", JSON.toJSONString(response.getNonIndustryAnalysis()), "ASSET_ANALYSIS");
                details.add(nonIndustryDetail);
            }

            // 保存资产配置分析
            if (response.getAssetAllocationAnalysis() != null) {
                VIPAIReportDetail assetAllocationDetail = createMultiReportDetail(
                    report.getId(), "配置建议", JSON.toJSONString(response.getAssetAllocationAnalysis()), "ALLOCATION_ADVICE");
                details.add(assetAllocationDetail);
            }

            // 保存宏观经济分析
            if (response.getMacroEconomicAnalysis() != null) {
                VIPAIReportDetail macroEconomicDetail = createMultiReportDetail(
                    report.getId(), "宏观经济", JSON.toJSONString(response.getMacroEconomicAnalysis()), "MACRO_ECONOMY");
                details.add(macroEconomicDetail);
            }
            
            // 批量插入详细信息
            if (!CollectionUtils.isEmpty(details)) {
                vipAIReportDetailMapper.batchInsert(details);
            }
            
            log.info("成功保存多机构报告到数据库，报告ID：{}，详细信息条数：{}", report.getId(), details.size());
            
        } catch (Exception e) {
            log.error("保存多机构报告到数据库异常", e);
            throw new RuntimeException("保存多机构报告到数据库异常", e);
        }
    }
    
    /**
     * 创建多机构报告详细信息
     */
    private VIPAIReportDetail createMultiReportDetail(String reportId, String title, String content, String reportType) {
        VIPAIReportDetail detail = new VIPAIReportDetail();
        detail.setId(CommonUtil.getGuId(32));
        detail.setReportId(reportId);
        detail.setTitle(title);
        detail.setContent(content);
        detail.setReferences(null); // 引用信息已包含在JSON内容中
        detail.setReportType(reportType);
        detail.setType(VIPAIReportConstant.ReportType.MULTI_ORG); // 默认为统一报告，调用方可以覆盖
        detail.setCreateTime(TimeUtil.getNowDate());
        detail.setUpdateTime(TimeUtil.getNowDate());
        return detail;
    }
}
