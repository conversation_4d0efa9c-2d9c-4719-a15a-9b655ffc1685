package ttfund.web.fortuneservice.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.manager.CFHApiManager;
import ttfund.web.fortuneservice.model.bo.AuthorDetailBo;
import ttfund.web.fortuneservice.model.bo.CFHUpdateInfoBo;
import ttfund.web.fortuneservice.service.UpdateCFHFInfoService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description UpdateCFHFInfoServiceImpl。java
 * @date 2023/8/3 14:35
 */
@Service
public class UpdateCFHFInfoServiceImpl implements UpdateCFHFInfoService {

    private final Logger logger = LoggerFactory.getLogger(UpdateCFHFInfoServiceImpl.class);

    @Resource
    CfhMongodbDao cfhMongodbDao;

    @Resource
    CFHApiManager cfhApiManager;

    @Override
    public boolean updateCFHFInfo(String param) {
        try {
            // 1.获取全部财富号Id - 读库
            List<String> allCFHIds = cfhMongodbDao.getAllCFHIds();
            List<CFHUpdateInfoBo> updateInfoList = new ArrayList<>();
            for (String id : allCFHIds) {
                // 2. 循环调接口获取财富号信息
                AuthorDetailBo cfhDetail = cfhApiManager.getAuthorDetail(id);

                if (cfhDetail != null) {
                    updateInfoList.add(new CFHUpdateInfoBo(
                            id,
                            cfhDetail.getFandsCount(),
                            cfhDetail.getPVCount(),
                            cfhDetail.getArticleCount(),
                            cfhDetail.getBigVip(),
                            cfhDetail.getIsFund()
                    ));
                }
            }
            // 3.批量写入mongo
            int count = cfhMongodbDao.updateCFHInfos(updateInfoList);
            logger.info("执行财富号服务 - 财富号信息更新(关注数等) - 批量更新cfh条数count：{}，任务执行完成！", count);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return false;
        }
        return true;
    }
}
