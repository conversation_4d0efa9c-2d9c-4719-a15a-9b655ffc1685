package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSON;
import com.ttfund.web.base.helper.DateHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.config.CommonConfig;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.manager.HqApiManager;
import ttfund.web.fortuneservice.model.bo.HotLabelBo;
import ttfund.web.fortuneservice.model.bo.ReviewInfoResponse;
import ttfund.web.fortuneservice.model.bo.ReviewOpinion;
import ttfund.web.fortuneservice.model.dto.FundLabelDto;
import ttfund.web.fortuneservice.model.dto.FundThemeDto;
import ttfund.web.fortuneservice.model.dto.QuickReviewDto;
import ttfund.web.fortuneservice.service.HotLabelService;
import ttfund.web.fortuneservice.utils.CommonUtil;
import ttfund.web.fortuneservice.utils.ReviewDataConvert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

@Service
public class HotLabelServiceImpl implements HotLabelService {

    Logger logger = LoggerFactory.getLogger(HotLabelServiceImpl.class);

    @Resource
    private CommonConfig config;

    @Resource
    private HqApiManager hqApiManager;

    @Resource
    private CfhMongodbDao cfhMongodbDao;

    @Override
    public void hotLabelHandler() {
        // 1、获取前2天 天天快评数据
        List<QuickReviewDto> reviewList = this.getQuickReviews();
        // 2、同一天内存在卡片为同一财富号ID, 仅展示最新的一条卡片
        Map<String, QuickReviewDto> map = new HashMap<>();
        if (reviewList != null && !reviewList.isEmpty()) {
            map = reviewList.stream().collect(
                    groupingBy(o -> o.getCFHID() + ":" + o.getUpdateDay(),
                            Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
        }
        // 3、获取标签json查mongodb TTFundCFHDB.Tb_CFHFundTheme
        List<FundLabelDto> labelList = cfhMongodbDao.getQuickReviewTheme();
        if (labelList == null || labelList.isEmpty() || CollectionUtils.isEmpty(reviewList)) {
            logger.info("刷新赛道排序失败：快评reviewList为空，或标签列表labelList为空。");
            return;
        }
        Map<String, FundLabelDto> labelMapJson = labelList.stream().collect(Collectors.toMap(FundLabelDto::getIndexCode, Function.identity()));

        TreeMap<String, Integer> labelNubMap = new TreeMap<>();
        HashMap<String, Long> labelTimeMap = new HashMap<>();
        List<HotLabelBo> hotLabels = new ArrayList<>();
        // 4、处理天天快评数据
        this.handleQuickReview(map, labelMapJson, labelNubMap, labelTimeMap);
        // 5、标签排序
        List<Map.Entry<String, Integer>> list = this.labelNubSort(labelNubMap, labelTimeMap);
        // 6、处理成前端数据
        for (Map.Entry<String, Integer> entry : list) {
            if (entry.getValue() < 2) {
                break;
            }
            String[] s = entry.getKey().split("_");
            hotLabels.add(new HotLabelBo(s[0], s[1], entry.getValue(), labelTimeMap.get(entry.getKey())));
        }
        // 7、写入mongo 通用数据表
        logger.info("赛道标签缓存, 存入mongo：{}",hotLabels);
        boolean upsertRes = cfhMongodbDao.upsertCFHGeneralData(CommonConstant.HOT_LABEL_LIST, hotLabels);
        if (!upsertRes){
            logger.warn("赛道标签缓存, 存入mongo失败");
        }
    }

    @Override
    public void activityLabelHandler() {
        List<String> activityIdList = config.activityIdList;
        if (CollectionUtils.isEmpty(activityIdList)) {
            return;
        }
        //查所有主题
        List<FundLabelDto> labelList = cfhMongodbDao.getQuickReviewTheme();
        if (labelList == null || labelList.isEmpty()) {
            logger.info("标签列表labelList为空。");
            return;
        }
        Map<String, FundLabelDto> labelMapJson = labelList.stream().collect(Collectors.toMap(FundLabelDto::getIndexCode, Function.identity()));
        for (String activityId : activityIdList) {
            //查快评
            List<QuickReviewDto> quickReviewList = cfhMongodbDao.getQuickReviewByActivity(activityId);
            if (CollectionUtils.isEmpty(quickReviewList)) {
                logger.info("活动id【{}】，无快评。", activityId);
                continue;
            }
            //处理数据
            TreeMap<String, Integer> industryPerspectivesMap = new TreeMap<>();//type=0，观点次数
            TreeMap<String, Integer> marketViewMap = new TreeMap<>();//type=1，观点次数

            //EmotionType：情绪类型, 0乐观, 1中性, 2谨慎
            Map<String, ReviewOpinion> reviewOpinion = new HashMap<>();

            //快评数据
            Map<String, List<ReviewInfoResponse>> data = new HashMap<>();
            //循环快评
            for (QuickReviewDto review : quickReviewList) {
                //防止一条快评被多次统计
                HashMap<String, Long> reviewUsedSet = new HashMap<>();
                //快评里的标签
                List<FundThemeDto> themeList = review.getThemeLabel();
                if (themeList == null || themeList.isEmpty()) {
                    continue;
                }
                for (FundThemeDto fundTheme : themeList) {
                    //统计整理标签次数
                    FundLabelDto showLabel = new FundLabelDto();
                    if (review.getLabelType() != null && review.getLabelType() == 1) {
                        showLabel.setThemeName(fundTheme.getThemeName());
                        showLabel.setIndexCode(fundTheme.getThemeId());
                        String reviewKey = showLabel.getIndexCode() + review.getID();
                        String label = this.generateLabelKey(showLabel);
                        Long ifUsed = reviewUsedSet.get(reviewKey);
                        if (ifUsed == null) {
                            reviewUsedSet.put(reviewKey, 1L);
                            marketViewMap.put(label, marketViewMap.getOrDefault(label, 0) + 1);
                        }
                    } else {
                        // 处理标签
                        showLabel = labelMapJson.get(fundTheme.getThemeId());
                        if (null == showLabel) {
                            continue;
                        }
                        //向上找到顶级标签
                        showLabel = this.cascadeProcess(showLabel, labelMapJson);
                        String reviewKey = showLabel.getIndexCode() + review.getID();
                        String label = this.generateLabelKey(showLabel);
                        Long ifUsed = reviewUsedSet.get(reviewKey);
                        if (ifUsed == null) {
                            reviewUsedSet.put(reviewKey, 1L);
                            industryPerspectivesMap.put(label, industryPerspectivesMap.getOrDefault(label, 0) + 1);
                        }
                    }
                    //统计情绪
                    String reviewKey = showLabel.getIndexCode() + "Emotion" + review.getID();
                    Long ifUsed = reviewUsedSet.get(reviewKey);
                    if (ifUsed == null) {
                        reviewUsedSet.put(reviewKey, 1L);
                        ReviewOpinion tempEmotion = reviewOpinion.get(showLabel.getIndexCode()) == null ? new ReviewOpinion(showLabel.getIndexCode(), showLabel.getIndexName()) : reviewOpinion.get(showLabel.getIndexCode());
                        Integer emotionType = review.getEmotionType();
                        switch (emotionType) {
                            case 0:
                                tempEmotion.setOptimism(tempEmotion.getOptimism() + 1);
                                break;
                            case 1:
                                tempEmotion.setNeuter(tempEmotion.getNeuter() + 1);
                                break;
                            case 2:
                                tempEmotion.setGloomy(tempEmotion.getGloomy() + 1);
                                break;
                            default:
                                break;

                        }
                        reviewOpinion.put(showLabel.getIndexCode(), tempEmotion);
                    }
                    //将快评拆分到各个主题
                    String reviewDataKey = showLabel.getIndexCode() + "review" + review.getID();
                    if (reviewUsedSet.get(reviewDataKey) == null) {
                        reviewUsedSet.put(reviewDataKey, 1L);
                        List<ReviewInfoResponse> lableReviewList = data.get(showLabel.getIndexCode()) == null ? new LinkedList<>() : data.get(showLabel.getIndexCode());
                        lableReviewList.add(ReviewDataConvert.reviewDataConvert(review));
                        data.put(showLabel.getIndexCode(), lableReviewList);
                    }
                }
            }
            //标签
            List<HotLabelBo> lables0 = new ArrayList<>();
            List<HotLabelBo> lables1 = new ArrayList<>();
            for (Map.Entry<String, Integer> entry : industryPerspectivesMap.entrySet()) {
                String[] s = entry.getKey().split("_");
                lables0.add(new HotLabelBo(s[0], s[1], entry.getValue(), 0L));
            }
            for (Map.Entry<String, Integer> entry : marketViewMap.entrySet()) {
                String[] s = entry.getKey().split("_");
                lables1.add(new HotLabelBo(s[0], s[1], entry.getValue(), 1L));
            }
            lables0.addAll(lables1);
            //存数据
            cfhMongodbDao.upsertCFHGeneralData(String.format(CommonConstant.LABEL_ACTIVITY, activityId), lables0);
            for (ReviewOpinion opinion : reviewOpinion.values()) {
                cfhMongodbDao.upsertCFHGeneralData(String.format(CommonConstant.LABEL_OPINION, activityId, opinion.getLabelId()),
                        Arrays.asList(opinion));
            }

            for (Map.Entry<String, List<ReviewInfoResponse>> entry : data.entrySet()) {
                String themId = entry.getKey();
                List<ReviewInfoResponse> reviewList = entry.getValue();
                cfhMongodbDao.upsertCFHGeneralData(String.format(CommonConstant.LABEL_DATA, activityId, themId), reviewList);
            }
        }

    }

    /**
     * 获取前2天 天天快评数据
     * <AUTHOR>
     * @date 2023/4/10 11:13
     * @return java.util.List
     */
    private List<QuickReviewDto> getQuickReviews() {
        // 当前时间
        Date nowDate = DateHelper.getNowDate();
        // 查交易日库时间、处理命中缓存
        Date tradeDayPreDate = DateHelper.stringToDate2(DateHelper.dateToStr(nowDate, DateHelper.FORMAT_YYYY_MM_DD), DateHelper.FORMAT_YYYY_MM_DD);
        tradeDayPreDate = DateUtils.addSeconds(tradeDayPreDate, 1);
        // 查前2天时间
        Date dayBegin = hqApiManager.getTradeDayPre(tradeDayPreDate, config.labelDays);
        // 查询开始时间
        Long timePointBegin = CommonUtil.getTimePoint(dayBegin);
        Long timePointEnd = CommonUtil.getTimePoint(nowDate);
        // 获取快评
        return cfhMongodbDao.getQuickReview(timePointBegin, timePointEnd, 0,0);
    }

    /**
     * 处理天天快评数据
     * <AUTHOR>
     * @date 2023/4/10 13:34
     */
    private void handleQuickReview(Map<String, QuickReviewDto> map, Map<String, FundLabelDto> labelMapJson, TreeMap<String, Integer> labelNubMap, HashMap<String, Long> labelTimeMap) {
        for (QuickReviewDto review : map.values()) {
            HashMap<String, Long> reviewUsedSet = new HashMap<>();
            List<FundThemeDto> themeList = review.getThemeLabel();
            if (themeList == null || themeList.isEmpty()) {
                continue;
            }
            for (FundThemeDto fundTheme : themeList) {
                // 处理标签
                FundLabelDto showLabel = labelMapJson.get(fundTheme.getThemeId());
                if (null == showLabel) {
                    continue;
                }
                showLabel = this.cascadeProcess(showLabel, labelMapJson);

                String reviewKey = showLabel.getIndexCode() + review.getID();
                String label = this.generateLabelKey(showLabel);
                Long ifUsed = reviewUsedSet.get(reviewKey);
                if (ifUsed == null) {
                    reviewUsedSet.put(reviewKey, 1L);
                    labelNubMap.put(label, labelNubMap.getOrDefault(label, 0) + 1);
                }
                labelTimeMap.put(label, labelTimeMap.getOrDefault(label, 0L) > review.getTimepoint() ? labelTimeMap.get(label) : review.getTimepoint());
            }
        }
    }

    /**
     * 标签级联处理
     * <AUTHOR>
     * @date 2023/4/10 13:53
     * @return ttfund.web.fortuneservice.model.dto.FundLabelDto
     */
    private FundLabelDto cascadeProcess(FundLabelDto showLabel, Map<String, FundLabelDto> labelMapJson) {
        while (StringUtils.isNotEmpty(showLabel.getParentIndexCode())) {
            FundLabelDto parent = labelMapJson.get(showLabel.getParentIndexCode());
            if (parent != null && !parent.getIndexCode().equals(parent.getParentIndexCode())) {
                showLabel = parent;
            } else {
                break;
            }
        }
        return showLabel;
    }

    /**
     * 生成label key
     * <AUTHOR>
     * @date 2023/4/10 13:57
     * @return java.lang.String
     */
    private String generateLabelKey(FundLabelDto showLabel) {
        String temp = showLabel.getThemeName();
        if (StringUtils.isEmpty(showLabel.getThemeName())) {
            temp = StringUtils.isNotEmpty(showLabel.getIndexName()) ? showLabel.getIndexName() : "";
        }
        return showLabel.getIndexCode() + "_" + temp;
    }

    /**
     * 标签排序
     * <AUTHOR>
     * @date 2023/4/10 13:29
     * @return java.util.List
     */
    private List<Map.Entry<String, Integer>> labelNubSort(TreeMap<String, Integer> labelNubMap, HashMap<String, Long> labelTimeMap) {
        List<Map.Entry<String, Integer>> list = new ArrayList<>(labelNubMap.entrySet());
        list.sort((o1, o2) -> {
            if (o1.getValue() > o2.getValue()) {
                return -1;
            } else if (o1.getValue() < o2.getValue()) {
                return 1;
            } else {
                Long o1Time = labelTimeMap.get(o1.getKey());
                Long o2Time = labelTimeMap.get(o2.getKey());
                return o2Time.compareTo(o1Time);
            }
        });
        return list;
    }
}
