package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ttfund.web.base.helper.HttpHelper;
import com.ttfund.web.core.model.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.config.CommonConfig;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.model.bo.FundThemeBo;
import ttfund.web.fortuneservice.model.dto.FundLabelDto;
import ttfund.web.fortuneservice.service.UpdateCfhFundThemeService;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UpdateCfhFundThemeServiceImpl implements UpdateCfhFundThemeService {


    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private CfhMongodbDao cfhMongodbDao;

    @Override
    public void handCFHFundTheme() {
        String hqBaseUrl = commonConfig.hqBaseUrl;
        String http = HttpHelper.requestGet(hqBaseUrl + "/mm/FundTheme/fundThemeClassificationList", "LOADALL=true", 1000 * 60 * 10);

        //查所有主题
        List<FundLabelDto> themeDbList = cfhMongodbDao.getQuickReviewTheme();
        Map<String, FundLabelDto> themeDbMap = themeDbList.stream().collect(Collectors.toMap(FundLabelDto::getIndexCode, Function.identity(), (o1, o2) -> o1));

        if (StringUtils.isNotEmpty(http)) {
            TypeReference<ApiResponse<List<FundThemeBo>,?>> typeReference = new TypeReference<ApiResponse<List<FundThemeBo>,?>>(){};
            ApiResponse<List<FundThemeBo>, ?> res = JSON.parseObject(http, typeReference);
            Optional<ApiResponse<List<FundThemeBo>, ?>> optionalRes = Optional.ofNullable(res);
            optionalRes.ifPresent(response -> {
                List<FundThemeBo> list = response.getData();
                if (!CollectionUtils.isEmpty(list)) {
                    for (FundThemeBo theme : list) {
                        String name = theme.getName();
                        String code = theme.getCode();
                        if (StringUtils.isNotEmpty(name) && StringUtils.isNotEmpty(code)) {
                            FundLabelDto inDB = themeDbMap.getOrDefault(code,new FundLabelDto());
                            if (!name.equals(inDB.getIndexName())) {
                                //更新表
                                cfhMongodbDao.updateTheme(code, name, 0);
                            }
                            themeDbMap.remove(code);
                        }
                    }
                    if (!CollectionUtils.isEmpty(themeDbMap)) {
                        for (FundLabelDto theme : themeDbMap.values()) {
                            cfhMongodbDao.updateTheme(theme.getIndexCode(), theme.getThemeName(),1);
                        }
                    }
                }
            });
        }
    }
}
