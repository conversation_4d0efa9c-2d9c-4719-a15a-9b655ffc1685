package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.DateHelper;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.config.App;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.constant.NoticeTypeEnum;
import ttfund.web.fortuneservice.constant.ResearchTaskCyclesEnum;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.dao.CfhSqlserverDao;
import ttfund.web.fortuneservice.manager.KafkaManager;
import ttfund.web.fortuneservice.model.cfhkafka.CFHNoticeModel;
import ttfund.web.fortuneservice.model.dto.*;
import ttfund.web.fortuneservice.service.TaskAutoGenerateService;
import ttfund.web.fortuneservice.utils.CommonUtil;
import ttfund.web.fortuneservice.utils.DBUtil;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * TaskAutoGenerateServiceImpl.java
 * 自动为机构下发任务及生成题目的实现
 *
 * <AUTHOR>
 * @date 2023/4/27 9:35
 */
@Service
public class TaskAutoGenerateServiceImpl implements TaskAutoGenerateService {

    private Logger logger = LoggerFactory.getLogger(TaskAutoGenerateServiceImpl.class);

    @Resource
    CfhSqlserverDao sqlServer;

    @Resource
    private CfhMongodbDao cfhMongodb;

    @Resource
    private KafkaManager kafkaManager;

    @Resource
    private App app;

    @Override
    public boolean autoGenerateService() {
        boolean flag;
        Date nowDate = DateHelper.getNowDate();
        StringBuilder log = new StringBuilder();
        Map<ResearchTaskConfigDto, ResearchTaskGroupDto> tasksMap = new HashMap<>();
        try {
            // 1.查找一次性任务/周期任务的配置信息及组信息
            String seqId = String.format("%s:%s", this.getClass().getName(), Thread.currentThread().getStackTrace()[1].getMethodName());
            Map<ResearchTaskConfigDto, ResearchTaskGroupDto> onceTasks = sqlServer.selectByTaskCycles(true, this.getBreakpointTime(seqId), nowDate);
            Map<ResearchTaskConfigDto, ResearchTaskGroupDto> periodTasks = sqlServer.selectByTaskCycles(false, null, nowDate);
            log.append("本次需处理").append(onceTasks.size()).append("个一次性任务对象和").append(periodTasks.size()).append("个周期任务对象，");
            tasksMap.putAll(onceTasks);
            tasksMap.putAll(periodTasks);

            // 2.为相应的财富号机构生成任务
            List<ResearchTaskListDto> taskList = this.getResearchTaskList(tasksMap);
            // 3.为定期观点类型的周期任务的生成题目
            List<ResearchTaskSubjectDto> subjectList = this.getResearchTaskSubjects(new ArrayList<>(tasksMap.keySet()));
            log.append("共需相应的财富号机构下发").append(taskList.size()).append("个任务,为定期观点类型的周期任务的生成").append(subjectList.size()).append("个题目，");

            log.append(String.format("生成的taskList的ID- %s ,生成的subjectList的ID- %s ",
                    taskList.stream().map(ResearchTaskListDto::getID).collect(Collectors.toList()).toString(),
                    subjectList.stream().map(ResearchTaskSubjectDto::getID).collect(Collectors.toList()).toString()));

            List<TaskNoticeDto> taskNoticeList = new ArrayList<>();
            List<CFHNoticeModel> cfhNoticeModelList = new ArrayList<>();
            // 4.保存任务
            if (!CollectionUtils.isEmpty(taskList)) {
                flag = sqlServer.insertResearchTaskList(taskList);
                log.append("任务保存成功，");
                taskNoticeList = getNoticeList(taskList);
                cfhNoticeModelList = getCfhNoticeList(taskNoticeList);
                try {
                    boolean saveNotice = sqlServer.insertNoticeList(taskNoticeList);
                    if (!saveNotice) {
                        logger.error("任务通知保存失败，任务ids：【{}】", taskList.stream().map(ResearchTaskListDto::getID).collect(Collectors.toList()));
                    } else {
                        log.append(String.format("消息通知保存成功，消息ID-[%s]", taskNoticeList.stream().filter(Objects::nonNull).map(TaskNoticeDto::getId).collect(Collectors.toList()).toString()));
                    }
                } catch (Exception e) {
                    logger.error("任务通知保存报错，任务ids：【{}】", taskList.stream().map(ResearchTaskListDto::getID).collect(Collectors.toList()));
                }
            }
            // 5.保存题目
            flag = sqlServer.insertResearchSubject(subjectList);
            log.append("题目保存成功，");

            // 7.周期任务时,修改TaskReleaseTime,把当前周期任务的TaskReleaseTime改成下个周期时间（周：+7天，月+1月，季度：+3个月）
            Map<String, Date> periodTaskReleaseTimeMap = this.getPeriodTaskReleaseTime(periodTasks.keySet());
            flag = sqlServer.updatePeriodTaskReleaseTime(periodTaskReleaseTimeMap);
            log.append("修改周期任务的TaskReleaseTime成功，");
            DBUtil.executeSqlsInTransaction(app.getSqlServer().getconn());
            // 6.一次行任务时，回写断点时间
            flag = this.setMaxTaskReleaseTime(onceTasks.keySet(), seqId);
            if (!flag) {
                log.append("回写一次行任务的TaskReleaseTime断点时间失败，");
                throw new RuntimeException("回写一次行任务的TaskReleaseTime断点时间失败");
            } else {
                log.append("回写一次行任务的TaskReleaseTime断点时间成功，");
            }
            try {
                boolean sendNotice = kafkaManager.sendCfhNotice(cfhNoticeModelList);
                if (!sendNotice) {
                    logger.error("任务通知提醒失败，任务ids：【{}】", taskList.stream().map(ResearchTaskListDto::getID).collect(Collectors.toList()));
                }
            } catch (Exception e) {
                logger.error("任务通知提醒报错，任务ids：【{}】", taskList.stream().map(ResearchTaskListDto::getID).collect(Collectors.toList()));
            }
            log.append("任务执行成功。");
        } catch (Exception e) {
            log.append("任务执行失败。数据回滚。");
            logger.error(e.getMessage(), e);
            flag = false;
        }
        logger.info(log.toString());
        XxlJobLogger.log(log.toString());
        return flag;
    }
    @Override
    public boolean autoGenerateVIPService() {
        boolean flag;
        Date nowDate = DateHelper.getNowDate();
        Map<ResearchTaskConfigDto, ResearchTaskGroupDto> tasksMap = new HashMap<>();
        try {
            // 1.查找一次性任务/周期任务的配置信息及组信息
            String seqId = String.format("%s:%s", this.getClass().getName(), Thread.currentThread().getStackTrace()[1].getMethodName());
            Map<ResearchTaskConfigDto, ResearchTaskGroupDto> onceTasks = sqlServer.selectVIPByTaskCycles(true, this.getBreakpointTime(seqId), nowDate);
            Map<ResearchTaskConfigDto, ResearchTaskGroupDto> periodTasks = sqlServer.selectVIPByTaskCycles(false, null, nowDate);
            logger.info("本次需处理【{}】个一次性任务对象和【{}】个周期任务对象", onceTasks.size(), periodTasks.size());
            XxlJobLogger.log("本次需处理【{}】个一次性任务对象和【{}】个周期任务对象", onceTasks.size(), periodTasks.size());
            tasksMap.putAll(onceTasks);
            tasksMap.putAll(periodTasks);

            // 2.为相应的财富号机构生成任务
            List<ResearchTaskListDto> taskList = this.getResearchTaskList(tasksMap);

            // 3.为定期观点类型的周期任务的生成题目
            List<ResearchTaskSubjectDto> subjectList = this.getVIPTaskSubjects(new ArrayList<>(tasksMap.keySet()));
            logger.info("共需相应的财富号机构下发{}个任务,为定期观点类型的周期任务的生成{}个题目", taskList.size(), subjectList.size());
            XxlJobLogger.log("共需相应的财富号机构下发{}个任务,为定期观点类型的周期任务的生成{}个题目", taskList.size(), subjectList.size());

            logger.info("生成的taskList的ID-{} ,生成的subjectList的ID- {} ", JSON.toJSONString(taskList.stream().map(ResearchTaskListDto::getID).collect(Collectors.toList()))
                    , JSON.toJSONString(subjectList.stream().map(ResearchTaskSubjectDto::getID).collect(Collectors.toList())));
            XxlJobLogger.log("生成的taskList的ID-{} ,生成的subjectList的ID- {} ", JSON.toJSONString(taskList.stream().map(ResearchTaskListDto::getID).collect(Collectors.toList()))
                    , JSON.toJSONString(subjectList.stream().map(ResearchTaskSubjectDto::getID).collect(Collectors.toList())));

            List<TaskNoticeDto> taskNoticeList = new ArrayList<>();
            List<CFHNoticeModel> cfhNoticeModelList = new ArrayList<>();
            // 4.保存任务
            if (!CollectionUtils.isEmpty(taskList)) {
                flag = sqlServer.insertVIPTaskList(taskList);
                logger.info("任务保存{}个任务成功", taskList.size());
                XxlJobLogger.log("任务保存{}个任务成功", taskList.size());
                taskNoticeList = getVIPNoticeList(taskList);
                cfhNoticeModelList = getCfhVIPNoticeList(taskNoticeList);
                try {
                    boolean saveNotice = sqlServer.insertNoticeList(taskNoticeList);
                    logger.error("任务通知保存【{}】，任务ids：【{}】", saveNotice, taskList.stream().map(ResearchTaskListDto::getID).collect(Collectors.toList()));
                } catch (Exception e) {
                    logger.error("任务通知保存报错，任务ids：【{}】", taskList.stream().map(ResearchTaskListDto::getID).collect(Collectors.toList()), e);
                }
            }
            // 5.保存题目
            flag = sqlServer.insertVIPSubject(subjectList);

            // 7.周期任务时,修改TaskReleaseTime,把当前周期任务的TaskReleaseTime改成下个周期时间（周：+7天，月+1月，季度：+3个月）
            Map<String, Date> periodTaskReleaseTimeMap = this.getPeriodTaskReleaseTime(periodTasks.keySet());
            flag = sqlServer.updatePeriodVIPTaskReleaseTime(periodTaskReleaseTimeMap);
            DBUtil.executeSqlsInTransaction(app.getSqlServer().getconn());
            // 6.一次行任务时，回写断点时间
            flag = this.setMaxTaskReleaseTime(onceTasks.keySet(), seqId);
            if (!flag) {
                logger.error("回写一次行任务的TaskReleaseTime断点时间失败");
                throw new RuntimeException("回写一次行任务的TaskReleaseTime断点时间失败");
            } else {
                logger.info("回写一次行任务的TaskReleaseTime断点时间成功");
            }
            try {
                boolean sendNotice = kafkaManager.sendCfhNotice(cfhNoticeModelList);
                if (!sendNotice) {
                    logger.error("任务通知提醒失败，任务ids：【{}】", taskList.stream().map(ResearchTaskListDto::getID).collect(Collectors.toList()));
                }
            } catch (Exception e) {
                logger.error("任务通知提醒报错，任务ids：【{}】", taskList.stream().map(ResearchTaskListDto::getID).collect(Collectors.toList()));
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            flag = false;
        }
        return flag;
    }

    private List<CFHNoticeModel> getCfhNoticeList(List<TaskNoticeDto> taskNoticeList) {
        if (CollectionUtils.isEmpty(taskNoticeList)) {
            return new ArrayList<>();
        }
        TaskNoticeConfigDto noticeConfigDto = sqlServer.getNoticeConfig(NoticeTypeEnum.TASK_NOTICE.getType());
        if (noticeConfigDto == null || noticeConfigDto.getNoticeContent() == null) {
            return new ArrayList<>();
        }
        return taskNoticeList.stream().filter(Objects::nonNull).flatMap(dto -> {
            String content = MessageFormat.format(noticeConfigDto.getNoticeContent(), dto.getTaskName(), dto.getTaskEndTime());
            dto.setContent(content);
            dto.setTitle(noticeConfigDto.getNoticeTitle());
//            List<TaskNoticeUserDto> userDtoList = sqlServer.getNoticeUser(dto.getCfhId());
            List<CFHUser> userDtoList = sqlServer.getCfhUserByCFHId(dto.getCfhId());
            if (CollectionUtils.isEmpty(userDtoList)) {
                return null;
            }
            return userDtoList.stream().filter(userDto -> {
                return userDto != null && StringUtils.isNotEmpty(userDto.getUid());
            }).map(userDto -> {
                return CFHNoticeModel.builder().noticeid(dto.getId()).noticetype(3).noticestatus(-1)
                        .taskname(dto.getTaskName()).taskendtime(DateHelper.dateToStr(dto.getTaskEndTime(), DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS))
                        .uid(userDto.getUid()).pushtype(1).title(noticeConfigDto.getNoticeTitle()).content(content).build();
            });
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }
    private List<CFHNoticeModel> getCfhVIPNoticeList(List<TaskNoticeDto> taskNoticeList) {
        if (CollectionUtils.isEmpty(taskNoticeList)) {
            return new ArrayList<>();
        }
        TaskNoticeConfigDto noticeConfigDto = sqlServer.getNoticeConfig(NoticeTypeEnum.VIP_TASK_NOTICE.getType());
        if (noticeConfigDto == null || noticeConfigDto.getNoticeContent() == null) {
            return new ArrayList<>();
        }
        return taskNoticeList.stream().filter(Objects::nonNull).flatMap(dto -> {
            String content = MessageFormat.format(noticeConfigDto.getNoticeContent(), dto.getTaskName(), dto.getTaskEndTime());
            dto.setContent(content);
            dto.setTitle(noticeConfigDto.getNoticeTitle());
            List<CFHUser> userDtoList = sqlServer.getCfhUserByCFHId(dto.getCfhId());
            if (CollectionUtils.isEmpty(userDtoList)) {
                return null;
            }
            return userDtoList.stream().filter(userDto -> userDto != null && StringUtils.isNotEmpty(userDto.getUid()))
                    .map(userDto -> CFHNoticeModel.builder().noticeid(dto.getId()).noticetype(3).noticestatus(-1)
                    .taskname(dto.getTaskName()).taskendtime(DateHelper.dateToStr(dto.getTaskEndTime(), DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS))
                    .uid(userDto.getUid()).pushtype(1).title(noticeConfigDto.getNoticeTitle()).content(content).build());
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<TaskNoticeDto> getNoticeList(List<ResearchTaskListDto> taskList) {
        if (CollectionUtils.isEmpty(taskList)) {
            return new ArrayList<>();
        }
        Date nowDate = DateHelper.getNowDate();
        return taskList.stream().filter(Objects::nonNull).map(dto -> {
            return TaskNoticeDto.builder().id(UUID.randomUUID().toString().replaceAll("-", ""))
                    .cfhId(dto.getCFHID()).content(dto.getTaskContent())
                    .title(dto.getTaskName()).status(1).mgrName("Java服务").createTime(nowDate).updateTime(nowDate)
                    .type(NoticeTypeEnum.TASK_NOTICE.getType())
                    .taskName(dto.getTaskName()).taskEndTime(dto.getTaskEndTime()).build();
        })
                .collect(Collectors.toList());
    }
    private List<TaskNoticeDto> getVIPNoticeList(List<ResearchTaskListDto> taskList) {
        if (CollectionUtils.isEmpty(taskList)) {
            return new ArrayList<>();
        }
        Date nowDate = DateHelper.getNowDate();
        return taskList.stream().filter(Objects::nonNull).map(dto -> {
            return TaskNoticeDto.builder().id(UUID.randomUUID().toString().replaceAll("-", ""))
                    .cfhId(dto.getCFHID()).content(dto.getTaskContent())
                    .title(dto.getTaskName()).status(1).mgrName("Java服务").createTime(nowDate).updateTime(nowDate)
                    .type(NoticeTypeEnum.VIP_TASK_NOTICE.getType())
                    .taskName(dto.getTaskName()).taskEndTime(dto.getTaskEndTime()).build();
        })
                .collect(Collectors.toList());
    }

    /**
     * 获取一次性任务的断点时间
     *
     * @param seqId seqId
     * @return 断点时间
     **/
    private Date getBreakpointTime(String seqId) {
        Date lastQueryTime = null;
        String preBreakPoint = cfhMongodb.getCFHGeneralData(seqId, CommonConstant.MIN_BREAK_POINT_ARRAY);
        try {
            JSONObject o = (JSONObject) JSON.parseArray(preBreakPoint).get(0);
            lastQueryTime = DateHelper.stringToDate2(o.getString("time"), DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS_SSS);
        } catch (Exception e) {
            lastQueryTime = DateHelper.stringToDate2(preBreakPoint, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
            logger.warn("财富号java服务，一次性任务生成断点时间转换异常，【db.getSiblingDB(\"TTFundCFHDB\").getCollection(\"TB_CFHGeneralData\").find({\"_id\": {$eq: '{}'}})】", seqId);
        }
        return lastQueryTime;
    }

    /**
     * 根据任务配置及其配置所对应的组信息生成任务清单
     *
     * @param map 任务配置及其配置所对应的组信息
     * @return 生成好的任务清单
     **/
    private List<ResearchTaskListDto> getResearchTaskList(Map<ResearchTaskConfigDto, ResearchTaskGroupDto> map) {
        List<ResearchTaskListDto> taskList = new ArrayList<>();
        if (CollectionUtils.isEmpty(map)) {
            return taskList;
        }
        // 按任务发布时间排序
        LinkedHashMap<ResearchTaskConfigDto, ResearchTaskGroupDto> sortedMap = map.entrySet().stream()
                .sorted((e1, e2) -> e2.getKey().getTaskReleaseTime().compareTo(e1.getKey().getUpdateTime()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldValue, newValue) -> oldValue, LinkedHashMap::new));

        int i = 0;
        for (Map.Entry<ResearchTaskConfigDto, ResearchTaskGroupDto> entry : sortedMap.entrySet()) {
            ResearchTaskConfigDto config = entry.getKey();
            String content = entry.getValue().getTaskGroupContent();
            // 给各个财富号下发任务
            List<String> cfhids = StringUtils.isEmpty(content) ? null : Arrays.asList(content.split(","));
            // 设置新的毫秒数
            Date date = DateHelper.getNowDate();
            i += 10;
            date.setTime(date.getTime() - i);
            if (!CollectionUtils.isEmpty(cfhids)) {
                cfhids.forEach(cfhid -> {
                    ResearchTaskListDto task = ResearchTaskListDto.builder()
                            .ID(CommonUtil.getGuId(32))
                            .CFHID(cfhid)
                            .TaskConfigID(config.getID())
                            .TaskName(config.getTaskName())
                            .TaskContent(config.getTaskContent())
                            .TaskFileUrl(config.getTaskFileUrl())
                            .TaskType(config.getTaskType())
                            // 任务来源：0-天天基金自动下发、1-机构自建任务
                            .TaskSource(0)
                            // 任务状态：0-未完成、1-已完成
                            .Status(0)
                            // 删除状态：0-未删除，1-已删除
                            .IsDel(0)
                            .CreateTime(date)
                            .UpdateTime(date)
                            .build();
                    // 设置任务发布、终止时间、周期标识
                    task.setTaskCycleMark(this.getTaskCycleMark(config.getTaskCycles(), config.getTaskReleaseTime()));
                    task.setTaskBeginTime(config.getTaskReleaseTime());
                    task.setTaskEndTime(CommonUtil.addDays(config.getTaskReleaseTime(), config.getTaskValidDay()));
                    taskList.add(task);
                });
            }
        }
        return taskList;
    }

    /**
     * 为定期观点类型的任务配置生成题目
     *
     * @param configList 指定任务配置
     * @return 题目
     **/
    private List<ResearchTaskSubjectDto> getResearchTaskSubjects(List<ResearchTaskConfigDto> configList) {
        List<ResearchTaskSubjectDto> sortSubjectList = new ArrayList<>();
        if (CollectionUtils.isEmpty(configList)) {
            return sortSubjectList;
        }
        Map<String, ResearchTaskConfigDto> configIdAndEntityMap = configList.stream().filter(m -> m.getTaskType().equals(1))
                .collect(Collectors.toMap(ResearchTaskConfigDto::getID, Function.identity()));
        if (!CollectionUtils.isEmpty(configIdAndEntityMap)) {
            // 查找库里的配置类题目
            List<ResearchTaskSubjectDto> subjectList = sqlServer.selectConfigSubject(new ArrayList<>(configIdAndEntityMap.keySet()));
            // 合法性校验
            if (!CollectionUtils.isEmpty(subjectList)) {
                sortSubjectList = subjectList;
                int i = 0;
                for (ResearchTaskSubjectDto subject : sortSubjectList) {
                    ResearchTaskConfigDto config = configIdAndEntityMap.get(subject.getTaskConfigID());
                    // 单独设置以下信息，其余信息不变
                    subject.setID(CommonUtil.getGuId(32));
                    subject.setTaskCycleMark(this.getTaskCycleMark(config.getTaskCycles(), config.getTaskReleaseTime()));
                    // 设置新的毫秒数
                    Date date = DateHelper.getNowDate();
                    i += 10;
                    date.setTime(date.getTime() - i);
                    subject.setCreateTime(date);
                    subject.setUpdateTime(date);
                    subject.setOperator("财富号服务生成");
                }
            }
        }
        return sortSubjectList;
    }
    private List<ResearchTaskSubjectDto> getVIPTaskSubjects(List<ResearchTaskConfigDto> configList) {
        List<ResearchTaskSubjectDto> sortSubjectList = new ArrayList<>();
        if (CollectionUtils.isEmpty(configList)) {
            return sortSubjectList;
        }
        Map<String, ResearchTaskConfigDto> configIdAndEntityMap = configList.stream().filter(m -> m.getTaskType().equals(4) || m.getTaskType().equals(1))
                .collect(Collectors.toMap(ResearchTaskConfigDto::getID, Function.identity()));
        if (!CollectionUtils.isEmpty(configIdAndEntityMap)) {
            // 查找库里的配置类题目
            List<ResearchTaskSubjectDto> subjectList = sqlServer.selectVIPConfigSubject(new ArrayList<>(configIdAndEntityMap.keySet()));
            // 合法性校验
            if (!CollectionUtils.isEmpty(subjectList)) {
                sortSubjectList = subjectList;
                int i = 0;
                for (ResearchTaskSubjectDto subject : sortSubjectList) {
                    ResearchTaskConfigDto config = configIdAndEntityMap.get(subject.getTaskConfigID());
                    // 单独设置以下信息，其余信息不变
                    subject.setID(CommonUtil.getGuId(32));
                    subject.setTaskCycleMark(this.getTaskCycleMark(config.getTaskCycles(), config.getTaskReleaseTime()));
                    // 设置新的毫秒数
                    Date date = DateHelper.getNowDate();
                    i += 10;
                    date.setTime(date.getTime() - i);
                    subject.setCreateTime(date);
                    subject.setUpdateTime(date);
                    subject.setOperator("财富号服务生成");
                }
            }
        }
        return sortSubjectList;
    }

    /**
     * 为一次性任务存放 最大任务配置的TaskReleaseTime断点
     *
     * @param set 本次处理过的任务配置
     **/
    private boolean setMaxTaskReleaseTime(Set<ResearchTaskConfigDto> set, String seqId) {
        boolean res = true;
        if (!CollectionUtils.isEmpty(set)) {
            // 获取一次性任务的最大TaskReleaseTime，
            Date lastQueryTime = set.stream()
                    .map(ResearchTaskConfigDto::getTaskReleaseTime)
                    // 获取最大日期
                    .max(Comparator.naturalOrder())
                    .orElse(null);
            // 回写到mongo中
            logger.info("财富号java服务，一次性任务时间的 新的断点时间为【{}】", lastQueryTime);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("time", DateHelper.dateToStr(lastQueryTime, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS_SSS));
            res = cfhMongodb.upsertCFHGeneralData(seqId, Collections.singletonList(jsonObject));
        }
        return res;
    }

    /**
     * 为题目和任务设置周期标识
     *
     * @param taskCycleType   周期任务类型
     * @param taskReleaseTime 任务发布时间(一次性任务)
     * @return TaskCycleMark
     **/
    private String getTaskCycleMark(Integer taskCycleType, Date taskReleaseTime) {
        Date nowDate = DateHelper.getNowDate();
        // 设置周期标识
        String taskCycleMark = "";
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(nowDate);
        if (ResearchTaskCyclesEnum.ONCE.getCode() == taskCycleType) {
            taskCycleMark = DateHelper.dateToStr(taskReleaseTime, DateHelper.FORMAT_YYYY_MM_DD);
        }
        String date = DateHelper.dateToStr(taskReleaseTime, CommonConstant.FORMAT_yyyyMMddHHmmss);
        if (ResearchTaskCyclesEnum.WEEK.getCode() == taskCycleType) {
            taskCycleMark = calendar.get(Calendar.YEAR) + "_week_" + date;
        }
        if (ResearchTaskCyclesEnum.MONTH.getCode() == taskCycleType) {
            taskCycleMark = calendar.get(Calendar.YEAR) + "_month_" + date;
        }
        if (ResearchTaskCyclesEnum.QUARTER.getCode() == taskCycleType) {
            taskCycleMark = calendar.get(Calendar.YEAR) + "_quarter_" + date;
        }
        return taskCycleMark;
    }

    /**
     * 获取周期任务的修改后的TaskReleaseTime
     *
     * @param keySet 周期任务
     * @return map<周期任务的ID, 修改后的TaskReleaseTime>
     **/
    private Map<String, Date> getPeriodTaskReleaseTime(Set<ResearchTaskConfigDto> keySet) {
        Map<String, Date> map = new HashMap<>();
        keySet.forEach(item -> {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(item.getTaskReleaseTime());
            if (item.getTaskCycles().equals(ResearchTaskCyclesEnum.WEEK.getCode())) {
                calendar.add(Calendar.DAY_OF_MONTH, 7);
            }
            if (item.getTaskCycles().equals(ResearchTaskCyclesEnum.MONTH.getCode())) {
                calendar.add(Calendar.MONTH, 1);
            }
            if (item.getTaskCycles().equals(ResearchTaskCyclesEnum.QUARTER.getCode())) {
                calendar.add(Calendar.MONTH, 3);
            }
            map.put(item.getID(), calendar.getTime());
        });
        return map;
    }

}
