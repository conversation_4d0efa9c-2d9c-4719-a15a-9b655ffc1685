package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.ttfund.web.base.helper.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.constant.CFHMongodbConstant;
import ttfund.web.fortuneservice.constant.VerticaSqlConstant;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.dao.VerticaDao;
import ttfund.web.fortuneservice.model.dto.CFHScoreDTO;
import ttfund.web.fortuneservice.service.CFHScoreService;
import ttfund.web.fortuneservice.utils.TimeUtil;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class CFHScoreServiceImpl implements CFHScoreService {

    private static final String DEFAULT_BREAK_POINT_DATE = "2024-10-15 00:00:00,000";

    public static final String CFH_SCORE_BREAKPOINT_KEY = "TTFund.CFHCache.CFHScoreServiceImpl.syncCFHScore";

    @Autowired
    private CfhMongodbDao cfhMongodbDao;

    @Autowired
    private VerticaDao verticaDao;

    @Override
    public void syncCFHScore(String executeImmediately) {
        boolean ifSync = ifSync(executeImmediately);
        if (!ifSync) {
            return;
        }
        //查数据
        List<CFHScoreDTO> cfhScoreList = verticaDao.selectSql(VerticaSqlConstant.LIVE_TB_CFHSCORE, CFHScoreDTO.class);
        if (CollectionUtils.isEmpty(cfhScoreList)) {
            log.info("未获取到数据，不执行");
            return;
        }
        cfhScoreList.forEach(model -> model.set_id(
                model.getPTYPE() + model.getCFHID()
        ));
        int rowDeleted = cfhMongodbDao.deleteAll(CFHMongodbConstant.DB_CFH_TRADE, CFHMongodbConstant.TB_CFHSCORE);
        int rowInsert = cfhMongodbDao.insertOrUpdate(cfhScoreList, CFHMongodbConstant.DB_CFH_TRADE, CFHMongodbConstant.TB_CFHSCORE);
        log.info("删除{}条，插入{}条", rowDeleted, rowInsert);

        //更新断点
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("time", DateHelper.dateToStr(TimeUtil.getTodayEndAsDate(TimeUtil.getNowDate()), DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS_SSS));
        cfhMongodbDao.upsertCFHGeneralData(CFH_SCORE_BREAKPOINT_KEY, Arrays.asList(jsonObject));
    }

    private boolean ifSync(String executeImmediately) {
        if ("true".equals(executeImmediately)) {
            log.info("手动操作，立即执行");
            return true;
        }
        Date nowDate = TimeUtil.getNowDate();
        Date todayTwoTime = TimeUtil.getTodayTimeAsDate(nowDate, 3, 0, 0, 0);
        if (nowDate.before(todayTwoTime)) {
            log.info("未到3点，不执行");
            return false;
        }
        Date breakPoint = cfhMongodbDao.getBreakPoint(CFH_SCORE_BREAKPOINT_KEY, DEFAULT_BREAK_POINT_DATE, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS_SSS);
        if (nowDate.before(breakPoint)) {
            log.info("日期：{},已经执行过", TimeUtil.dateToStr(breakPoint, TimeUtil.FORMAT_YYYY_MM_DD));
            return false;
        }
        return true;
    }

}
