package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSON;
import com.ttfund.web.base.helper.DateHelper;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.model.dto.CFHProductReadDto;
import ttfund.web.fortuneservice.model.dto.QuarterlyReportDto;
import ttfund.web.fortuneservice.service.QuarterlyReportService;

@Service
public class QuarterlyReportServiceImpl implements QuarterlyReportService {

    @Autowired
    private CfhMongodbDao cfhMongodbDao;
    @Override
    public void quarterlyReportToProductRead(String value) {
        QuarterlyReportDto report = JSON.parseObject(value, QuarterlyReportDto.class);
        CFHProductReadDto modle = new CFHProductReadDto();
        modle.setID(String.format("%s_%s_%s", report.getReportDate(), report.getFcode(), report.getType()));
        modle.setFundCode(report.getFcode());
        modle.setFundCodeArry(new String[]{report.getFcode()});
        modle.setStatus(1);
        modle.setIsShow(1);
        modle.setShowPosition(3);
        modle.setReadType(201);
        modle.setReportType(Integer.parseInt(report.getType()));
        modle.setCreateTime(DateHelper.stringToDate2(report.getReportDate(), DateHelper.FORMAT_YYYY_MM_DD));
        modle.setStartTime(report.getCreateTime());
        modle.setEndTime(DateUtils.addDays(report.getCreateTime(), 7));
        modle.setUpdateTime(DateHelper.getNowDate());
        modle.setWarnNum(report.getWarnNum());
        cfhMongodbDao.saveProductRead(modle);
    }
}
