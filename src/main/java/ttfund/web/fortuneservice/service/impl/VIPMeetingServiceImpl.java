package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.constant.AppConfigConstant;
import ttfund.web.fortuneservice.dao.sqlserver.VIPMeetingLibMapper;
import ttfund.web.fortuneservice.dao.sqlserver.VIPMeetingMapper;
import ttfund.web.fortuneservice.manager.AIApiService;
import ttfund.web.fortuneservice.model.dto.MeetingSummaryDataDTO;
import ttfund.web.fortuneservice.model.dto.MeetingSummaryPoint;
import ttfund.web.fortuneservice.model.entity.VIPMeeting;
import ttfund.web.fortuneservice.model.entity.VIPMeetingExtend;
import ttfund.web.fortuneservice.model.entity.VIPMeetingLib;
import ttfund.web.fortuneservice.model.entity.VIPMeetingQA;
import ttfund.web.fortuneservice.service.VIPMeetingService;
import ttfund.web.fortuneservice.utils.TimeUtil;

import java.util.*;

@Service
@Slf4j
public class VIPMeetingServiceImpl implements VIPMeetingService {

    @Value("${" + AppConfigConstant.VIP_MEETING_LIB_MONTH + ":-3}")
    public Integer vipMeetingLibMonth;

    @Autowired
    private VIPMeetingMapper meetingMapper;

    @Autowired
    private VIPMeetingLibMapper libMapper;

    @Autowired
    private AIApiService aiApiService;

    @Autowired
    VIPMeetingServiceImpl self;

    @Override
    public void handleMeetingLib(String message) {
        if (StringUtils.isEmpty(message)) {
            return;
        }
        //解析数据
        VIPMeeting vipMeeting = JSON.parseObject(message, VIPMeeting.class);
        vipMeeting = meetingMapper.selectByPrimaryKey(vipMeeting.getId());
        if (vipMeeting == null || vipMeeting.getId() == null) {
            log.info("解析数据失败，message:{}", message);
            return;
        }
        //3个月之前的不处理
        Date nowDate = TimeUtil.getNowDate();
        if (DateUtils.addMonths(nowDate, vipMeetingLibMonth).after(vipMeeting.getMeetingEndTime())) {
            log.info("{}个月之前的不处理，{}", vipMeetingLibMonth, JSON.toJSONString(vipMeeting));
            return;
        }
        VIPMeetingExtend extend = new VIPMeetingExtend();
        if (StringUtils.isNotEmpty(vipMeeting.getExtend())) {
            extend = JSON.parseObject(vipMeeting.getExtend(), VIPMeetingExtend.class);
        }
        String content = extend.getMeetingContent();
        if (StringUtils.isEmpty(content)) {
            log.info("无会议纪要，{}", JSON.toJSONString(vipMeeting));
            return;
        }
        //拼AI参数
        Document doc = Jsoup.parse(content);
        content = doc.text();

        String userInput = "标题：" + extend.getMeetingTopic() + "\n" +
                "时间：" + TimeUtil.dateToStr(vipMeeting.getMeetingStartTime(), TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS) + "\n" +
                "内容：" + content;
        List<VIPMeetingQA> meetingQA = extend.getMeetingQA();
        if (!CollectionUtils.isEmpty(meetingQA)) {
            for (int i = 0; i < meetingQA.size(); i++) {
                if (i == 0) {
                    userInput += "\nQA:Q:" + meetingQA.get(i).getQ() + "\nA:" + meetingQA.get(i).getA();
                } else {
                    userInput += "\nQ:" + meetingQA.get(i).getQ() + "\nA:" + meetingQA.get(i).getA();
                }
            }
        }
        MeetingSummaryDataDTO aiContent = aiApiService.getAIContent(userInput);
        List<MeetingSummaryPoint> viewpointList = Optional.ofNullable(aiContent).map(MeetingSummaryDataDTO::getViewpointList).orElse(new ArrayList<>());
        if (CollectionUtils.isEmpty(viewpointList)) {
            log.info("AI未识别出集锦：userInput{}", userInput);
            return;
        }
        self.handlerDB(vipMeeting, viewpointList);
    }

    @Transactional
    public void handlerDB(VIPMeeting vipMeeting, List<MeetingSummaryPoint> viewpointList) {
        VIPMeeting vipMeetingUpdate = new VIPMeeting();
        vipMeetingUpdate.setId(vipMeeting.getId());
        vipMeetingUpdate.setUpdateTime(TimeUtil.getNowDate());
        vipMeetingUpdate.setAiStatus(1);
        meetingMapper.updateByPrimaryKeySelective(vipMeetingUpdate);
        //写库
        List<VIPMeetingLib> vipMeetingLibOlds = libMapper.selectByMeetingId(vipMeeting.getId());

        Set<String> idSet = new HashSet<>();
        for (MeetingSummaryPoint point : viewpointList) {
            VIPMeetingLib vipMeetingLib = new VIPMeetingLib(point, vipMeeting);
            VIPMeetingLib vipMeetingLibDB = libMapper.selectByPrimaryKey(vipMeetingLib.getId());
            idSet.add(vipMeetingLib.getId());
            if (vipMeetingLibDB == null) {
                libMapper.insertSelective(vipMeetingLib);
            } else {
                libMapper.updateByPrimaryKeySelective(vipMeetingLib);
            }
        }
        //删除旧的
        for (VIPMeetingLib vipMeetingLib : vipMeetingLibOlds) {
            if (!idSet.contains(vipMeetingLib.getId())) {
                vipMeetingLib.setDeleted(1);
                vipMeetingLib.setUpdateTime(TimeUtil.getNowDate());
                libMapper.updateByPrimaryKeySelective(vipMeetingLib);
            }
        }
    }

    @Override
    public void handleMeetingSummary(String id) {
        List<VIPMeeting> list = StringUtils.isNotEmpty(id)
                ? meetingMapper.selectById(id)
                : meetingMapper.selectSummaryList();
        if (CollectionUtils.isEmpty(list)) {
            log.info("无需处理");
            return;
        }
        for (VIPMeeting meeting : list) {

            VIPMeetingExtend extend = new VIPMeetingExtend();
            if (StringUtils.isNotEmpty(meeting.getExtend())) {
                extend = JSON.parseObject(meeting.getExtend(), VIPMeetingExtend.class);
            }
            String content = extend.getMeetingContent();
            if (StringUtils.isEmpty(content)) {
                log.info("无会议纪要，{}", JSON.toJSONString(meeting));
                continue;
            }
            log.info("开始处理会议纪要：{}", JSON.toJSONString(meeting));
            //拼AI参数
            Document doc = Jsoup.parse(content);
            String text = doc.text();

            String userInput = "标题：" + extend.getMeetingTopic() + "\n" +
                    "时间：" + TimeUtil.dateToStr(meeting.getMeetingStartTime(), TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS) + "\n" +
                    "内容：" + text;
            MeetingSummaryDataDTO aiContent = aiApiService.getAIContent(userInput);
            String aiContentStr = Optional.ofNullable(aiContent).map(MeetingSummaryDataDTO::getContent).orElse(content);
            if (aiContent == null || StringUtils.isEmpty(aiContent.getContent())) {
                log.info("AI未给出样式排版：userInput{}", userInput);
                continue;
            }
            Boolean handlerLib = true;
            Date nowDate = TimeUtil.getNowDate();
            if (DateUtils.addMonths(nowDate, vipMeetingLibMonth).after(meeting.getMeetingEndTime())) {
                handlerLib = false;
            }
            VIPMeeting vipMeetingUpdate = new VIPMeeting();
            vipMeetingUpdate.setId(meeting.getId());
            vipMeetingUpdate.setAiStatus(handlerLib ? 1 : 0);
            vipMeetingUpdate.setUpdateTime(TimeUtil.getNowDate());
            extend.setMeetingContent(aiContentStr);
            vipMeetingUpdate.setExtend(JSON.toJSONString(extend));
            meetingMapper.updateByPrimaryKeySelective(vipMeetingUpdate);
//3个月之前的不处理
            if (!handlerLib) {
                log.info("{}个月之前的不处理集锦，{}", vipMeetingLibMonth, JSON.toJSONString(meeting));
                continue;
            }
            List<VIPMeetingQA> meetingQA = extend.getMeetingQA();
            if (!CollectionUtils.isEmpty(meetingQA)) {
                for (int i = 0; i < meetingQA.size(); i++) {
                    if (i == 0) {
                        userInput += "\nQA:Q:" + meetingQA.get(i).getQ() + "\nA:" + meetingQA.get(i).getA();
                    } else {
                        userInput += "\nQ:" + meetingQA.get(i).getQ() + "\nA:" + meetingQA.get(i).getA();
                    }
                }
            }
            //处理集锦
            MeetingSummaryDataDTO aiContentViewpoint = aiApiService.getAIContent(userInput);

            List<MeetingSummaryPoint> viewpointList = aiContentViewpoint.getViewpointList();
            List<VIPMeetingLib> vipMeetingLibOlds = libMapper.selectByMeetingId(meeting.getId());
            //写库
            Set<String> idSet = new HashSet<>();
            for (MeetingSummaryPoint point : viewpointList) {
                VIPMeetingLib vipMeetingLib = new VIPMeetingLib(point, meeting);
                VIPMeetingLib vipMeetingLibDB = libMapper.selectByPrimaryKey(vipMeetingLib.getId());
                idSet.add(vipMeetingLib.getId());
                if (vipMeetingLibDB == null) {
                    libMapper.insertSelective(vipMeetingLib);
                } else {
                    libMapper.updateByPrimaryKeySelective(vipMeetingLib);
                }
            }
            //删除旧的
            for (VIPMeetingLib vipMeetingLib : vipMeetingLibOlds) {
                if (!idSet.contains(vipMeetingLib.getId())) {
                    vipMeetingLib.setDeleted(1);
                    vipMeetingLib.setUpdateTime(TimeUtil.getNowDate());
                    libMapper.updateByPrimaryKeySelective(vipMeetingLib);
                }
            }
        }
    }
}
