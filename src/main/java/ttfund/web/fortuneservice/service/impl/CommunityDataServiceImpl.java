package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSON;
import com.ttfund.web.base.helper.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.config.CommonConfig;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.dao.CfhSqlserverDao;
import ttfund.web.fortuneservice.dao.VerticaDao;
import ttfund.web.fortuneservice.manager.DongDongManager;
import ttfund.web.fortuneservice.model.dto.*;
import ttfund.web.fortuneservice.service.CommunityDataService;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CommunityDataServiceImpl implements CommunityDataService {

    @Autowired
    private VerticaDao verticaDao;

    @Autowired
    private CfhSqlserverDao cfhSqlserverDao;

    @Autowired
    private CfhMongodbDao cfhMongodbDao;

    @Autowired
    private DongDongManager dongDongManager;

    @Autowired
    private CommonConfig commonConfig;

    @Override
    public void handlerData(String param) {
        //时间参数处理
        Date now = null;
        if (StringUtils.isNotEmpty(param)) {
            if (param.startsWith("all")) {
                Date nowDate = DateHelper.getNowDate();
                String[] split = param.split("_");
                if (split.length != 2) {
                    return;
                }
                int i = Integer.valueOf(split[1]);
                for (int j = 0; j < i; j++) {
                    handlerData(DateHelper.dateToStr(DateUtils.addDays(nowDate, -j), DateHelper.FORMAT_YYYYMMDD));
                }
                return;
            }
            now = DateHelper.stringToDate2(param, DateHelper.FORMAT_YYYYMMDD);
        }
        if (now == null) {
            now = DateHelper.getNowDate();
        }
        log.info("开始处理社区数据，日期：{}", DateHelper.dateToStr(now, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS));

        //每日访问人数，处理公式：【（访问人数/社群用户总数）^（2/3）】*社群用户总数，向上取整为整数
        List<CommunityDataDayVisitDto> communityDataDayVisitors = verticaDao.getCommunityDataDayVisitors(now);
        Map<String, CommunityDataDayVisitDto> visitDtoMap = communityDataDayVisitors.stream().collect(Collectors.toMap(CommunityDataDayVisitDto::getInstitutionId, Function.identity(), (o1, o2) -> o1));
        log.info("访问人数（日）data:{}", JSON.toJSONString(communityDataDayVisitors));

        //月访问天数，不处理直接用
        List<CommunityDataDaysOfMouthDto> communityDataDaysOfMouth = verticaDao.getCommunityDataDaysOfMouth(now);
        Map<String, CommunityDataDaysOfMouthDto> mouthDtoMap = communityDataDaysOfMouth.stream().collect(Collectors.toMap(CommunityDataDaysOfMouthDto::getInstitutionId, Function.identity(), (o1, o2) -> o1));
        log.info("月访问天数data:{}", JSON.toJSONString(communityDataDaysOfMouth));

        //当日交易金额 √（实际交易金额/所有机构max交易金额）*100
        List<CommunityDataDayTradeDto> communityDataDayTreade = verticaDao.getCommunityDataDayTreade(now);
        Map<String, CommunityDataDayTradeDto> dayTradeDtoMap = communityDataDayTreade.stream().collect(Collectors.toMap(CommunityDataDayTradeDto::getInstitutionId, Function.identity(), (o1, o2) -> o1));
        log.info("当日交易金额data:{}", JSON.toJSONString(communityDataDayTreade));

        //发言数、发言人数
        List<CommunityDataDayGroupUserDto> communityDataGroupUser = verticaDao.getCommunityDataGroupUser(now);
        Map<String, CommunityDataDayGroupUserDto> groupUserDtoMap = communityDataGroupUser.stream().collect(Collectors.toMap(CommunityDataDayGroupUserDto::getInstitutionId, Function.identity(), (o1, o2) -> o1));
        log.info("发言数、发言人数data:{}", JSON.toJSONString(communityDataGroupUser));

        //群人数
        List<CommunityDataDayGroupUserNumDto> communityDataGroupUserNum = verticaDao.getCommunityDataGroupUserNum(now);
        Map<String, CommunityDataDayGroupUserNumDto> groupUserNumDtoMap = communityDataGroupUserNum.stream().collect(Collectors.toMap(CommunityDataDayGroupUserNumDto::getInstitutionId, Function.identity(), (o1, o2) -> o1));
        log.info("群人数data:{}", JSON.toJSONString(communityDataGroupUserNum));


        //出入群人数
        List<CommunityDataDayGroupUserInOutDto> communityDataGroupUserInOut = verticaDao.getCommunityDataGroupUserInOut(now);
        Map<String, CommunityDataDayGroupUserInOutDto> inOutDtoMap = communityDataGroupUserInOut.stream().collect(Collectors.toMap(CommunityDataDayGroupUserInOutDto::getInstitutionId, Function.identity(), (o1, o2) -> o1));
        log.info("出入群人数data:{}", JSON.toJSONString(communityDataGroupUserInOut));

        Set<String> institutionIdList = new HashSet<>();
        institutionIdList.addAll(communityDataDayVisitors.stream().filter(o -> StringUtils.isNotEmpty(o.getInstitutionId())).map(CommunityDataDayVisitDto::getInstitutionId).collect(Collectors.toSet()));
        institutionIdList.addAll(communityDataDaysOfMouth.stream().filter(o -> StringUtils.isNotEmpty(o.getInstitutionId())).map(CommunityDataDaysOfMouthDto::getInstitutionId).collect(Collectors.toSet()));
        institutionIdList.addAll(communityDataDayTreade.stream().filter(o -> StringUtils.isNotEmpty(o.getInstitutionId())).map(CommunityDataDayTradeDto::getInstitutionId).collect(Collectors.toSet()));
        institutionIdList.addAll(communityDataGroupUser.stream().filter(o -> StringUtils.isNotEmpty(o.getInstitutionId())).map(CommunityDataDayGroupUserDto::getInstitutionId).collect(Collectors.toSet()));
        institutionIdList.addAll(communityDataGroupUserInOut.stream().filter(o -> StringUtils.isNotEmpty(o.getInstitutionId())).map(CommunityDataDayGroupUserInOutDto::getInstitutionId).collect(Collectors.toSet()));

        log.info("需要处理的数据：{}", JSON.toJSONString(institutionIdList));
        if (CollectionUtils.isEmpty(institutionIdList)) {
            log.info("没有数据，请注意");
            //elk检查不靠谱，手动发咚咚
            String oas = commonConfig.alarmOaMap.getOrDefault("CommunityDataJob", "220089");
            dongDongManager.sendDongDongMessage(oas, "同步IM数据失败",
                    DateHelper.dateToStr(now, DateHelper.FORMAT_YYYYMMDD) + "同步IM数据失败");
            return;
        }
        //查财富号信息
        List<CFHInfoDto> cfhList = cfhSqlserverDao.selectAllCFH();

        //公司真实ID转换
        List<CFHWhiteSheetDTO> cfhWhiteSheetDTOS = cfhSqlserverDao.selectAllCFHRealCompany();
        Map<String, String> idMapping = cfhWhiteSheetDTOS.stream()
                .collect(Collectors.toMap(CFHWhiteSheetDTO::getCompanyCode, CFHWhiteSheetDTO::getRealCompanyCode));
        for (CFHInfoDto cfh : cfhList) {
            if (cfh.getStatus().equals(10)) {
                cfh.setCommpanyCode(idMapping.getOrDefault(cfh.getCommpanyCode(), cfh.getCommpanyCode()));
            }
        }
        Map<String, List<CFHInfoDto>> cfhMap = cfhList.stream().filter(o -> institutionIdList.contains(o.getCommpanyCode()) &&
                (o.getStatus() == 1 || o.getStatus() == 10)).collect(Collectors.groupingBy(CFHInfoDto::getCommpanyCode));

        log.info("财富号信息data:{}", JSON.toJSONString(cfhMap));
        List<CommunityDataDay> dataList = new ArrayList<>();
        String dateStr = DateHelper.dateToStr(DateUtils.addDays(now, -2), DateHelper.FORMAT_YYYYMMDD);
        for (String companyId : institutionIdList) {
            List<CFHInfoDto> cfhInfoDtos = cfhMap.get(companyId);
            if (CollectionUtils.isEmpty(cfhInfoDtos)) {
                log.info("没有找到财富号信息或财富号不可用:{}", companyId);
                continue;
            }
            for (CFHInfoDto cfhInfoDto : cfhInfoDtos) {
                String cfhid = cfhInfoDto.getCFHID();
                CommunityDataDayVisitDto visitDto = visitDtoMap.get(companyId);
                CommunityDataDayGroupUserDto groupUserDto = groupUserDtoMap.get(companyId);
                CommunityDataDayGroupUserInOutDto userInOutDto = inOutDtoMap.get(companyId);
                CommunityDataDaysOfMouthDto mouthDto = mouthDtoMap.get(companyId);
                CommunityDataDayTradeDto dayTradeDto = dayTradeDtoMap.get(companyId);
                CommunityDataDayGroupUserNumDto groupUserNumDto = groupUserNumDtoMap.get(companyId);
                double maxTreade = communityDataDayTreade.stream().filter(o -> StringUtils.isNotEmpty(o.getInstitutionId())).filter(o -> o.getAmount() != null).mapToDouble(o -> o.getAmount().doubleValue()).max().orElse(0);
                double speakUserMax = communityDataGroupUser.stream().filter(o -> StringUtils.isNotEmpty(o.getInstitutionId())).filter(o -> o.getSpeakUserNum() != null).mapToDouble(o -> o.getSpeakUserNum()).max().orElse(0);
                double speakTotalMax = communityDataGroupUser.stream().filter(o -> StringUtils.isNotEmpty(o.getInstitutionId())).filter(o -> o.getSpeakTotal() != null).mapToDouble(o -> o.getSpeakTotal()).max().orElse(0);
                long visitorsMax = communityDataDayVisitors.stream().filter(o -> StringUtils.isNotEmpty(o.getInstitutionId())).filter(o -> o.getDAU() != null).mapToLong(o -> o.getDAU()).max().getAsLong();
                double dayOfMouthMax = communityDataDaysOfMouth.stream().filter(o -> StringUtils.isNotEmpty(o.getInstitutionId())).filter(o -> o.getDAU() != null).mapToDouble(o -> o.getDAU()).max().orElse(0);


                //访问人数
                long visitorsNum = this.handlerVisitorsNum(visitDto,groupUserDto,groupUserNumDto);

                //交易转化分
                int payScore = this.handlerPayScore(dayTradeDto,maxTreade);

                //发言互动分
                int speakScore = this.handlerSpeakScore(speakUserMax, speakTotalMax,groupUserDto);

                //活跃分
                int activeScore = this.handlerActiveScore(dayOfMouthMax, visitorsMax, visitDto, mouthDto);

                //综合分
                int totalScore = this.handlerTotalScore(activeScore, speakScore, payScore);

                CommunityDataDay data = CommunityDataDay.builder()
                        ._id(dateStr + "_" + cfhid).date(dateStr).CFHID(cfhid).companyId(companyId)
                        .visitorsNumReal(visitDto != null ? visitDto.getDAU() : 0).visitorsNum(visitorsNum)
                        .userNum(groupUserNumDto != null ? groupUserNumDto.getGroupUserNum() : 0)
                        .speakNum(groupUserDto != null && groupUserDto.getSpeakTotal() != null ? groupUserDto.getSpeakTotal() : 0)
                        .speakUserNum(groupUserDto != null && groupUserDto.getSpeakUserNum() != null ? groupUserDto.getSpeakUserNum() : 0)
                        .userInNum(userInOutDto != null ? userInOutDto.getInNum() : 0)
                        .userOutNum(userInOutDto != null ? userInOutDto.getOutNum() : 0)
                        .visitDaysPreMonth(mouthDto != null ? mouthDto.getDAU() : 0)
                        .payScore(payScore).speakScore(speakScore).activeScore(activeScore).comprehensiveScore(totalScore)
                        .build();
                dataList.add(data);
            }
        }
        log.info("社区每日数据:{}", JSON.toJSONString(dataList));
        int rows = cfhMongodbDao.saveCommunityDataDay(dataList);
        log.info("保存社区每日数据:{}", rows);
    }

    /**
     * 综合分 活跃分50%+发言互动分25%+交易转化分25%
     * @param activeScore
     * @param speakScore
     * @param payScore
     * @return
     */
    private int handlerTotalScore(int activeScore, int speakScore, int payScore) {
        return (int) Math.round(activeScore * 0.5 + speakScore * 0.25 + payScore * 0.25);
    }

    /**
     * √（实际访问人数/所有机构max访问人数）*50+实际月访天数/所有机构max月访天数*50
     * @param dayOfMouthMax
     * @param visitorsMax
     * @param visitDto
     * @param mouthDto
     * @return
     */
    private int handlerActiveScore(double dayOfMouthMax, long visitorsMax, CommunityDataDayVisitDto visitDto, CommunityDataDaysOfMouthDto mouthDto) {
        int result = 0;
        double part1 = 0;
        double part2 = 0;
        if (visitDto != null) {
            part1 = Math.sqrt((visitDto.getDAU() != null ? visitDto.getDAU().doubleValue() : 0.0) / visitorsMax) * 50;
        }
        if (mouthDto != null) {
            part2 = (mouthDto.getDAU() != null ? mouthDto.getDAU() : 0.0) / dayOfMouthMax * 50;
        }
        result = (int) Math.round(part1 + part2);
        return result;
    }

    /**
     * √（整体发言人数/所有机构max发言人数）*50+√（整体发言消息数/所有机构max发言消息数）*50
     * @param speakUserMax
     * @param speakTotalMax
     * @param groupUserDto
     * @return
     */
    private int handlerSpeakScore(double speakUserMax, double speakTotalMax, CommunityDataDayGroupUserDto groupUserDto) {
        int result = 0;
        if (groupUserDto != null) {
            double part1 = Math.sqrt((groupUserDto.getSpeakTotal() != null ? groupUserDto.getSpeakTotal().doubleValue() : 0.0) / speakTotalMax) * 50;
            double part2 = Math.sqrt((groupUserDto.getSpeakUserNum() != null ? groupUserDto.getSpeakUserNum().doubleValue() : 0.0) / speakUserMax) * 50;
            result = (int) Math.round(part1 + part2);
        }
        return result;
    }

    /**
     * 交易转化分：√（实际交易金额/所有机构max交易金额）*100
     * @param dayTradeDto
     * @param maxTreade
     * @return
     */
    private int handlerPayScore(CommunityDataDayTradeDto dayTradeDto, double maxTreade) {
        int result = 0;
        if (dayTradeDto != null) {
            result = (int) Math.round(Math.sqrt((dayTradeDto.getAmount() != null ? dayTradeDto.getAmount().doubleValue() : 0.0) / maxTreade) * 100);
        }
        return result;
    }

    /**
     * 访问量 = 【（访问人数/社群用户总数）^（2/3）】*社群用户总数，向上取整为整数
     *
     * @param visitDto
     * @param groupUserDto
     * @param groupUserNumDto
     * @return
     */
    private Long handlerVisitorsNum(CommunityDataDayVisitDto visitDto, CommunityDataDayGroupUserDto groupUserDto, CommunityDataDayGroupUserNumDto groupUserNumDto) {
        Long result = 0L;
        if (visitDto != null && groupUserDto != null && groupUserNumDto != null) {
            int groupUserNum = Math.max(groupUserNumDto.getGroupUserNum(), 0);
            result = (long) Math.ceil(Math.pow((double) (visitDto.getDAU() == null ? 0L : visitDto.getDAU()) / groupUserNum, 2.0 / 3) * groupUserNum);
        }
        return result;
    }
}
