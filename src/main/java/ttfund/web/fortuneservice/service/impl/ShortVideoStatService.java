package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.constant.AppConfigConstant;
import ttfund.web.fortuneservice.constant.CFHMongodbConstant;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.dao.CfhSqlserverDao;
import ttfund.web.fortuneservice.model.FundCode;
import ttfund.web.fortuneservice.model.dto.*;
import ttfund.web.fortuneservice.utils.TimeUtil;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ShortVideoStatService {

    @Value("${" + AppConfigConstant.SHORT_VIDEO_RELEASE_DATA + ":2024-01-01 00:00:00}")
    public String releaseDateStr;

    @Autowired
    private CfhMongodbDao cfhMongodbDao;

    @Autowired
    private CfhSqlserverDao cfhSqlserverDao;

    public void handler(String message) {
        ObjectId id = new ObjectId();
        try {
            //备份数据
            cfhMongodbDao.saveMessage(message, CommonConstant.TOPIC_EAST_MONEY_EMAV_AV_COMMON_STATISTIC, id);
            //解析数据
            ShortVideoMessageDTO dto = JSON.parseObject(message, ShortVideoMessageDTO.class);
            if (dto == null) {
                log.info("解析数据失败，messageId:{},message:{}", id, message);
                return;
            }
            //筛选下发布日期在上线日期之后的
            Date releaseDate = TimeUtil.stringToDate2(releaseDateStr, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS);
            ShortVideoMessageDetailDTO opCtx = Optional.ofNullable(dto.getOp_ctx()).orElse(new ShortVideoMessageDetailDTO());
            ShortVideoMessageAvInfoDTO avInfo = Optional.ofNullable(opCtx.getAv_info()).orElse(new ShortVideoMessageAvInfoDTO());
            Date createTime = avInfo.getCreate_time();
            String avId = avInfo.getId();
            if (createTime == null || createTime.before(releaseDate) || StringUtils.isEmpty(avId)) {
                log.info("不满足统计条件，message:{},avId:{},cTime:{}", message, avId, createTime);
                return;
            }
            ShortVideoMessageAvAnchorInfoDTO anchor = Optional.ofNullable(avInfo.getAnchor()).orElse(new ShortVideoMessageAvAnchorInfoDTO());
            String emuid = anchor.getEmuid();
            if (StringUtils.isEmpty(emuid)) {
                log.info("emuid为空，messageId:{},message:{}", id, message);
            }
            ShortVideoMessageReportDTO shortVideoMessageReportDTO = Optional.ofNullable(opCtx.getReport_detail()).orElse(new ShortVideoMessageReportDTO());
            //全量数据
            ShortVideoMessageSummaryDTO liveSummary = shortVideoMessageReportDTO.getLive_summary();
            //增量数据
            ShortVideoMessageEdwDTO edwDelta = shortVideoMessageReportDTO.getEdw_delta();

            //查财富号信息
            CFHBaseInfoDto cfhBaseInfoDto = cfhSqlserverDao.selectByUID(emuid);
            String cfhId = cfhBaseInfoDto == null ? null : cfhBaseInfoDto.getCFHID();
            String cfhName = cfhBaseInfoDto == null ? null : cfhBaseInfoDto.getCFHName();

            ShortVideoMongoAvInfoDTO mongoDTO = new ShortVideoMongoAvInfoDTO();
            mongoDTO.set_id(avId);
            mongoDTO.setAvId(avId);
            mongoDTO.setName(avInfo.getName());
            mongoDTO.setCfhId(cfhId);
            mongoDTO.setCfhName(cfhName);
            mongoDTO.setEmUID(emuid);
            mongoDTO.setCreateTime(TimeUtil.getNowDate());
            mongoDTO.setReleaseTime(createTime);
            mongoDTO.setViewCountGuba(liveSummary != null ? liveSummary.getView_count_guba() : null);
            mongoDTO.setLikeCountGuba(liveSummary != null ? liveSummary.getLike_count_guba() : null);
            mongoDTO.setCommentCountGuba(liveSummary != null ? liveSummary.getComment_count_guba() : null);
            mongoDTO.setShareCountGuba(liveSummary != null ? liveSummary.getShare_count_guba() : null);

            //处理相关产品
            List<ShortVideoMessageAvQuoteDTO> refQuote = avInfo.getRef_quote();
            if (!CollectionUtils.isEmpty(refQuote)) {
                List<String> codes = refQuote.stream().map(ShortVideoMessageAvQuoteDTO::getQ_code).distinct().collect(Collectors.toList());
                List<FundCode> fundCodes = cfhSqlserverDao.selectFundByCodes(codes);
                codes = fundCodes.stream().map(FundCode::getFCODE).distinct().collect(Collectors.toList());
                mongoDTO.setRefFund(codes);
            }
            //观看时长
            mongoDTO.setVdPlayDur(edwDelta != null ? edwDelta.getVd_play_dur() : 0D);
            //播完数
            mongoDTO.setFinishedNum(edwDelta != null ? edwDelta.getVd_finished_num() : 0D);
            int row = cfhMongodbDao.insertInc(mongoDTO, CFHMongodbConstant.DB_TT_FUND_CFH, CFHMongodbConstant.TB_SHORTVIDEO, "vdPlayDur","finishedNum");
            log.info("消费视频数据:{}，插入{}条数据", JSON.toJSONString(mongoDTO), row);
            if (row > 0) {
                //每日数据刷新
                refreshShortVideoDayData(cfhBaseInfoDto, emuid, createTime);
            }

        } catch (Exception e) {
            log.error("消费视频数据失败,messageId:{}, error: {}", id, e.getMessage(), e);
        }
    }

    /**
     * @param cfhBaseInfoDto
     * @param emuid
     * @param createTime
     */
    private void refreshShortVideoDayData(CFHBaseInfoDto cfhBaseInfoDto, String emuid, Date createTime) {
        String cfhId = cfhBaseInfoDto == null ? null : cfhBaseInfoDto.getCFHID();
        String cfhName = cfhBaseInfoDto == null ? null : cfhBaseInfoDto.getCFHName();

        // 查每日数据
        Date startTime = TimeUtil.getTodayStartAsDate(createTime);
        Date endTime = DateUtils.addDays(startTime, 1);
        List<ShortVideoMongoAvInfoDTO> avInfoList = cfhMongodbDao.getShortVideoByAuthorAndTime(cfhId, emuid, startTime, endTime);

        if (CollectionUtils.isEmpty(avInfoList)) {
            return;
        }

        // 创建dayDTO对象并设置基本字段
        ShortVideoMongoDayDTO dayDTO = new ShortVideoMongoDayDTO();
        dayDTO.setCfhId(cfhId);
        dayDTO.setCfhName(cfhName);
        dayDTO.setUpdateTime(TimeUtil.getNowDate());
        dayDTO.setReleaseTime(createTime);
        dayDTO.setEmUID(emuid);
        dayDTO.set_id(TimeUtil.dateToStr(startTime, TimeUtil.FORMAT_YYYYMMDD) + "_" + cfhId + "_" + emuid);

        // 使用单个流求和所有字段
        BigDecimal[] totals = avInfoList.stream().reduce(new BigDecimal[]{BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO},
                (acc, o) -> {
                    acc[0] = acc[0].add(parseBigDecimal(o.getViewCountGuba()));
                    acc[1] = acc[1].add(parseBigDecimal(o.getLikeCountGuba()));
                    acc[2] = acc[2].add(parseBigDecimal(o.getCommentCountGuba()));
                    acc[3] = acc[3].add(parseBigDecimal(o.getShareCountGuba()));
                    return acc;
                },
                (a, b) -> {
                    a[0] = a[0].add(b[0]);
                    a[1] = a[1].add(b[1]);
                    a[2] = a[2].add(b[2]);
                    a[3] = a[3].add(b[3]);
                    return a;
                });

        // 设置结果到dayDTO
        dayDTO.setViewCountGuba(totals[0].setScale(0, BigDecimal.ROUND_HALF_UP).toString());
        dayDTO.setLikeCountGuba(totals[1].setScale(0, BigDecimal.ROUND_HALF_UP).toString());
        dayDTO.setCommentCountGuba(totals[2].setScale(0, BigDecimal.ROUND_HALF_UP).toString());
        dayDTO.setShareCountGuba(totals[3].setScale(0, BigDecimal.ROUND_HALF_UP).toString());
        cfhMongodbDao.insertInc(dayDTO, CFHMongodbConstant.DB_TT_FUND_CFH, CFHMongodbConstant.TB_SHORTVIDEO_DAY);
    }

    // 辅助方法，处理空字符串或非数字的情况
    private BigDecimal parseBigDecimal(String value) {
        return StringUtils.isNotEmpty(value) ? new BigDecimal(value) : BigDecimal.ZERO;
    }
}
