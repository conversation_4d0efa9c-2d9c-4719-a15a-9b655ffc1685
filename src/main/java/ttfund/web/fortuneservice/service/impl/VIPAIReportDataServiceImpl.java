package ttfund.web.fortuneservice.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.dao.sqlserver.VIPAIReportDetailMapper;
import ttfund.web.fortuneservice.dao.sqlserver.VIPAIReportMapper;
import ttfund.web.fortuneservice.model.dto.MonthlyReportRequestDTO;
import ttfund.web.fortuneservice.model.dto.MonthlyReportResponseDTO;
import ttfund.web.fortuneservice.model.entity.VIPAIReport;
import ttfund.web.fortuneservice.model.entity.VIPAIReportDetail;
import ttfund.web.fortuneservice.service.VIPAIReportDataService;
import ttfund.web.fortuneservice.utils.CommonUtil;
import ttfund.web.fortuneservice.utils.TimeUtil;
import ttfund.web.fortuneservice.constant.VIPAIReportConstant;

import java.util.ArrayList;
import java.util.List;

/**
 * VIP AI报告数据服务实现类
 */
@Service
@Slf4j
public class VIPAIReportDataServiceImpl implements VIPAIReportDataService {

    @Autowired
    private VIPAIReportMapper vipAIReportMapper;
    
    @Autowired
    private VIPAIReportDetailMapper vipAIReportDetailMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveReportToDatabase(MonthlyReportRequestDTO request, MonthlyReportResponseDTO response, String cfhid, String taskConfigId, String taskCycleMark, int index) {
        if (request == null || response == null) {
            log.error("保存报告失败：请求或响应数据为空");
            throw new IllegalArgumentException("请求或响应数据不能为空");
        }

        if (StringUtils.isEmpty(cfhid) || StringUtils.isEmpty(taskConfigId)) {
            log.error("保存报告失败：CFHID或TaskConfigID为空，CFHID：{}，TaskConfigID：{}", cfhid, taskConfigId);
            throw new IllegalArgumentException("CFHID和TaskConfigID不能为空");
        }

        log.info("开始保存报告到数据库，机构：{}，月份：{}，CFHID：{}，TaskConfigID：{}",
                request.getInstitutionName(), request.getTaskMonth(), cfhid, taskConfigId);
        
        try {
            // 1. 保存主报告记录
            VIPAIReport report = new VIPAIReport();
            report.setId(CommonUtil.getGuId(32));
            report.setCfhid(cfhid);
            report.setTaskConfigId(taskConfigId);
            report.setTaskCycleMark(taskCycleMark);
            report.setReportDate(request.getTaskMonth());
            report.setStatus(VIPAIReportConstant.Status.PENDING); // 待审核
            report.setIndex(index); // 设置批次内序号
            report.setCreateTime(TimeUtil.getNowDate());
            report.setUpdateTime(TimeUtil.getNowDate());
            
            vipAIReportMapper.insertSelective(report);
            
            // 2. 保存详细信息
            List<VIPAIReportDetail> details = new ArrayList<>();
            
            // 整体总结
            if (StringUtils.isNotEmpty(response.getInstitutionSummary())) {
                VIPAIReportDetail overallDetail = createReportDetail(
                    report.getId(), "整体总结", response.getInstitutionSummary(), VIPAIReportConstant.ReportType.OVERALL);
                details.add(overallDetail);
            }
            
            // 模块总结
            if (response.getModuleSummary() != null) {
                MonthlyReportResponseDTO.ModuleSummary moduleSummary = response.getModuleSummary();
                
                // 热门行业
                if (StringUtils.isNotEmpty(moduleSummary.getIndustrySummary())) {
                    VIPAIReportDetail industryDetail = createReportDetail(
                        report.getId(), "热门行业", moduleSummary.getIndustrySummary(), "HOT_INDUSTRY");
                    details.add(industryDetail);
                }
                
                // 资产研判
                if (StringUtils.isNotEmpty(moduleSummary.getOtherCategorySummary())) {
                    VIPAIReportDetail assetAnalysisDetail = createReportDetail(
                        report.getId(), "资产研判", moduleSummary.getOtherCategorySummary(), "ASSET_ANALYSIS");
                    details.add(assetAnalysisDetail);
                }
                
                // 配置建议
                if (StringUtils.isNotEmpty(moduleSummary.getAssetAllocationSummary())) {
                    VIPAIReportDetail allocationDetail = createReportDetail(
                        report.getId(), "配置建议", moduleSummary.getAssetAllocationSummary(), "ALLOCATION_ADVICE");
                    details.add(allocationDetail);
                }
                
                // 宏观经济
                if (StringUtils.isNotEmpty(moduleSummary.getMacroEconomicSummary())) {
                    VIPAIReportDetail macroDetail = createReportDetail(
                        report.getId(), "宏观经济", moduleSummary.getMacroEconomicSummary(), "MACRO_ECONOMY");
                    details.add(macroDetail);
                }
            }
            
            // 批量插入详细信息
            if (!CollectionUtils.isEmpty(details)) {
                vipAIReportDetailMapper.batchInsert(details);
            }
            
            log.info("成功保存报告到数据库，报告ID：{}，详细信息条数：{}", report.getId(), details.size());
            
        } catch (Exception e) {
            log.error("保存报告到数据库异常", e);
            throw new RuntimeException("保存报告到数据库异常", e);
        }
    }
    
    /**
     * 创建报告详细信息
     */
    private VIPAIReportDetail createReportDetail(String reportId, String title, String content, String reportType) {
        VIPAIReportDetail detail = new VIPAIReportDetail();
        detail.setId(CommonUtil.getGuId(32));
        detail.setReportId(reportId);
        detail.setTitle(title);
        detail.setContent(content);
        detail.setReferences(null); // 单机构不用
        detail.setReportType(reportType);
        detail.setType(VIPAIReportConstant.ReportType.SINGLE_ORG); // 单机构
        detail.setCreateTime(TimeUtil.getNowDate());
        detail.setUpdateTime(TimeUtil.getNowDate());
        return detail;
    }
}
