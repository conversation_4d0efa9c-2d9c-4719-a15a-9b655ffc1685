package ttfund.web.fortuneservice.service.impl;

import cn.hutool.core.text.StrBuilder;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.constant.VerticaSqlConstant;
import ttfund.web.fortuneservice.constant.VerticaTableNameConstant;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.dao.CfhTradeMongodbDao;
import ttfund.web.fortuneservice.dao.VerticaDao;
import ttfund.web.fortuneservice.model.dto.FundApplyDataStatModel;
import ttfund.web.fortuneservice.model.dto.TradeDetailDTO;
import ttfund.web.fortuneservice.model.dto.TradeDetailRecord;
import ttfund.web.fortuneservice.utils.TimeUtil;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年12月18日
 * 财富号交易数据增量同步服务
 */
@Slf4j
@Service
public class CFHTradeDataKafkaRelocateService {

    @Resource
    VerticaDao verticaDao;

    @Resource
    private CfhTradeMongodbDao cfhTradeMongodbDao;

    @Resource
    private CfhMongodbDao cfhMongodbDao;

    public void sync(List<TradeDetailDTO> messages) {
        //保存消息
        List<List<TradeDetailDTO>> lists = Lists.partition(messages, 500);
        for (List<TradeDetailDTO> list : lists) {
            cfhMongodbDao.saveMessage(JSON.toJSONString(list), CommonConstant.FUND_CFH_TRANSACTION_MESSAGE, null);
        }

        Map<Integer, List<TradeDetailRecord>> actionMap = messages.stream()
                .collect(Collectors.groupingBy(
                        TradeDetailDTO::getAction,
                        Collectors.mapping(o -> o.getBusinApp(), Collectors.toList())
                ));
        List<TradeDetailRecord> insertList = actionMap.getOrDefault(0, Collections.emptyList());
        List<TradeDetailRecord> updateList = actionMap.getOrDefault(1, Collections.emptyList());
        List<TradeDetailRecord> revertList = actionMap.getOrDefault(2, Collections.emptyList());

        //交易日
        Set<Date> transDates = messages.stream().map(o -> o.getBusinApp().getTransactionDate()).filter(o -> o != null).collect(Collectors.toSet());
        boolean insertFlag = true;
        boolean updateFlag = true;
        boolean revertFlag = true;
        if (!CollectionUtils.isEmpty(insertList)) {
            Set<String> applyNos = insertList.stream().filter(o -> StringUtils.isNotEmpty(o.getApplyNo())).map(o -> o.getApplyNo()).collect(Collectors.toSet());

            StringJoiner sqlJoiner = new StringJoiner(",", "select distinct APPLYNO from BUSIN.TTTRADEDETAIL_BASIC_SYN where APPLYNO in (", ")");
            applyNos.forEach(applyNo -> sqlJoiner.add("?"));

            List<String> tradeDetailRecords = verticaDao.selectSql(sqlJoiner.toString(), new ArrayList<>(applyNos), String.class);

            insertList = insertList.stream().filter(o -> !tradeDetailRecords.contains(o.getApplyNo())).collect(Collectors.toList());
            insertFlag = verticaDao.insertTradeDetails(insertList);
        }
        if (!CollectionUtils.isEmpty(updateList)) {
            for (TradeDetailRecord record : updateList) {
                verticaDao.updateTradeDetailByApplyNo(record);
            }
            log.info("更新了{}条数据到表{}中",updateList.size(), VerticaTableNameConstant.TTTRADEDETAIL_BASIC_SYN);
            XxlJobLogger.log("更新了{}条数据到表{}中",updateList.size(), VerticaTableNameConstant.TTTRADEDETAIL_BASIC_SYN);
        }
        if (!CollectionUtils.isEmpty(revertList)) {
            revertFlag = verticaDao.revertTradeDetailByApplyNos(revertList);
        }
        for (Date transDate : transDates) {
            //统计
            List<FundApplyDataStatModel> dataStatModels = verticaDao.statTradeData(transDate);

            if (CollectionUtils.isEmpty(dataStatModels)) {
                log.error("统计交易数据结果为空:{}", TimeUtil.dateToStr(transDate, TimeUtil.FORMAT_YYYY_MM_DD));
                XxlJobLogger.log("统计交易数据结果为空:" + TimeUtil.dateToStr(transDate, TimeUtil.FORMAT_YYYY_MM_DD));
                continue;
            }
            //回写vertica
            boolean flag2 = verticaDao.delByDayAndInsertData(VerticaSqlConstant.SQL_DELETE_STAT_DATA_BY_TRANSACTION_DAY
                    , VerticaSqlConstant.SQL_INSERT_STAT_DATA, transDate, CFHTradeDataRelocateCommonService.getInsertStatDataParamList(dataStatModels));

            //写mongoDB
            cfhTradeMongodbDao.updateAndInsertCFHData(transDate, dataStatModels);
        }
    }

}
