package ttfund.web.fortuneservice.service.impl;

import com.ttfund.web.base.helper.DateHelper;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import ttfund.web.fortuneservice.config.CommonConfig;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.manager.HqApiManager;
import ttfund.web.fortuneservice.model.bo.PopularManagerBo;
import ttfund.web.fortuneservice.model.dto.CfhListDto;
import ttfund.web.fortuneservice.model.dto.QuickReviewDto;
import ttfund.web.fortuneservice.service.FundManagerService;
import ttfund.web.fortuneservice.utils.CommonUtil;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

@Service
public class FundManagerServiceImpl implements FundManagerService {

    Logger logger = LoggerFactory.getLogger(FundManagerServiceImpl.class);

    @Resource
    private CommonConfig config;

    @Resource
    private HqApiManager hqApiManager;

    @Resource
    private CfhMongodbDao cfhMongodbDao;

    @Override
    public void fundManagerHandler() {
        // 1、查询前10天数据
        List<QuickReviewDto> reviewList = this.getQuickReviewList();
        if (reviewList != null && !reviewList.isEmpty()) {
            // 同一天内存在卡片为同一财富号ID, 仅展示最新的一条卡片
            Map<String, QuickReviewDto> map = reviewList.stream().collect(
                    groupingBy(o -> o.getCFHID() + ":" + o.getUpdateDay(),
                            Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            Collection<QuickReviewDto> useReviewList = map.values();
            Map<String, List<QuickReviewDto>> reviewMap = useReviewList.stream().collect(Collectors.groupingBy(QuickReviewDto::getMGRID));
            // 2、获取机构手动权限
            Map<String, CfhListDto> weightMap = this.getCfhListMap(useReviewList);
            // 3、逻辑处理后，添加实体对象
            List<PopularManagerBo> managerList = this.addManagerList(reviewMap, weightMap);
            // 自然排序 - 排序规则：自然排序权重+手动权重=总权重，按权重倒叙
            managerList.sort(Comparator.comparing(PopularManagerBo::getLastTimePoint).reversed());
            // 4、权重处理
            List<PopularManagerBo> result = this.handleWeight(managerList);
            // 按照最新排序
            result.sort(Comparator.comparing(PopularManagerBo::getWeight).reversed());
            if (!result.isEmpty()) {
                result = result.subList(0, Math.min(result.size(), 10));
            }
            // 5、写入财富号通用数据表
            logger.info("人气大咖缓存, 存入mongo：{}",result);
            boolean upsertRes = cfhMongodbDao.upsertCFHGeneralData(CommonConstant.HOT_MANAGER_LIST, result);
            if (!upsertRes){
                logger.warn("存入mongo失败");
            }
        }
    }

    /**
     * 查询前10天 天天快评数据
     * <AUTHOR>
     * @date 2023/4/10 9:40
     * @return java.util.List
     */
    private List<QuickReviewDto> getQuickReviewList() {
        // 当前时间
        Date nowDate = DateHelper.getNowDate();
        // 查交易日库时间、处理命中缓存
        Date tradeDayPreDate = DateHelper.stringToDate2(DateHelper.dateToStr(nowDate, DateHelper.FORMAT_YYYY_MM_DD), DateHelper.FORMAT_YYYY_MM_DD);
        tradeDayPreDate = DateUtils.addSeconds(tradeDayPreDate, 1);

        // 查询结束时间
        Long timePointEnd = CommonUtil.getTimePoint(nowDate);
        // 10天前时间，查询开始i时间
        Long timePointBegin = CommonUtil.getTimePoint(hqApiManager.getTradeDayPre(tradeDayPreDate, config.managerDays));

        // 前10天数据
        return cfhMongodbDao.getQuickReview(timePointBegin, timePointEnd, 0, null);
    }

    /**
     * 获取机构手动权限
     * <AUTHOR>
     * @date 2023/4/10 9:51
     * @param useReviewList useReviewList
     * @return java.util.Map
     */
    private Map<String, CfhListDto> getCfhListMap(Collection<QuickReviewDto> useReviewList) {
        List<String> cfhIds = useReviewList.stream().map(QuickReviewDto::getCFHID).distinct().collect(Collectors.toList());
        List<CfhListDto> cfhWeight = cfhMongodbDao.getCFHWeight(cfhIds);
        return cfhWeight.stream().collect(Collectors.toMap(CfhListDto::getCFHID, Function.identity()));
    }

    /**
     * 逻辑处理后，添加实体对象
     * <AUTHOR>
     * @date 2023/4/10 10:44
     * @param reviewMap 天天快评数据
     * @param weightMap 权重
     * @return java.util.List
     */
    private List<PopularManagerBo> addManagerList(Map<String, List<QuickReviewDto>> reviewMap, Map<String, CfhListDto> weightMap) {
        List<PopularManagerBo> managerList = new ArrayList<>();
        for (Map.Entry<String, List<QuickReviewDto>> entry : reviewMap.entrySet()) {
            // 满足条件阈值加进去
            if (entry.getValue().size() >= config.gteNum) {
                // 找最近的时间
                Optional<QuickReviewDto> max = entry.getValue().stream().max(Comparator.comparing(QuickReviewDto::getTimepoint));
                if (!max.isPresent()) {
                    continue;
                }
                QuickReviewDto lastReview = max.get();
                //找手动权重
                CfhListDto cfh = weightMap.get(lastReview.getCFHID());
                Integer weight = 0;
                if (ifAddWeight(entry.getValue(), cfh)) {
                    weight = cfh.getDefaultWeight();
                }
                managerList.add(new PopularManagerBo(lastReview.getCFHID(), lastReview.getMGRID(),
                        lastReview.getMGRName(), entry.getValue(), lastReview.getTimepoint(), weight));
            }
        }
        return managerList;
    }

    /**
     * 权重处理
     * <AUTHOR>
     * @date 2023/4/10 11:00
     * @param managerList 快评数据集合
     * @return java.util.List
     */
    private List<PopularManagerBo> handleWeight(List<PopularManagerBo> managerList) {
        List<PopularManagerBo> result = new ArrayList<>();
        int weight = 10;
        HashMap<String,Integer> cfhIdMap = new HashMap<>();
        for (PopularManagerBo manager : managerList) {
            weight = Math.max(weight, 0);
            String cfhId = manager.getCFHID();
            if (cfhIdMap.getOrDefault(cfhId,0) > 0) {
                continue;
            }
            cfhIdMap.put(cfhId, cfhIdMap.getOrDefault(cfhId, 0) + 1);
            manager.setWeight(weight + manager.getHandWeight());
            result.add(manager);
            weight--;
        }
        return result;
    }

    /**
     * 是否需要添加权重
     * <p>只需要用户在给定时间范围内发过快评就加</p>
     * <AUTHOR>
     * @date 2023/4/10 11:03
     * @return boolean
     */
    private boolean ifAddWeight(List<QuickReviewDto> value, CfhListDto cfh) {
        // 只需要用户在给定时间范围内发过快评就加
        if (cfh == null) {
            return false;
        }
        long weightStartTime = cfh.getWeightStartTimeStamp() == null ? 0L : cfh.getWeightStartTimeStamp();
        long weightEndTime = cfh.getWeightEndTimeStamp() == null ? 0L : cfh.getWeightEndTimeStamp();
        for (QuickReviewDto review : value) {
            Long timePoint = review.getTimepoint();
            if (timePoint == null) {
                continue;
            }
            String substring = timePoint.toString().substring(0, Math.min(13, timePoint.toString().length()));
            timePoint = Long.valueOf(substring);
            if (timePoint > weightStartTime && timePoint < weightEndTime) {
                return true;
            }
        }
        return false;
    }
}
