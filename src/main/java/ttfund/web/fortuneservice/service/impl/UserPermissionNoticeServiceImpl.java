package ttfund.web.fortuneservice.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ttfund.web.fortuneservice.constant.NoticeTypeEnum;
import ttfund.web.fortuneservice.dao.CfhSqlserverDao;
import ttfund.web.fortuneservice.model.dto.CFHInfoDto;
import ttfund.web.fortuneservice.model.dto.TaskNoticeDto;
import ttfund.web.fortuneservice.service.UserPermissionNoticeService;
import ttfund.web.fortuneservice.utils.TimeUtil;

import java.util.*;

@Slf4j
@Service
public class UserPermissionNoticeServiceImpl implements UserPermissionNoticeService {

    @Autowired
    private CfhSqlserverDao sqlServer;

    @Override
    public void creat(String date) {
        Date fistDayOfQuarter = getFistDayOfQuarter(date);

        List<CFHInfoDto> cfhInfoDtos = sqlServer.selectAllCFH();
        Set<String> success = new HashSet<>();
        for (CFHInfoDto dto : cfhInfoDtos) {
            if (dto != null && StringUtils.isNotEmpty(dto.getRelatedUid()) && StringUtils.isNotEmpty(dto.getRoleId())) {
                TaskNoticeDto notice = TaskNoticeDto.builder().id(dto.getCFHID() + "_" + TimeUtil.dateToStr(fistDayOfQuarter, TimeUtil.FORMAT_YYYYMMDD))
                        .cfhId(dto.getCFHID()).content("【账号权限管理提醒】 尊敬的管理员，您好！请定期检查账号权限并及时注销非在职人员的账号，确保所有账号的拥有者均为在职员工。如有运营人员变动请及时创建新账号，避免多人共用同一账号，以维护数据安全。感谢您的配合！")
                        .title("【账号权限管理提醒】请定期检查账号权限").status(1).mgrName("Java服务").createTime(TimeUtil.getNowDate()).updateTime(TimeUtil.getNowDate())
                        .type(NoticeTypeEnum.SYSTEM_NOTICE.getType()).build();
                try {
                    boolean saveNotice = sqlServer.insertNotice(notice);
                    if (saveNotice) {
                        success.add(dto.getCFHID());
                    }
                } catch (Exception e) {
                    continue;
                }
            }
        }
        log.info("【账号权限管理提醒】成功通知：{}", success);
    }

    private Date getFistDayOfQuarter(String date) {
        Date nowDate = null;
        if (StringUtils.isNotEmpty(date)) {
            try {
                nowDate = TimeUtil.stringToDate2(date, TimeUtil.FORMAT_YYYYMM);
            } catch (Exception e) {
                log.warn("日期格式错误,要求格式：{}，参数:{}", TimeUtil.FORMAT_YYYYMM, date);
            }
        }
        if (nowDate == null) {
            nowDate = TimeUtil.getNowDate();
        }

        // 获取当前季度的第一天
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(nowDate);

        int month = calendar.get(Calendar.MONTH); // 获取当前月份（0-11）
        int quarterStartMonth = (month / 3) * 3; // 计算当前季度的起始月份（0, 3, 6, 9）

        calendar.set(Calendar.MONTH, quarterStartMonth); // 设置为季度起始月份
        calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为月份的第一天
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();

    }

}
