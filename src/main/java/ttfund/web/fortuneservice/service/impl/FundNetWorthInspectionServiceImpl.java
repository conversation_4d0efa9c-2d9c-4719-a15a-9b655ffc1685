package ttfund.web.fortuneservice.service.impl;

import com.ttfund.web.base.helper.DateHelper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.constant.ProductCheckTypeEnum;
import ttfund.web.fortuneservice.constant.ReportTypeEnum;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.dao.VerticaDao;
import ttfund.web.fortuneservice.manager.QSApiManager;
import ttfund.web.fortuneservice.model.dto.*;
import ttfund.web.fortuneservice.service.FundNetWorthInspectionService;
import ttfund.web.fortuneservice.utils.CommonUtil;
import ttfund.web.fortuneservice.utils.ProductCheckUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * FundNetWorthInspectionServiceImpl.java
 *
 * <AUTHOR>
 * @date 2023/5/11 11:51
 */
@Service
public class FundNetWorthInspectionServiceImpl implements FundNetWorthInspectionService {

    private Logger logger = LoggerFactory.getLogger(FundNetWorthInspectionServiceImpl.class);

    @Resource
    VerticaDao verticaDao;

    @Resource
    CfhMongodbDao cfhMongodbDao;

    @Autowired
    private QSApiManager qsApiManager;

    @Override
    public boolean inspectionService(String param) {
        Date nowDate;
        if (!StringUtils.isEmpty(param)) {
            nowDate = DateHelper.stringToDate2(param, DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
        } else {
            nowDate = DateHelper.getNowDate();
        }

        StringBuilder log = new StringBuilder("开始检测私募基金净值，");
        // 1.判断当前日期是否为交易日
        List<DaysInfoMongoDto> daysInfoList = cfhMongodbDao.getDaysInfoByTimeRange(CommonUtil.getFirstDayOfMonth(nowDate), CommonUtil.getLastDayOfMonth(nowDate));
        if (!ProductCheckUtil.checkIsTradeDay(nowDate, daysInfoList)) {
            log.append(String.format("当前日期为%s，非交易日，今日不需要进行私募净值检查。", DateHelper.dateToStr(nowDate, DateHelper.FORMAT_YYYY_MM_DD)));
            logger.info(log.toString());
            return true;
        }
        // 2.计算当前时间是第几个交易日
        int tradeDayNum = ProductCheckUtil.calculateTradeDayNumber(nowDate, daysInfoList, false);
        log.append(String.format("当前日期为%s，当前日期为所在月的第%s个交易日，", DateHelper.dateToStr(nowDate, DateHelper.FORMAT_YYYY_MM_DD), tradeDayNum));

        // 3.检测一：每月结束后6个工作日（T+1）
        if (tradeDayNum == 6) {
            logger.info(log.toString());
            return this.netWorthInspectionService(nowDate, 1);
        }
        // 4.检测二：每季度结束后11个工作日（T+1）   先计算是否为每季度开始月份
        if (CommonUtil.isFirstMonthOfQuarter(nowDate)) {
            log.append(String.format("当前为所在季度的第%s天。", tradeDayNum));
            if (tradeDayNum == 11) {
                logger.info(log.toString());
                return this.netWorthInspectionService(nowDate, 2);
            }
        }
        log.append("今日不需要进行私募净值检查。");
        logger.info(log.toString());
        return true;
    }

    /**
     * 净值检测方法
     *
     * @param date 检测日期
     * @param type 检测类型 type=1:每月结束后6个工作日 / type=2:每季度结束后11个工作日
     * @return 检测结果
     **/
    private boolean netWorthInspectionService(Date date, Integer type) {
        boolean flag = true;
        StringBuilder log = new StringBuilder(type == 1 ? "本次检测产品为符合基金规模条件的私募基金，" : "本次检测产品为所有私募基金，");
        try {
            // 1.获取私募基金
            List<FundHighLevelInfo> siMUFund = verticaDao.getFundCodeType().stream().filter(m -> (m.getIsPvt().equals(1))).collect(Collectors.toList());
            log.append(type == 1 ? "本次检测产品为符合基金规模条件的私募基金，" : "本次检测产品为所有私募基金，");
            log.append("共需检测").append(siMUFund.size()).append("个私募基金的最新净值披露日期，");
            List<String> siMUFundCodes = siMUFund.stream().map(FundHighLevelInfo::getFundCode).collect(Collectors.toList());

            // 2.计算私募基金的规模
            List<FundSizeDto> siMuFundSizeInfo = verticaDao.getFundSizeInfo(siMUFundCodes);
            log.append("共计算出").append(siMuFundSizeInfo.size()).append("个私募基金的基金规模，");
            // 筛选符合基金规模条件的私募基金
            siMuFundSizeInfo = this.filterFundSizeInfo(siMUFund, siMuFundSizeInfo, type);
            Map<String, FundSizeDto> map = siMuFundSizeInfo.stream().collect(Collectors.toMap(FundSizeDto::getFundCode, Function.identity()));

            // 3.检测是否有有效的净值报告 - 有且在指定日期之前披露
            List<FundNetWorthDto> fundNetWorthInfo = verticaDao.getFundNetWorthInfo(new ArrayList<>(map.keySet()));
            Date lastTradeDay = this.getLastTradeDay(date);
            List<String> qualifiedFundCodes = fundNetWorthInfo.stream()
                    .filter(obj -> obj != null && obj.getPDate() != null && (!obj.getPDate().before(lastTradeDay)))
                    .map(FundNetWorthDto::getFundCode)
                    .collect(Collectors.toList());
            qualifiedFundCodes.forEach(map::remove);
            log.append("经检测共有").append(map.size()).append("个私募基金未在指定日期").append(lastTradeDay).append("之前披露净值报告，");

            //4.1 获取持仓状态基金列表
            List<String> holdFundList = qsApiManager.getHoldFundList(map.keySet().stream().collect(Collectors.toList()));

            // 4.保存净值报告、净值报告内容
            if (!CollectionUtils.isEmpty(map)) {
                String reportName = this.getReportName(type, date);
                flag = this.saveInspectionResult(new ArrayList<>(map.values()), date, reportName,holdFundList,siMUFund,fundNetWorthInfo);
                if (!flag) {
                    logger.info(log.toString());
                    return flag;
                } else {
                    log.append("共生成1个检测报告，并保存").append(map.size()).append("条检测内容，检测报告及报告内容保存成功，");
                }
                //5.发清算告警
//                boolean sendResult = qsApiManager.sendMessage("【基金数据】高端理财有新报告", reportName);
//                log.append("清算告警结果：【").append(sendResult).append("】，");
            }
            log.append("本次检测成功。");
        } catch (Exception e) {
            log.append("本次检测执行失败。");
            logger.error(e.getMessage());
            flag = false;
        }
        logger.info(log.toString());
        return flag;
    }

    /**
     * 筛选符合基金规模条件的私募基金
     *
     * @param fundCodeTypes 私募基金 代处理的所有私募基金
     * @param fundSizeInfo  私募基金 从FUND.TB_ZD07表查到的且含有规模信息
     * @return 筛选结果
     **/
    private List<FundSizeDto> filterFundSizeInfo(List<FundHighLevelInfo> fundCodeTypes, List<FundSizeDto> fundSizeInfo, int type) {
        if (CollectionUtils.isEmpty(fundCodeTypes)) {
            return new ArrayList<>();
        }
        List<FundSizeDto> fundSizeInfoFiltered = new ArrayList<>();
        if (type == 1) {
            // 筛选条件：规模金额达到 5000 万元以上/金额为0元/金额未知-C_TOTALFUNDVOL或C_NAV字段为空
            fundSizeInfoFiltered = fundSizeInfo.stream().filter(item -> {
                BigDecimal fundSize = item != null ? item.getFundSize() : null;
                return fundSize == null || fundSize.compareTo(BigDecimal.ZERO) == 0 || fundSize.compareTo(BigDecimal.valueOf(50000000)) > 0;
            }).collect(Collectors.toList());
        }
        if (type == 2) {
            // 不加任何筛选：从FUND.TB_ZD07表查到的且含有规模信息
            fundSizeInfoFiltered.addAll(fundSizeInfo);
        }
        // 基金规模未知-在FUND.TB_ZD07表中无法查询到私募基金
        List<String> discovered = fundSizeInfo.stream().map(FundSizeDto::getFundCode).collect(Collectors.toList());
        List<FundSizeDto> finalFundSizeInfoFiltered = fundSizeInfoFiltered;
        fundCodeTypes.forEach(item -> {
            // 无法查到的仍然要检测净值信息
            if (!discovered.contains(item.getFundCode())) {
                finalFundSizeInfoFiltered.add(FundSizeDto.builder().fundCode(item.getFundCode()).fundName(item.getFundName()).build());
            }
        });
        return finalFundSizeInfoFiltered;
    }

    /**
     * 获取上一个月的最后一个交易日
     * 说明：在任务执行的时候，确定了季度检测只会在1，4，7，9月份执行，故[最新基金净值日期是否在上季度最后一个交易日之前]改变成[是否在上一个月最后一个交易日之前]
     *      月度检测本身就是检测上一个月是否会执行
     *
     * @param date 指定日期
     * @return 交易日
     **/
    private Date getLastTradeDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        // 计算最后一天以前最近的一个交易日
        return DateHelper.stringToDate2(cfhMongodbDao.getLastTradeDay(calendar.getTime()), CommonConstant.FORMAT_YYYY_M_D_H_MM_SS);
    }

    /**
     * 获取报告名称
     *
     * @param type 检测类型
     * @param date 指定日期
     * @return 交易日
     **/
    private String getReportName(Integer type, Date date) {
        StringBuilder reportName = new StringBuilder("YQ_");
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        if (type == 1) {
            cal.add(Calendar.MONTH, -1);
            reportName.append(DateHelper.dateToStr(cal.getTime(), DateHelper.FORMAT_YYYYMM)).append("simu").append("_jz");
        }
        if (type == 2) {
            int currentMonth = cal.get(Calendar.MONTH);
            // 计算当前月份距离上一个季度结束还有多少个月
            int offset = (currentMonth % 3 == 0) ? 3 : (currentMonth % 3);
            cal.add(Calendar.MONTH, -offset);
            // 计算上一个季度的季度数
            int lastQuarter = (cal.get(Calendar.MONTH) / 3) + 1;
            reportName.append(cal.get(Calendar.YEAR)).append("Q").append(lastQuarter).append("simu").append("_jz");
        }
        return reportName.toString();
    }

    /**
     * 保存检测报告及报告内容
     *
     * @param list             基金机构的基金规模等信息
     * @param nowDate          当前时间
     * @param reportName       报告名称
     * @param holdFundList
     * @param siMUFund
     * @param fundNetWorthInfo
     * @return 保存结果
     **/
    private boolean saveInspectionResult(List<FundSizeDto> list, Date nowDate, String reportName, List<String> holdFundList, List<FundHighLevelInfo> siMUFund, List<FundNetWorthDto> fundNetWorthInfo) {
        List<RegularInspectionReportContentDto> reportContentList = new ArrayList<>(list.size());
        String reportId = CommonUtil.getGuId(32);
        // 生成报告实体
        RegularInspectionReportDto report = RegularInspectionReportDto.builder()
                .reportId(reportId)
                .reportName(reportName)
                .reportType(ReportTypeEnum.SIMUJZ_YQ.getType())
                .checkTime(nowDate)
                .build();
        // 生成报告内容实体
        Map<String, FundHighLevelInfo> fundInfoMap = siMUFund.stream().collect(Collectors.toMap(FundHighLevelInfo::getFundCode, Function.identity(), (k1, k2) -> k1));
        Map<String, FundNetWorthDto> pDateMap = fundNetWorthInfo.stream().collect(Collectors.toMap(FundNetWorthDto::getFundCode, Function.identity(), (k1, k2) -> k1));
        list.forEach(entry -> {
            FundHighLevelInfo fundInfo = fundInfoMap.get(entry.getFundCode());
            FundNetWorthDto pDate = pDateMap.get(entry.getFundCode());
            RegularInspectionReportContentDto reportContent = RegularInspectionReportContentDto.builder()
                    .reportId(reportId)
                    .reportContentId(CommonUtil.getGuId(32))
                    .createTime(nowDate)
                    .fundSize(entry.getFundSize() == null ? null : entry.getFundSize().doubleValue())
                    .missingInformationType(ProductCheckTypeEnum.NAV.getType())
                    .productCode(entry.getFundCode())
                    .productName(entry.getFundName())
                    .isHold(holdFundList.contains(entry.getFundCode()))
                    .stabilshDate(fundInfo == null ? null : fundInfo.getEstablishmentTime())
                    .manager(fundInfo == null ? null : fundInfo.getManager())
                    .pDate(pDate == null ? null : pDate.getPDate())
                    .build();
            reportContentList.add(reportContent);
        });
        // 存放数据库
        int insertCount1 = cfhMongodbDao.batchSaveRegularReportList(Collections.singletonList(report));
        int insertCount2 = cfhMongodbDao.batchSaveRegularReportContentList(reportContentList);
        return (insertCount1 == 1) && (insertCount2 == list.size());
    }
}
