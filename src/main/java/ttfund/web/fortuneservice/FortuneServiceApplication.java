package ttfund.web.fortuneservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(exclude = MongoAutoConfiguration.class)
@ComponentScan({"com.ttfund.web.core","ttfund.web"})
@EnableAsync
@EnableRetry
@EnableScheduling
@EnableTransactionManagement
public class FortuneServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(FortuneServiceApplication.class, args);
    }
}
