package ttfund.web.fortuneservice.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;

import java.sql.SQLException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 刷新数据库配置
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(DataSourceProperties.class)
public class DataSourceConfig {

    public static Map<String, String> dsreload = new ConcurrentHashMap<>();
    @Autowired
    ApplicationContext context;

    @ApolloConfigChangeListener
    private void onChange(ConfigChangeEvent changeEvent) {
        changeEvent.changedKeys().stream().forEach(s -> {
            if (dsreload.containsKey(s)) {
                log.info("{} changed oldValue={} newValue={}",
                        s, changeEvent.getChange(s).getOldValue(), changeEvent.getChange(s).getNewValue());
                String beanName = dsreload.get(s);
                try {
                    // 注意这里的bean名称要和自定义的名称一致，否则无法找到对应的实例
                    DruidDataSource master = context.getBean(beanName,DruidDataSource.class);
                    master.restart();
                    master.setUrl(changeEvent.getChange(s).getNewValue());
                    master.restart();
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }

            }
        });
    }
}
