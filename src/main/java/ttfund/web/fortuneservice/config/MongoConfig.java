package ttfund.web.fortuneservice.config;

import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;
import ttfund.web.fortuneservice.constant.AppConfigConstant;
import ttfund.web.fortuneservice.constant.CFHMongodbConstant;

import javax.annotation.PostConstruct;

@Slf4j
@Configuration
public class MongoConfig {

    @Value("${" + AppConfigConstant.MONGO_CFH_URL + "}")
    private String cfhMongoUrl;

    private MongoTemplate cfhTradeMongoTemplate;

    private MongoTemplate cfhMongoTemplate;


    @PostConstruct
    public void init() {
        createCfhTradeMongoTemplate();
        createCfhMongoTemplate();
    }

    @ApolloConfigChangeListener
    private void onChangeForCfhMongo(ConfigChangeEvent changeEvent) {
        if (changeEvent.isChanged(AppConfigConstant.MONGO_CFH_URL)) {
            this.cfhMongoUrl = changeEvent.getChange(AppConfigConstant.MONGO_CFH_URL).getNewValue();
            createCfhTradeMongoTemplate();
            createCfhMongoTemplate();
        }
    }

    public void createCfhTradeMongoTemplate() {
        MongoClient cfhTradeMongoClient = MongoClients.create(cfhMongoUrl);
        SimpleMongoClientDatabaseFactory factory = new SimpleMongoClientDatabaseFactory(cfhTradeMongoClient, CFHMongodbConstant.DB_CFH_TRADE);
        this.cfhTradeMongoTemplate = new MongoTemplate(factory);
    }
    public void createCfhMongoTemplate() {
        MongoClient cfhTradeMongoClient = MongoClients.create(cfhMongoUrl);
        SimpleMongoClientDatabaseFactory factory = new SimpleMongoClientDatabaseFactory(cfhTradeMongoClient, CFHMongodbConstant.DB_TT_FUND_CFH);
        this.cfhMongoTemplate = new MongoTemplate(factory);
    }

    public MongoTemplate getCfhTradeMongoTemplate() {
        return this.cfhTradeMongoTemplate;
    }

    public MongoTemplate getCfhMongoTemplate() {
        return cfhMongoTemplate;
    }
}
