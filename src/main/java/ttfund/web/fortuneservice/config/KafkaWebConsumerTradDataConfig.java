package ttfund.web.fortuneservice.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import ttfund.web.fortuneservice.constant.AppConfigConstant;
import ttfund.web.fortuneservice.constant.CommonConstant;

import java.util.Properties;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Configuration
public class KafkaWebConsumerTradDataConfig {

    @Value("${" + AppConfigConstant.KAFKA_WEB_CONSUMER_CONFIG_URL + ":10.228.130.182:9092}")
    public String url;

    @Bean(CommonConstant.WEB_TRAD_CONSUMER)
    public KafkaConsumer<String, String> createKafkaConsumer() {
        // 初始化KafkaConsumer
        Properties properties = new Properties();
        properties.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, url);  // Kafka服务器地址
        properties.put(ConsumerConfig.GROUP_ID_CONFIG, CommonConstant.GROUP_ID_CFH);  // 消费者组ID
        properties.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        properties.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        properties.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");  // 偏移量策略
        properties.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "false");  // 手动提交偏移量
        properties.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, "600000");  // 10分钟
        properties.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, "2000");  // 每2秒心跳
        properties.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, "30000");  // 会话超时时间
        properties.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, "100000");  // 每次最多拉取x条消息
        properties.put(ConsumerConfig.FETCH_MAX_BYTES_CONFIG, "104857600"); // 100 MB
        properties.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, "10485760"); // 10 MB


        // 创建KafkaConsumer
        KafkaConsumer<String, String> kafkaConsumer = new KafkaConsumer<>(properties);
        log.info("KafkaConsumer initialized with group ID: {}", CommonConstant.GROUP_ID_CFH);

        return kafkaConsumer;
    }

}
