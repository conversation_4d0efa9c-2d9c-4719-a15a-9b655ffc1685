package ttfund.web.fortuneservice.config;

import com.ttfund.web.core.constant.CoreConstant;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * @description 在日志中添加traceid便于追踪
 * @ date 2022/9/16
 **/
@Aspect
@Component
public class AddTraceIdAspect {

  /**
   *  切点是ttfund.web.financialplannerservice.job包下所有类(不包含子包)的public Return<String> execute(String s)方法
  */
  @Pointcut(value = "execution(public com.xxl.job.core.biz.model.ReturnT<String> ttfund.web.fortuneservice.job.*.execute(String))")
  public void traceIdAspect() {}

  @Before("traceIdAspect()")
  public void addTraceId() {
    String token = UUID.randomUUID().toString().replace("-", "");
    MDC.put(CoreConstant.logtraceid, token);
  }

  /**
   *   正常返回和抛异常都会执行
   */
  @After("traceIdAspect()")
  public void removeTraceId() {
    MDC.remove(CoreConstant.logtraceid);
  }

}
