package ttfund.web.fortuneservice.config;

import com.ttfund.web.base.helper.CommonHelper;
import com.ttfund.web.base.helper.DruidHelper;
import com.ttfund.web.base.helper.KafkaProducerHelper;
import com.ttfund.web.base.helper.MongodbHelper;
import com.ttfund.web.base.redishelper.IRedis;
import com.ttfund.web.base.redishelper.impl.RedisConnImpl;
import com.ttfund.web.core.register.AppCore;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.constant.AppConfigConstant;
import ttfund.web.fortuneservice.utils.VerticaHelper;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Properties;

/**
 * 核心操作类
 * <AUTHOR>
 * @version 1.0.0
 * @className App
 */
@Component
public class App {

    @Resource
    private AppCore appCore;

    @Resource
    private AppConfig appConfig;

    private MongodbHelper cfhMongodbWrite;

    private MongodbHelper cfhMongodbRead;

    private MongodbHelper hqMongodbRead;

    private DruidHelper sqlServer;

    private VerticaHelper verticaRead;

    private KafkaProducerHelper kafkafundweb;

    public IRedis cfhrediswrite;
    public IRedis cfhrediswritePJ;

    public VerticaHelper verticaTrade;

    public MongodbHelper tgMongodbRead;

    public KafkaProducerHelper getKafkafundweb() {
        return kafkafundweb;
    }

    public MongodbHelper getCfhMongodbWrite() {
        return cfhMongodbWrite;
    }

    public MongodbHelper getCfhMongodbRead() {
        return cfhMongodbRead;
    }

    public MongodbHelper getHqMongodbRead() {
        return hqMongodbRead;
    }

    public DruidHelper getSqlServer() {
        return sqlServer;
    }

    public VerticaHelper getVerticaRead(){
        return verticaRead;
    }

    @PostConstruct
    private void init() {

        cfhMongodbWrite = new MongodbHelper(appConfig.mongodbCfhConnStrWrite);
        appCore.dsreload.put(AppConfigConstant.MONGODB_CFH_CONN_WRITE, cfhMongodbWrite);

        cfhMongodbRead = new MongodbHelper(appConfig.mongodbCfhConnStrRead);
        appCore.dsreload.put(AppConfigConstant.MONGODB_CFH_CONN_READ, cfhMongodbRead);

        hqMongodbRead = new MongodbHelper(appConfig.mongodbHqConnStrRead);
        appCore.dsreload.put(AppConfigConstant.MONGODB_HQ_CONN_READ, hqMongodbRead);

        tgMongodbRead = new MongodbHelper(appConfig.mongodbTgConnStrRead);
        appCore.dsreload.put(AppConfigConstant.MONGODB_TG_CONN_READ, tgMongodbRead);

        Properties druidProperty = new Properties();
        druidProperty.put("druid.initialSize", "3");
        druidProperty.put("druid.maxActive", CommonHelper.toStr(100));
        druidProperty.put("druid.minIdle", "1");
        druidProperty.put("druid.maxWait", "1000");
        druidProperty.put("druid.timeBetweenEvictionRunsMillis", "60000");
        druidProperty.put("druid.minEvictableIdleTimeMillis", "30000");
        druidProperty.put("druid.testWhileIdle", "true");
        druidProperty.put("druid.failFast", "true");
        druidProperty.put("druid.validationQueryTimeout", "1");
        druidProperty.put("druid.validationQuery", "select 1");

        sqlServer = new DruidHelper(appConfig.sqlserver, "com.microsoft.sqlserver.jdbc.SQLServerDriver",druidProperty);
        appCore.dsreload.put(AppConfigConstant.SQLSERVER_CFH_CONN_WRITE, sqlServer);

        verticaRead = new VerticaHelper(appConfig.verticaConnStrRead, "com.vertica.jdbc.Driver", druidProperty);
        verticaRead.load();
        appCore.dsreload.put(AppConfigConstant.VERTICA_CONN_READ, verticaRead);

        //交易数据读写vertica
        verticaTrade = new VerticaHelper(appConfig.verticaConnStrReadWrite, "com.vertica.jdbc.Driver", druidProperty);
        verticaTrade.load();
        appCore.dsreload.put(AppConfigConstant.VERTICA_CONN_READ_WRITE, verticaTrade);

        kafkafundweb = new KafkaProducerHelper(appConfig.kafka_fundweb_connsr);
        appCore.dsreload.put(AppConfigConstant.kafka_fundweb_connsr, kafkafundweb);

        //万国88A
        cfhrediswrite = new RedisConnImpl(appConfig.redis_cfh_connsr_write,200,500);
        appCore.dsreload.put(AppConfigConstant.REDIS_CFH_CONNSR_WRITE, cfhrediswrite);

        //浦江api
        cfhrediswritePJ = new RedisConnImpl(appConfig.redis_cfh_connsr_write_pj,200,500);
        appCore.dsreload.put(AppConfigConstant.REDIS_CFH_CONNSR_WRITE_PJ, cfhrediswritePJ);
    }
}
