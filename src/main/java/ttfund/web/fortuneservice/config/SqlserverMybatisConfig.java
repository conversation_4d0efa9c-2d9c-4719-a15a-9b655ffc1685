package ttfund.web.fortuneservice.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.ttfund.web.base.helper.CommonHelper;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;

import javax.sql.DataSource;
import java.util.Properties;

@Configuration             //此处指向DAO接口
@MapperScan(basePackages = "ttfund.web.fortuneservice.dao.sqlserver", sqlSessionTemplateRef  = "cfhSqlSessionTemplate")
public class SqlserverMybatisConfig {

    @Bean(name = "cfhSqlserverDataSource") //此处指向yml配置文件中具体的数据源
    @ConfigurationProperties(prefix = "sqlserver.cfh.connsr.write")
    public DataSource tefpDataSource() {
        DruidDataSource dataSource = new DruidDataSource();
        Properties druidProperty = new Properties();
        druidProperty.put("druid.initialSize", "3");
        druidProperty.put("druid.maxActive", CommonHelper.toStr(100));
        druidProperty.put("druid.minIdle", "1");
        druidProperty.put("druid.maxWait", "1000");
        druidProperty.put("druid.timeBetweenEvictionRunsMillis", "60000");
        druidProperty.put("druid.minEvictableIdleTimeMillis", "30000");
        druidProperty.put("druid.testWhileIdle", "true");
        druidProperty.put("druid.failFast", "true");
        druidProperty.put("druid.validationQueryTimeout", "1");
        druidProperty.put("druid.validationQuery", "select 1");
        dataSource.configFromPropety(druidProperty);
        DataSourceConfig.dsreload.put("sqlserver.cfh.connsr.write.url", "cfhSqlserverDataSource");
        return dataSource;
    }

    @Bean(name = "cfhSqlserverSqlSessionFactory")
    public SqlSessionFactory tefpSqlSessionFactory(@Qualifier("cfhSqlserverDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml"));//指定mapper.xml路径
        //增加驼峰配置
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setCallSettersOnNulls(true);
        bean.setConfiguration(configuration);
        return bean.getObject();
    }

    @Bean(name = "cfhSqlserverTransactionManager")
    public DataSourceTransactionManager tefpTransactionManager(@Qualifier("cfhSqlserverDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Primary
    @Bean(name = "cfhSqlserverTransactionTemplate")
    public TransactionTemplate shardingTransactionTemplate(@Qualifier("cfhSqlserverTransactionManager") DataSourceTransactionManager transactionManager) {
        TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
        transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        return transactionTemplate;
    }

    @Bean(name = "cfhSqlSessionTemplate")
    public SqlSessionTemplate tefpSqlSessionTemplate(@Qualifier("cfhSqlserverSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}
