package ttfund.web.fortuneservice.config;

import com.alibaba.fastjson.JSON;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ContainerProperties;
import ttfund.web.fortuneservice.constant.AppConfigConstant;
import ttfund.web.fortuneservice.constant.CommonConstant;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className CommonConstant
 * @date 2023/4/14 15:34
 */
@Configuration
public class KafkaEastMoneyConsumerConfig {

    @Value("${" + AppConfigConstant.KAFKA_EAST_MONEY_LISTENER_CONCURRENCY + ":1}")
    public Integer listenerConcurrency;

    @Value("${" + AppConfigConstant.KAFKA_EAST_MONEY_CONSUMER_CONFIG + "}")
    public String properties;

    @Bean(CommonConstant.FACTORY_EAST_MONEY)
    //@RefreshScope
    public ConcurrentKafkaListenerContainerFactory listenerContainer() {
        ConcurrentKafkaListenerContainerFactory container = new ConcurrentKafkaListenerContainerFactory();
        Map config = JSON.parseObject(properties, Map.class);
        config.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, org.apache.kafka.common.serialization.StringDeserializer.class);
        config.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, org.apache.kafka.common.serialization.StringDeserializer.class);
        container.setConsumerFactory(new DefaultKafkaConsumerFactory(config));

        //设置并发量，小于或等于Topic的分区数
        container.setConcurrency(listenerConcurrency);
        //设置为批量监听
        container.setBatchListener(true);
        container.getContainerProperties().setAckMode(ContainerProperties.AckMode.BATCH);
        return container;
    }
}
