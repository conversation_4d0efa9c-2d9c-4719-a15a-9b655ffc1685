package ttfund.web.fortuneservice.config;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.constant.AppConfigConstant;
import ttfund.web.fortuneservice.constant.CommonConstant;
import ttfund.web.fortuneservice.model.dto.ReportCheckConfigDto;

import java.util.List;
import java.util.Map;

/**
 * 通用配置
 * <AUTHOR>
 * @version 1.0.0
 * @className CommonConfig
 * @date 2023/4/8 8:39
 */
@Component
public class CommonConfig {

    /**
     * 行情接口url
     */
    @Value("${" + CommonConstant.API_HQ_URL + ":}")
    public String hqBaseUrl;

    /**
     * 行情接口key
     */
    @Value("${" + CommonConstant.API_HQ_AUTH_KEY + ":}")
    public String authKey;

    /**
     * 向前查询交易日天数配置
     */
    @Value("${" + CommonConstant.SELECT_DAY_MANAGER + ":10}")
    public Integer managerDays;

    /**
     * 向前查询交易日天数配置
     */
    @Value("${" + CommonConstant.SELECT_DAY_LABEL + ":2}")
    public Integer labelDays;

    /**
     * 期间 观点条数配置
     */
    @Value("${" + CommonConstant.QUICK_REVIEW_NUM + ":3}")
    public Integer gteNum;

    /**
     * 默认 img地址
     */
    @Value("${" + CommonConstant.DEFAULT_HEADER_IMG + ":https://img.1234567.com.cn/trade/2017102417253129.png}")
    public String defaultHeaderImg;

    /**
     * 雨燕接口url
     */
    @Value("${" + CommonConstant.API_YUYAN_URL + ":}")
    public String yuYanBaseUrl;

    @Value("${" + AppConfigConstant.ACTIVITY_ALIVE_ID + ":123ac}")
    public List<String> activityIdList;

    /**
     * 咚咚消息发送url
     **/
    @Value("${"+CommonConstant.DONG_DONG_URL +":}")
    public String dongDongSendUrl;

    /**
     * 产品未按期披露公告/净值定期检测报告-消息通知人员
     **/
    @Value("${"+CommonConstant.INSPECTION_REPORT_RECEIVING +":}")
    public List<String> inspectionReportReceiving;

    /**
     * 产品未按期披露公告/净值定期检测报告-清算告警配置ID
     **/
    @Value("${"+CommonConstant.INSPECTION_REPORT_QS_CONFIG +":JH_LEVEL2}")
    public String inspectionReportQSConfig;

    @Value("${"+CommonConstant.SYSTEM_REPORT_QS_CONFIG +":0005}")
    public String systemReportQSConfig;

    @Value("${"+CommonConstant.TASK_REPORT_QS_CONFIG +":0005404}")
    public String taskReportQSConfig;

    /**
     * 服务告警通知人员
     **/
    @ApolloJsonValue("${"+CommonConstant.SERVICE_ALARM_OA +":{}}")
    public Map<String, String> alarmOaMap;
}
