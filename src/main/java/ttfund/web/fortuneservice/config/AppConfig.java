package ttfund.web.fortuneservice.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import ttfund.web.fortuneservice.constant.AppConfigConstant;

/**
 * 核心配置类
 * <p>只配置连接串</p>
 * <AUTHOR>
 * @version 1.0.0
 * @className AppConfig
 */
@Component
public class AppConfig {

    /**
     * CFH mongodb 写
     */
    @Value("${" + AppConfigConstant.MONGODB_CFH_CONN_WRITE + "}")
    public String mongodbCfhConnStrWrite;

    /**
     * CFH mongodb 读
     */
    @Value("${" + AppConfigConstant.MONGODB_CFH_CONN_READ + "}")
    public String mongodbCfhConnStrRead;

    /**
     * HQ mongodb 读
     */
    @Value("${" + AppConfigConstant.MONGODB_HQ_CONN_READ + "}")
    public String mongodbHqConnStrRead;

    /**
     * 东财 sqlserver
     */
    @Value("${" + AppConfigConstant.SQLSERVER_CFH_CONN_WRITE + "}")
    public String sqlserver;

    /**
     * vertica读
     */
    @Value("${" + AppConfigConstant.VERTICA_CONN_READ + "}")
    public String verticaConnStrRead;
    /**
     * vertica读写
     */
    @Value("${" + AppConfigConstant.VERTICA_CONN_READ_WRITE + "}")
    public String verticaConnStrReadWrite;

    @Value("${" + AppConfigConstant.kafka_fundweb_connsr + "}")
    public String kafka_fundweb_connsr;

    @Value("${" + AppConfigConstant.REDIS_CFH_CONNSR_WRITE + "}")
    public String redis_cfh_connsr_write;
    /**
     * 投顾mongoDB
     */
    @Value("${" + AppConfigConstant.MONGODB_TG_CONN_READ + "}")
    public String mongodbTgConnStrRead;

    @Value("${" + AppConfigConstant.REDIS_CFH_CONNSR_WRITE_PJ + "}")
    public String redis_cfh_connsr_write_pj;
}
