package ttfund.web.fortuneservice.model.mongo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * VIP AI报告详细信息MongoDB实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VIPAIReportDetailMongo {

    @Field("ID")
    private String id;

    @Field("ReportID")
    private String reportId;

    @Field("Title")
    private String title;

    @Field("Content")
    private Object content;

    @Field("References")
    private String references;

    @Field("ReportType")
    private String reportType;

    @Field("Type")
    private String type;

    @Field("CreateTime")
    private Date createTime;

    @Field("UpdateTime")
    private Date updateTime;

    @Field("OriginalQA")
    private String originalQA; // 改为JSON字符串存储，避免嵌套对象编解码器问题

    /**
     * 原始问答项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OriginalQAItem {
        @Field("SubjectID")
        private String subjectId;

        @Field("SubjectType")
        private Integer subjectType;

        @Field("TaskSubjectType")
        private String taskSubjectType;

        @Field("SubjectTitle")
        private String subjectTitle;

        @Field("SubjectCode")
        private String subjectCode;

        @Field("PointPrediction")
        private String pointPrediction;
    }
}
