package ttfund.web.fortuneservice.model.mongo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * VIP AI报告MongoDB实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "VIPAIReport")
public class VIPAIReportMongo {

    private String _id; // MongoDB主键字段，用于insertOrUpdate

    @Field("CFHID")
    private String cfhid;

    @Field("TaskConfigID")
    private String taskConfigId;

    @Field("TaskCycleMark")
    private String taskCycleMark;

    @Field("ReportDate")
    private String reportDate;

    @Field("Status")
    private String status;

    @Field("Index")
    private Integer index; // 批次内序号

    @Field("CreateTime")
    private Date createTime;

    @Field("UpdateTime")
    private Date updateTime;

    @Field("Details")
    private String details; // 改为JSON字符串存储，避免嵌套对象编解码器问题
}
