package ttfund.web.fortuneservice.model.entity;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * AI月报处理结果详细信息实体类
 */
@Data
public class VIPAIReportDetail implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 唯一ID
     */
    private String id;
    
    /**
     * ai报告ID
     */
    private String reportId;
    
    /**
     * 标题
     */
    private String title;
    
    /**
     * 内容
     */
    private String content;
    
    /**
     * 引用多机构才有，单机构不用
     */
    private String references;
    
    /**
     * 报告类型：整体-OVERALL、资产研判-ASSET_ANALYSIS、热门行业-HOT_INDUSTRY、配置建议-ALLOCATION_ADVICE、宏观经济-MACRO_ECONOMY
     */
    private String reportType;
    
    /**
     * 单机构-SINGLE_ORG、多机构-MULTI_ORG
     */
    private String type;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
}
