package ttfund.web.fortuneservice.model.entity;

import java.io.Serializable;
import java.util.Date;

public class VIPMeeting implements Serializable {
    private String id;

    private String cfhId;

    private Integer minVipLevel;

    private Date meetingStartTime;

    private Date meetingEndTime;

    private Date createTime;

    private Date updateTime;

    private String typeId;

    private Integer deleted;

    private Integer aiEdit;

    private String status;

    private Integer aiStatus;

    private String extend;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getCfhId() {
        return cfhId;
    }

    public void setCfhId(String cfhId) {
        this.cfhId = cfhId == null ? null : cfhId.trim();
    }

    public Integer getMinVipLevel() {
        return minVipLevel;
    }

    public void setMinVipLevel(Integer minVipLevel) {
        this.minVipLevel = minVipLevel;
    }

    public Date getMeetingStartTime() {
        return meetingStartTime;
    }

    public void setMeetingStartTime(Date meetingStartTime) {
        this.meetingStartTime = meetingStartTime;
    }

    public Date getMeetingEndTime() {
        return meetingEndTime;
    }

    public void setMeetingEndTime(Date meetingEndTime) {
        this.meetingEndTime = meetingEndTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getTypeId() {
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId == null ? null : typeId.trim();
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public Integer getAiEdit() {
        return aiEdit;
    }

    public void setAiEdit(Integer aiEdit) {
        this.aiEdit = aiEdit;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public Integer getAiStatus() {
        return aiStatus;
    }

    public void setAiStatus(Integer aiStatus) {
        this.aiStatus = aiStatus;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend == null ? null : extend.trim();
    }
}