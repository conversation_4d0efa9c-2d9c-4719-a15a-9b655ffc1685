package ttfund.web.fortuneservice.model.entity;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * AI月报处理结果实体类
 */
@Data
public class VIPAIReport implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 唯一ID
     */
    private String id;
    
    /**
     * 财富号ID，多机构版为null
     */
    private String cfhid;
    
    /**
     * 任务ID
     */
    private String taskConfigId;

    /**
     * 任务周期标记
     */
    private String taskCycleMark;

    /**
     * 所属月份yyyy-MM
     */
    private String reportDate;
    
    /**
     * 状态（待审核-PENDING，审核通过-APPROVED，审核未通过-REJECTED，下架-OFFLINE）
     */
    private String status;

    /**
     * 批次内序号（每个批次从1开始）
     */
    private Integer index;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
