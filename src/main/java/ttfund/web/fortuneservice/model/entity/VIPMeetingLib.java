package ttfund.web.fortuneservice.model.entity;

import ttfund.web.fortuneservice.model.dto.MeetingSummaryPoint;
import ttfund.web.fortuneservice.utils.TimeUtil;

import java.io.Serializable;
import java.util.Date;

public class VIPMeetingLib implements Serializable {
    private String id;

    private String funds;

    private String indexs;

    private String sectors;

    private String topic;

    private String viewpoint;

    private String meetingid;

    private Integer deleted;

    private Date creatTime;

    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public VIPMeetingLib(MeetingSummaryPoint point, VIPMeeting meeting) {
        this.id = meeting.getId() + point.getTopic();
        this.funds = String.join(",", point.getCodes().getFUND());
        this.indexs = String.join(",", point.getCodes().getINDEX());
        this.sectors = String.join(",", point.getCodes().getSECTOR());
        this.topic = point.getTopic();
        this.viewpoint = point.getViewpoint();
        this.meetingid = meeting.getId();
        this.deleted = 0;
        this.creatTime = TimeUtil.getNowDate();
        this.updateTime = TimeUtil.getNowDate();
    }

    public VIPMeetingLib() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getFunds() {
        return funds;
    }

    public void setFunds(String funds) {
        this.funds = funds == null ? null : funds.trim();
    }

    public String getIndexs() {
        return indexs;
    }

    public void setIndexs(String indexs) {
        this.indexs = indexs == null ? null : indexs.trim();
    }

    public String getSectors() {
        return sectors;
    }

    public void setSectors(String sectors) {
        this.sectors = sectors == null ? null : sectors.trim();
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic == null ? null : topic.trim();
    }

    public String getViewpoint() {
        return viewpoint;
    }

    public void setViewpoint(String viewpoint) {
        this.viewpoint = viewpoint == null ? null : viewpoint.trim();
    }

    public String getMeetingid() {
        return meetingid;
    }

    public void setMeetingid(String meetingid) {
        this.meetingid = meetingid == null ? null : meetingid.trim();
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public Date getCreatTime() {
        return creatTime;
    }

    public void setCreatTime(Date creatTime) {
        this.creatTime = creatTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}