package ttfund.web.fortuneservice.model.entity;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class VIPMeetingExtend {
    @ApiModelProperty(value = "会议主题")
    private String meetingTopic;

    @ApiModelProperty(value = "审核描述")
    private String auditDescription;

    @ApiModelProperty(value = "背景描述")
    private String backgroundDescription;

    @ApiModelProperty(value = "基金经理名称")
    private String managerName;

    @ApiModelProperty(value = "基金经理职位")
    private String managerPosition;

    @ApiModelProperty(value = "基金经理头像")
    private String managerAvatar;

    @ApiModelProperty(value = "海报图")
    private String posterImage;

    @ApiModelProperty(value = "banner图")
    private String bannerImage;

    @ApiModelProperty(value = "腾讯会议链接")
    private String tencentMeetingLink;

    @ApiModelProperty(value = "腾讯会议房间号")
    private String tencentRoomId;

    @ApiModelProperty(value = "会议内容")
    private String meetingContent;

    @ApiModelProperty(value = "会议内容AI")
    private String meetingContentAI;

    @ApiModelProperty(value = "会议问答")
    private List<VIPMeetingQA> meetingQA;

    @ApiModelProperty(value = "会议产品")
    private String products;

    @ApiModelProperty(value = "会议名片")
    private String cards;

    @ApiModelProperty(value = "会议pdf")
    private String meetingPdfName;

    @ApiModelProperty(value = "会议pdf")
    private String meetingPdfUrl;

    @ApiModelProperty(value = "审核人")
    private String auditUser;

    @ApiModelProperty(value = "历史数据会议纪要url")
    private String summaryLink;
}
