package ttfund.web.fortuneservice.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@ApiModel("主题实体")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FundThemeDto implements Serializable {

    @ApiModelProperty("主题id")
    @JSONField(name = "ThemeId")
    private String ThemeId;

    @ApiModelProperty("主题名称")
    @JSONField(name = "ThemeName")
    private String ThemeName;

    @ApiModelProperty("删除标识：0：未删除")
    @JSONField(name = "IsDel")
    private Integer IsDel;

    @ApiModelProperty("子主题列表")
    @JSONField(name = "SubThemeId")
    private String SubThemeId;
}
