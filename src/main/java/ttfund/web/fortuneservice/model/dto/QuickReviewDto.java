package ttfund.web.fortuneservice.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> dengchaojun
 * @version : 1.0
 * @email : deng<PERSON><PERSON><EMAIL>
 * @date : 2021-07-27 08:48
 * @description :
 */
@ApiModel("天天快评实体对象")
@Data
public class QuickReviewDto implements Serializable {

    @ApiModelProperty("评论卡片唯一ID")
    @JSONField(name = "ID")
    private String ID;

    @ApiModelProperty("权重")
    @JSONField(name = "Weight")
    private Integer Weight;

    @ApiModelProperty("财富号id")
    @JSONField(name = "CFHID")
    private String CFHID;

    @ApiModelProperty("财富号名称")
    @JSONField(name = "CFHName")
    private String CFHName;

    @ApiModelProperty("基金经理id")
    @JSONField(name = "MGRID")
    private String MGRID;

    @ApiModelProperty("基金经理名称")
    @JSONField(name = "MGRName")
    private String MGRName;

    @ApiModelProperty("快评标题")
    @JSONField(name = "Title")
    private String Title;

    @ApiModelProperty("快评内容")
    @JSONField(name = "ReviewContent")
    private String ReviewContent;

    @ApiModelProperty("情绪类型, 0乐观, 1中性, 2谨慎")
    @JSONField(name = "EmotionType")
    private Integer EmotionType;

    @ApiModelProperty("产品类型, 1普通基金, 6组合, 15投顾")
    @JSONField(name = "ProductType")
    private Integer ProductType;

    @ApiModelProperty("产品代码")
    @JSONField(name = "ProductCode")
    private String ProductCode;

    // 产品标签类型 ? 列表
    @ApiModelProperty("产品标签, ',' 分割")
    @JSONField(name = "ProductTags")
    private String ProductTags;

    @ApiModelProperty("产品类型2, 1普通基金, 6组合, 15投顾")
    @JSONField(name = "ProductType2")
    private Integer ProductType2;

    @ApiModelProperty("产品代码")
    @JSONField(name = "ProductCode2")
    private String ProductCode2;

    @ApiModelProperty("产品标签2, ',' 分割")
    @JSONField(name = "ProductTags2")
    private String ProductTags2;

    // 评论发布时间
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(name = "UpdateTime")
    private Date UpdateTime;

    @ApiModelProperty("更新日期")
    @JSONField(name = "UpdateDay")
    private Integer UpdateDay;

    @ApiModelProperty("更新时间戳")
    @JSONField(name = "timepoint")
    private Long timepoint;
    /**
     * 产品收益图片地址(仅限组合产品使用)
     */
    @JSONField(name = "ProductSyImg")
    private String ProductSyImg;
    /**
     * 产品收益图片地址(仅限组合产品使用)
     */
    @JSONField(name = "ProductSyImg2")
    private String ProductSyImg2;

    @ApiModelProperty("主题列表")
    @JSONField(name = "ThemeLabel")
    private List<FundThemeDto> ThemeLabel;

    @ApiModelProperty("标签类型：0赛道标签，1观点标签")
    @JSONField(name = "LabelType")
    private Integer LabelType;
}
