package ttfund.web.fortuneservice.model.dto;

import lombok.Data;

import java.util.Date;


@Data
public class InvestmentUserStatDTO {
    private String _id;
    private String jjgsId;
    private String strategyName;//策略名称
    private String strategyId;//策略id
    private String partner;//机构投顾编号
    private Date updateTIme;

    //自选
    private Integer optionNum;//策略加自选数

    //订阅
    private Integer hisSubNum;//历史订阅数
    private Integer currSubNum;//当前订阅数

    //近一年留存率
    private Double retentionRate;//当前订阅数

    //目标盈年化收益率、目标盈目标收益
    private String TGNAME;//策略名称
    private String TGCODE;//策略代码
    private String ACCOUNTID;//财富号id
    private String TARGETPROFIT;//目标盈目标收益
    private Double TARGETANNYIELD;//目标盈年化收益

    //策略复购率、用户持仓正收益占比、用户平均持有时长
    private Double USER_FGRATIO;//策略复购率
    private Double USER_POSITIVE_RATIO;//用户持仓正收益占比
    private Double USER_AVGDAY;//用户平均持有时长


}
