package ttfund.web.fortuneservice.model.dto;

import lombok.Data;

import java.util.Date;

@Data
public class VedioAudioJson {
    /**
     * 视频或音频ID
     */
    public String ID;
    /**
     * 视频或音频名称
     */
    public String Name;
    /**
     * 视频或音频持续时长
     */
    public Integer Duration;
    /**
     * 视频或音频大小
     */
    public Integer Size;
    /**
     * 视频或音频源文件地址
     */
    public String Url;
    /**
     * 视频或音频截图地址
     */
    public String ImgUrl;
    /**
     * 作者Id
     */
    public String AuthorId;
    /**
     * 讲解时间点
     */
    public Integer PointTime;
    /**
     * 直播开始时间
     */
    public Date StartTime;
    /**
     * 直播大咖头像
     */
    public String HeadAddress;
    /**
     * 直播房间号
     */
    public Integer RoomNumber;
}
