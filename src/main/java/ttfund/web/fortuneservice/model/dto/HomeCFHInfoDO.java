package ttfund.web.fortuneservice.model.dto;

import ttfund.web.fortuneservice.constant.PurviewRoleTypeEnum;

import java.util.Date;
import java.util.List;

/**
 * 接口首页财富号精选类
 */
public class HomeCFHInfoDO {
    /**
     * 财富号Id
     */
    private String Id;
    /**
     *财富号Id
     */
    private String CFHID;
    /**
     *财富号名称
     */
    private String CFHName;
    /**
     *基金公司代码
     */
    private String CommpanyCode;
    /**
     *基金公司名称
     */
    private String CommpanyName;
    /**
     *简介或标题
     */
    private String Summary;
    /**
     *财富号头像Logo
     */
    private String HeaderImgPath;
    /**
     *财富号状态
     */
    private int Status;
    /**
     *财富号标语
     */
    private String Slogans;
    /**
     *卡劵面值金额 2,5,8
     */
    private String CouponAmount;
    /**
     *财富号三方权限
     */
    private Integer Purview;
    /**
     *财富号推送权限
     */
    private Integer PushPurview;
    /**
     *推送栏目ID
     */
    private String PushCategoryCode;
    /**
     *角色ID
     */
    private String RoleId;
    /**
     *角色名称
     */
    private String RoleName;
    /**
     *角色类型
     */
    private PurviewRoleTypeEnum RoleType;
    /**
     *基金公司名称简拼
     */
    private String JianPin;
    /**
     *财富号类型
     */
    private String OrganizationType;
    /**
     *标签信息列表
     */
    private List<CFHLabelInfoDO> LabelInfoList;
    /**
     *通行证ID
     */
    private String RelatedUid;
    /**
     *直播黑名单状态
     */
    private int LiveBlackState;
    /**
     *（天天快评）默认权重
     */
    private int DefaultWeight;
    /**
     *权重开始时间
     */
    private long WeightStartTimeStamp;
    /**
     *权重结束时间
     */
    private long WeightEndTimeStamp;
    /**
     * 订阅数
     */
    private long SubscriberCount;
    /**
     * 阅读数
     */
    private long ReadCount;
    /**
     * 文章数
     */
    private long ArticleCount;
    /**
     * 是否加V(0未加,1已加)
     */
    private int BigVip;

    private Boolean IsFund;

    /**
     *更新时间
     */
    private Date UpdateTime;

    public String getId() {
        return Id;
    }

    public void setId(String id) {
        Id = id;
    }

    public String getCFHID() {
        return CFHID;
    }

    public void setCFHID(String CFHID) {
        this.CFHID = CFHID;
    }

    public String getCFHName() {
        return CFHName;
    }

    public void setCFHName(String CFHName) {
        this.CFHName = CFHName;
    }

    public String getCommpanyCode() {
        return CommpanyCode;
    }

    public void setCommpanyCode(String commpanyCode) {
        CommpanyCode = commpanyCode;
    }

    public String getCommpanyName() {
        return CommpanyName;
    }

    public void setCommpanyName(String commpanyName) {
        CommpanyName = commpanyName;
    }

    public String getSummary() {
        return Summary;
    }

    public void setSummary(String summary) {
        Summary = summary;
    }

    public String getHeaderImgPath() {
        return HeaderImgPath;
    }

    public void setHeaderImgPath(String headerImgPath) {
        HeaderImgPath = headerImgPath;
    }

    public int getStatus() {
        return Status;
    }

    public void setStatus(int status) {
        Status = status;
    }

    public String getSlogans() {
        return Slogans;
    }

    public void setSlogans(String slogans) {
        Slogans = slogans;
    }

    public String getCouponAmount() {
        return CouponAmount;
    }

    public void setCouponAmount(String couponAmount) {
        CouponAmount = couponAmount;
    }

    public Integer getPurview() {
        return Purview;
    }

    public void setPurview(Integer purview) {
        Purview = purview;
    }

    public Integer getPushPurview() {
        return PushPurview;
    }

    public void setPushPurview(Integer pushPurview) {
        PushPurview = pushPurview;
    }

    public String getPushCategoryCode() {
        return PushCategoryCode;
    }

    public void setPushCategoryCode(String pushCategoryCode) {
        PushCategoryCode = pushCategoryCode;
    }

    public String getRoleId() {
        return RoleId;
    }

    public void setRoleId(String roleId) {
        RoleId = roleId;
    }

    public String getRoleName() {
        return RoleName;
    }

    public void setRoleName(String roleName) {
        RoleName = roleName;
    }

    public PurviewRoleTypeEnum getRoleType() {
        return RoleType;
    }

    public void setRoleType(PurviewRoleTypeEnum roleType) {
        RoleType = roleType;
    }

    public String getJianPin() {
        return JianPin;
    }

    public void setJianPin(String jianPin) {
        JianPin = jianPin;
    }

    public String getOrganizationType() {
        return OrganizationType;
    }

    public void setOrganizationType(String organizationType) {
        OrganizationType = organizationType;
    }

    public List<CFHLabelInfoDO> getLabelInfoList() {
        return LabelInfoList;
    }

    public void setLabelInfoList(List<CFHLabelInfoDO> labelInfoList) {
        LabelInfoList = labelInfoList;
    }

    public String getRelatedUid() {
        return RelatedUid;
    }

    public void setRelatedUid(String relatedUid) {
        RelatedUid = relatedUid;
    }

    public int getLiveBlackState() {
        return LiveBlackState;
    }

    public void setLiveBlackState(int liveBlackState) {
        LiveBlackState = liveBlackState;
    }

    public int getDefaultWeight() {
        return DefaultWeight;
    }

    public void setDefaultWeight(int defaultWeight) {
        DefaultWeight = defaultWeight;
    }

    public long getWeightStartTimeStamp() {
        return WeightStartTimeStamp;
    }

    public void setWeightStartTimeStamp(long weightStartTimeStamp) {
        WeightStartTimeStamp = weightStartTimeStamp;
    }

    public long getWeightEndTimeStamp() {
        return WeightEndTimeStamp;
    }

    public void setWeightEndTimeStamp(long weightEndTimeStamp) {
        WeightEndTimeStamp = weightEndTimeStamp;
    }

    public Date getUpdateTime() {
        return UpdateTime;
    }

    public void setUpdateTime(Date updateTime) {
        UpdateTime = updateTime;
    }

    public long getSubscriberCount() {
        return SubscriberCount;
    }

    public void setSubscriberCount(long subscriberCount) {
        SubscriberCount = subscriberCount;
    }

    public long getReadCount() {
        return ReadCount;
    }

    public void setReadCount(long readCount) {
        ReadCount = readCount;
    }

    public long getArticleCount() {
        return ArticleCount;
    }

    public void setArticleCount(long articleCount) {
        ArticleCount = articleCount;
    }

    public int getBigVip() {
        return BigVip;
    }

    public void setBigVip(int bigVip) {
        BigVip = bigVip;
    }

    public Boolean getIsFund() {
        return IsFund;
    }

    public void setIsFund(Boolean fund) {
        IsFund = fund;
    }
}
