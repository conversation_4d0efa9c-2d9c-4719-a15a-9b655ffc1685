package ttfund.web.fortuneservice.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 高端理财-定期检测报告
 *
 * <AUTHOR>
 * @date 2023/5/6 15:02
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RegularInspectionReportDto {

    /**
     * 唯一ID
     **/
    private String reportId;

    /**
     * 报告名称
     **/
    private String reportName;

    /**
     * 报告类型  (1-私募逾期报告 /2-私募检查报告 /3-资管产品检查报告 /4-资管产品逾期报告)
     **/
    private Integer reportType;

    /**
     * 检测时间
     **/
    private Date checkTime;
}
