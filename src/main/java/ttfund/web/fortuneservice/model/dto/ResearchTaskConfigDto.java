package ttfund.web.fortuneservice.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * ResearchTaskConfigDto.java
 *
 * <AUTHOR>
 * @date 2023/4/26 16:46
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResearchTaskConfigDto {

    /**
     * 唯一ID
     **/
    private String ID;

    /**
     * 任务名称
     **/
    private String TaskName;

    /**
     * 任务内容
     **/
    private String TaskContent;

    /**
     * 任务文件地址
     **/
    private String TaskFileUrl;

    /**
     * 任务类型：1-定期观点、2-高频行业、3-突发事件
     **/
    private Integer TaskType;

    /**
     * 任务周期：0-一次性任务、1-每周、2-每月、3-每季度
     **/
    private Integer TaskCycles;

    /**
     * 研究任务组ID
     **/
    private String TaskGroupId;

    /**
     * 任务发布时间
     **/
    private Date TaskReleaseTime;

    /**
     * 任务有效天数
     **/
    private Integer TaskValidDay;

    /**
     * 删除状态：0-未删除，1-已删除
     **/
    private Integer IsDel;

    /**
     * 更新时间
     **/
    private Date UpdateTime;

    /**
     * 创建时间
     **/
    private Date CreateTime;
}
