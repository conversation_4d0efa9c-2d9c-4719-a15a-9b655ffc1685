package ttfund.web.fortuneservice.model.dto;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/2/3 10:37
 * 调接口获取得到的财富号交易数据
 */
@NoArgsConstructor
@AllArgsConstructor
public class TradeDetailRecord {
    /**
     * 交易申请编号
     */
    private String applyNo;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 父交易类型
     */
    private String parentTradeType;

    /**
     * 父交易编号
     */
    private String parentApplyNo;

    private String traceNo;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 申请数额
     */
    private BigDecimal applyAmount;

    private BigDecimal applyVol;

    /**
     * 申请时间 年月日时分秒 2020-09-03 13:58:38.000000
     */
    private Date applyTime;

    /**
     * 交易时间 年月日 2020-09-03
     */
    private Date transactionDate;

    public String getApplyNo() {
        return applyNo;
    }

    public void setApplyNo(String applyNo) {
        this.applyNo = applyNo;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getParentTradeType() {
        return parentTradeType;
    }

    public void setParentTradeType(String parentTradeType) {
        this.parentTradeType = parentTradeType;
    }

    public String getParentApplyNo() {
        return parentApplyNo;
    }

    public void setParentApplyNo(String parentApplyNo) {
        this.parentApplyNo = parentApplyNo;
    }

    public String getTraceNo() {
        return traceNo;
    }

    public void setTraceNo(String traceNo) {
        this.traceNo = traceNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public BigDecimal getApplyAmount() {
        return applyAmount;
    }

    public void setApplyAmount(BigDecimal applyAmount) {
        this.applyAmount = applyAmount;
    }

    public BigDecimal getApplyVol() {
        return applyVol;
    }

    public void setApplyVol(BigDecimal applyVol) {
        this.applyVol = applyVol;
    }

    public Date getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    public Date getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(Date transactionDate) {
        this.transactionDate = transactionDate;
    }
}
