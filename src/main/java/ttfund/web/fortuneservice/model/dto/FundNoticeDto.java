package ttfund.web.fortuneservice.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 基金公告（公募、私募）实体类
 * <AUTHOR>
 * @date 2022/10/12 11:28
 * @email <EMAIL>
 * @version 1.0
 */
@Data
public class FundNoticeDto {

    @ApiModelProperty(value = "主键")
    private String _id;

    @ApiModelProperty(value = "EID")
    private String EID;

    @ApiModelProperty(value = "基金代码")
    private String FCODE;

    @ApiModelProperty(value = "公告日期")
    private Date PDATE;

    /**
     * 1-公告 2-研报 3-占位公告
     */
    @ApiModelProperty(value = "公告类型")
    private String NOTICETYPE;

    @ApiModelProperty(value = "公告文案")
    private String NOTICE;

    @ApiModelProperty(value = "展示时间-起始值")
    private Date BEGINTIME;

    @ApiModelProperty(value = "展示时间-结束值")
    private Date ENDTIME;

    @ApiModelProperty(value = "公告标签")
    private String NOTICEMARK;

    @ApiModelProperty(value = "跳转链接")
    private String LINKURL;

    @ApiModelProperty(value = "连接类型")
    private String LINKTYPE;

    @ApiModelProperty(value = "更新时间")
    private Date UPDATETIME;

    @ApiModelProperty(value = "MongodbFundNoticeLinkUrlJson")
    private String LINKJSON;
    /**
     * 1:单品页,2历史净值,3阶段涨幅
     */
    @ApiModelProperty(value = "占位公告显示位置")
    private String SHOWPOSITION;

    /**
     * 仅支持ETF(ETF)、REITS(REITS) （已作废）基金类型：0(全部)、25(股票)、27(混合)、26(指数)、31(债券)、35(货币)、6(QDII)、3(ETF场内)、33(ETF联接)、15(FOF)、4(LOF)、50(商品)、968(香港基金)、32(定开债)
     */
    @ApiModelProperty(value = "FUNDTYPE")
    private String FUNDTYPE;

    /**
     * 1-根据基金代码匹配 2-根据基金类型匹配
     */
    @ApiModelProperty(value = "匹配类型")
    private String MATCHTYPE;
}
