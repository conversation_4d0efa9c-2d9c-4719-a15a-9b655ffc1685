package ttfund.web.fortuneservice.model.dto;

import lombok.Data;
import java.util.List;

/**
 * 多机构月度报告请求DTO
 */
@Data
public class MultiMonthlyReportRequestDTO {
    
    /**
     * 机构列表
     */
    private List<Institution> institutions;
    
    /**
     * 截止时间
     */
    private String deadline;
    
    @Data
    public static class Institution {
        /**
         * 财富号ID
         */
        private String cfhId;
        
        /**
         * 机构名称
         */
        private String institutionName;
        
        /**
         * 任务月份
         */
        private String taskMonth;
        
        /**
         * 大类与行业
         */
        private List<CategoryAndIndustry> categoryAndIndustries;
        
        /**
         * 资产配置
         */
        private List<AssetAllocation> assetAllocations;
        
        /**
         * 宏观经济
         */
        private List<MacroEconomic> macroEconomics;
    }
    
    @Data
    public static class CategoryAndIndustry {
        /**
         * 题目ID
         */
        private String subjectId;
        
        /**
         * 所属类型
         */
        private String categoryType;
        
        /**
         * 行业名称
         */
        private String industryName;
        
        /**
         * 行业代码
         */
        private String industryCode;
        
        /**
         * 观点-历史总结
         */
        private String historicalSummary;
        
        /**
         * 观点-未来预期
         */
        private String futureExpectation;
    }
    
    @Data
    public static class AssetAllocation {
        /**
         * 题目ID
         */
        private String subjectId;
        
        /**
         * 所属类型
         */
        private String assetType;
        
        /**
         * 资产名称
         */
        private String assetName;
        
        /**
         * 低风险占比
         */
        private Double lowRiskRatio;
        
        /**
         * 中风险占比
         */
        private Double mediumRiskRatio;
        
        /**
         * 高风险占比
         */
        private Double highRiskRatio;
    }
    
    @Data
    public static class MacroEconomic {
        /**
         * 题目ID
         */
        private String subjectId;
        
        /**
         * 方面详情
         */
        private String aspectDetail;
        
        /**
         * 项目列表
         */
        private List<MacroEconomicItem> items;
    }
    
    @Data
    public static class MacroEconomicItem {
        /**
         * 小标题
         */
        private String subtitle;
        
        /**
         * 内容
         */
        private String content;
    }
}
