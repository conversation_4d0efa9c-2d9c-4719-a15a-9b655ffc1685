package ttfund.web.fortuneservice.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class InterfaceFundDayDTO {
    private String FUNDCODE;
    private String SHORTNAME;
    private String JJGSID;
    private String FUNDTYPE;
    private Date PDATE;
    private BigDecimal POSIVOL_AMT;
    private Integer ALLNUM;
    private Integer HOLDNUM;
    private Integer ZERONUM;
    private Integer DAYZEROOLD;
    private Integer FIRSTNUM;
    private BigDecimal PERNUM;
    private Integer STOCKNUM;
    private BigDecimal POSIVOL;
    private Integer DAYZERONUM;
    private String FEATURETYPE;
    private String FEATURE;
    private Integer NUM;
    private String CYSC;
}
