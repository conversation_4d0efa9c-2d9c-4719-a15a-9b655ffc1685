package ttfund.web.fortuneservice.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/30 9:24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskNoticeConfigDto {

    @JSONField(name = "ID")
    private String id;
    @JSONField(name = "NoticeType")
    private String noticeType;
    @JSONField(name = "NoticeTitle")
    private String noticeTitle;
    @JSONField(name = "NoticeContent")
    private String noticeContent;
    @JSONField(name = "Description")
    private String description;

}
