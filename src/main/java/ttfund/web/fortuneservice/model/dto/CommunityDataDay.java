package ttfund.web.fortuneservice.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommunityDataDay {

    /**
     * 唯一id，date_cfhid
     */
    private String _id;
    /**
     * 日期
     */
    private String date;
    private String CFHID;
    private String companyId;

    /**
     * 访问量 = 【（访问人数/社群用户总数）^（2/3）】*社群用户总数，向上取整为整数
     */
    private Long visitorsNum;
    private Long visitorsNumReal;

    /**
     * 保有用户数
     */
    private Integer userNum;

    /**
     * 发言数
     */
    private Integer speakNum;
    private Integer speakUserNum;

    /**
     *入群用户数
     */
    private Integer userInNum;

    /**
     *出群用户数
     */
    private Integer userOutNum;

    /**
     * 月访问天数，近30天人均访问群聊的天数
     */
    private Double visitDaysPreMonth;

    /**
     * 交易转化分 = √（实际交易金额/所有机构max交易金额）*100
     */
    private Integer payScore;

    /**
     * 发言转化分 = √（整体发言人数/所有机构max发言人数）*50+√（整体发言消息数/所有机构max发言消息数）*50
     */
    private Integer speakScore;

    /**
     * 活跃分 = √（实际访问人数/所有机构max访问人数）*50+实际月访天数/所有机构max月访天数*50
     */
    private Integer activeScore;

    /**
     * 综合分 = 活跃分50%+发言互动分25%+交易转化分25%
     */
    private Integer comprehensiveScore;

    private Date UpdateTime;

    private Date CreateTime;
}
