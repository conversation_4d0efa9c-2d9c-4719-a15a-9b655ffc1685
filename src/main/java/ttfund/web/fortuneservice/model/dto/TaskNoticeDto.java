package ttfund.web.fortuneservice.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/1/30 9:24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskNoticeDto {

    @J<PERSON><PERSON>ield(name = "ID")
    private String id;
    @JSONField(name = "Title")
    private String title;
    @J<PERSON>NField(name = "Content")
    private String content;
    @JSONField(name = "EnumTypeID")
    private Integer type;
    @JSONField(name = "NoticeObject")
    private String noticeObj;
    @J<PERSON>NField(name = "Status")
    private Integer status;
    @JSONField(name = "CreatTime")
    private Date createTime;
    @J<PERSON>NField(name = "UpDateTime")
    private Date updateTime;
    @J<PERSON><PERSON>ield(name = "CFHID")
    private String cfhId;
    @JSONField(name = "MgrName")
    private String mgrName;
    @J<PERSON>NField(name = "ReadCFH")
    private String readCfh;

    /**
     * 任务名称
     **/
    private String taskName;

    /**
     * 任务结束时间
     **/
    private Date taskEndTime;
}
