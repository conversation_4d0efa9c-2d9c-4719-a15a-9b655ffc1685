package ttfund.web.fortuneservice.model.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CFHTradeRankDataDTO {
    private String JJGSID;//基金公司id
    private String FUNDTYPE;//基金类型
    private String calc_date;//统计日期
    private BigDecimal POSI_AMT;//持有金额
    private Integer POSI_AMT_RANK;//持有金额排名
    private BigDecimal POSI_NUM;//持有人数
    private Integer POSI_NUM_RANK;//持有人数排名
    private BigDecimal POSI_PER;//人均持有金额
    private Integer POSI_PER_RANK;//人均持有金额排名
    private BigDecimal POSI_AMT_RATE;//增长率
    private Integer POSI_AMT_RATE_RANK;//增长率排名

    public BigDecimal getPOSI_AMT() {
        return POSI_AMT == null ? BigDecimal.ZERO : POSI_AMT;
    }

    public BigDecimal getPOSI_NUM() {
        return POSI_NUM == null ? BigDecimal.ZERO : POSI_NUM;
    }

    public BigDecimal getPOSI_PER() {
        return POSI_PER == null ? BigDecimal.ZERO : POSI_PER;
    }

    public BigDecimal getPOSI_AMT_RATE() {
        return POSI_AMT_RATE == null ? BigDecimal.ZERO : POSI_AMT_RATE;
    }
}
