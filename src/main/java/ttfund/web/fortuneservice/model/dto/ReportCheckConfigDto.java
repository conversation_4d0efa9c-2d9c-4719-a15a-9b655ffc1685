package ttfund.web.fortuneservice.model.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/6/8 9:59
 * 报告检查日期、命名配置实体类
 */
@Data
public class ReportCheckConfigDto {
    /**
     * 在几月份进行检查
     */
    private int month;
    /**
     * 在第几个交易日进行检查
     */
    private int tradeDay;
    /**
     * 交易日排序规则 true：正向排序，false：反向排序
     */
    private boolean sort;
    /**
     * 需要检查的报告名称格式
     */
    private String reportName;

    /**
     * 上一次需要检查的报告名称格式
     */
    private String lastId;
    /**
     * 检查生成的报告名称
     */
    private String checkReportName;
    /**
     * 年份偏移量（用于生成需要的产品报告名称以及最终生成的检查报告名称）
     */
    private int yearAdd;
    /**
     * 生成的报告是否需要基金规模
     */
    private boolean needFundSize;
}
