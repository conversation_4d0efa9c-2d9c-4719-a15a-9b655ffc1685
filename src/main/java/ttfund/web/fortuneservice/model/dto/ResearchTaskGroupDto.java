package ttfund.web.fortuneservice.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * ResearchTaskGroup.java
 *
 * <AUTHOR>
 * @date 2023/4/26 17:20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResearchTaskGroupDto {

    /**
     * 唯一ID
     **/
    private String ID;

    /**
     * 任务组名称
     **/
    private String TaskGroupName;

    /**
     * 分组内财富号ID，多个以逗号分隔
     **/
    private String TaskGroupContent;

    /**
     * 删除状态：0-未删除，1-已删除
     **/
    private Integer IsDel;

    /**
     * 更新时间
     **/
    private Date UpdateTime;

    /**
     * 创建时间
     **/
    private Date CreateTime;
}
