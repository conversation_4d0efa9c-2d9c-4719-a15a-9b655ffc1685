package ttfund.web.fortuneservice.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * ResearchTaskSubjectDto.java
 *
 * <AUTHOR>
 * @date 2023/4/28 15:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResearchTaskSubjectDto {
    /**
     * 唯一ID
     **/
    private String ID;

    /**
     * 任务配置ID
     **/
    private String TaskConfigID;

    /**
     * 任务周期标识（只有自动下发给机构的任务才会有值）
     **/
    private String TaskCycleMark;

    /**
     * 题目标题
     **/
    private String SubjectTitle;

    /**
     * 题目代码
     **/
    private String SubjectCode;

    /**
     * 题目类型（1大类资产，2行业）
     **/
    private Integer SubjectType;

    /**
     * 是否重点（0否，1是）
     **/
    private String SubjectImportant;

    /**
     * 删除状态：0-未删除，1-已删除
     **/
    private Integer IsDel;

    /**
     * 更新时间
     **/
    private Date UpdateTime;

    /**
     * 操作人
     **/
    private String Operator;

    /**
     * 创建时间
     **/
    private Date CreateTime;

    /**
     * 点位扩展数据
     */
    private String extend;

    private Integer sort;

    private String TaskSubjectType;
}
