package ttfund.web.fortuneservice.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

@Data
public class QuarterlyReportDto {

    @JSONField(name = "FCODE")
    private String fcode;

    @JSONField(name = "REPORTDATE")
    private String reportDate;

    /**
     *  1 年报 2 半年报 3 季报
     */
    @JSONField(name = "STYLE")
    private String type;

    @JSONField(name = "WARNNUM")
    private Integer warnNum;

    @JSONField(name = "CREATETIME")
    private Date createTime;


}
