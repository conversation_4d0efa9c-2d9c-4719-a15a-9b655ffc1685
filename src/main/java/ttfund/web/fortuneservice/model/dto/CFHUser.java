package ttfund.web.fortuneservice.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
public class CFHUser implements Serializable {
    private String id;

    private String phone;

    private Date createTime;

    private Date updateTime;

    private Integer isDelete;

    private Date auditTime;

    private String CFHID;

    private String uid;

    private String userName;

    private String senstiveDataStatus;

    private String roleIds;

    private String operator;

    private String auditDecription;

    private String businessId;

    private String relatedUid;
    private String cfhName;
    private String role;

    private static final long serialVersionUID = 1L;

}