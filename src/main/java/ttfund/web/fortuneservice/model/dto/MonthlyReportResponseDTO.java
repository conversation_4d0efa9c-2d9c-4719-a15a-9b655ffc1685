package ttfund.web.fortuneservice.model.dto;

import lombok.Data;

/**
 * 月度报告响应DTO
 */
@Data
public class MonthlyReportResponseDTO {
    
    /**
     * 机构名称
     */
    private String institutionName;
    
    /**
     * 任务月份
     */
    private String taskMonth;
    
    /**
     * 机构总结
     */
    private String institutionSummary;
    
    /**
     * 模块总结
     */
    private ModuleSummary moduleSummary;
    
    /**
     * 生成时间
     */
    private String generatedTime;
    
    /**
     * 处理时间(毫秒)
     */
    private Long processingTime;
    
    /**
     * AI模型
     */
    private String aiModel;
    
    @Data
    public static class ModuleSummary {
        /**
         * 行业总结
         */
        private String industrySummary;
        
        /**
         * 其他大类总结
         */
        private String otherCategorySummary;
        
        /**
         * 资产配置总结
         */
        private String assetAllocationSummary;
        
        /**
         * 宏观经济总结
         */
        private String macroEconomicSummary;
    }
}
