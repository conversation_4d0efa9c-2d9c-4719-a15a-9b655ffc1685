package ttfund.web.fortuneservice.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * FundSizeInfo.java
 *
 * <AUTHOR>
 * @date 2023/5/10 13:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FundSizeDto {

    /**
     * 基金代码
     */
    private String fundCode;
    /**
     * 基金名称
     */
    private String fundName;
    /**
     * 基金规模
     */
    private BigDecimal fundSize;
}
