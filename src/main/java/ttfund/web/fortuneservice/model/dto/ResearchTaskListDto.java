package ttfund.web.fortuneservice.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * ResearchTaskListDto.java
 *
 * <AUTHOR>
 * @date 2023/4/26 17:14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResearchTaskListDto {

    /**
     * 唯一ID
     **/
    private String ID;

    /**
     * 财富号ID
     **/
    private String CFHID;

    /**
     * 任务配置ID
     **/
    private String TaskConfigID;

    /**
     * 任务周期标识
     **/
    private String TaskCycleMark;

    /**
     * 任务名称
     **/
    private String TaskName;

    /**
     * 任务周期
     **/
    private String TaskContent;

    /**
     * 任务文件地址
     **/
    private String TaskFileUrl;

    /**
     * 任务类型
     **/
    private Integer TaskType;

    /**
     * 任务开始时间
     **/
    private Date TaskBeginTime;

    /**
     * 任务结束时间
     **/
    private Date TaskEndTime;

    /**
     * 任务来源：0-天天基金自动下发、1-机构自建任务
     **/
    private Integer TaskSource;

    /**
     * 回答文件地址
     **/
    private String AnswerFileUrl;

    /**
     * 任务状态：0-未完成、1-已完成
     **/
    private Integer Status;

    /**
     * 删除状态：0-未删除，1-已删除
     **/
    private Integer IsDel;

    /**
     * 更新时间
     **/
    private Date UpdateTime;

    /**
     * 创建时间
     **/
    private Date CreateTime;
}
