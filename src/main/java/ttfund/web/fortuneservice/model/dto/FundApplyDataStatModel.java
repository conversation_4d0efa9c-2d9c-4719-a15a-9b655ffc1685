package ttfund.web.fortuneservice.model.dto;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/2/3 17:06
 * 执行sql统计BUSIN.TTTRADEDETAIL_BASIC_SYN得到的实体类
 */
public class FundApplyDataStatModel {
    /**
     * 基金代码
     */
    private String fundcode;

    /**
     * 基金公司id
     */
    private String jjgsid;

    /**
     * 交易类型
     */
    private String busintype;
    /**
     * 申请日期 yyyy-MM-dd形式
     */
    private Date Applyday;
    /**
     * 申请小时数
     */
    private Integer Applyhour;
    /**
     * 申请数额
     */
    private BigDecimal applyAmount;
    /**
     * 申请数量
     */
    private BigDecimal applynum;
    /**
     * 基金名称
     */
    private String shortname;
    /**
     * 交易日期
     */
    private Date transactionDate;

    public FundApplyDataStatModel() {
    }

    public String getFundcode() {
        return fundcode;
    }

    public void setFundcode(String fundcode) {
        this.fundcode = fundcode;
    }

    public String getJjgsid() {
        return jjgsid;
    }

    public void setJjgsid(String jjgsid) {
        this.jjgsid = jjgsid;
    }

    public String getBusintype() {
        return busintype;
    }

    public void setBusintype(String busintype) {
        this.busintype = busintype;
    }

    public Date getApplyday() {
        return Applyday;
    }

    public void setApplyday(Date applyday) {
        Applyday = applyday;
    }

    public Integer getApplyhour() {
        return Applyhour;
    }

    public void setApplyhour(Integer applyhour) {
        Applyhour = applyhour;
    }

    public BigDecimal getApplyAmount() {
        return applyAmount;
    }

    public void setApplyAmount(BigDecimal applyAmount) {
        this.applyAmount = applyAmount;
    }

    public BigDecimal getApplynum() {
        return applynum;
    }

    public void setApplynum(BigDecimal applynum) {
        this.applynum = applynum;
    }

    public String getShortname() {
        return shortname;
    }

    public void setShortname(String shortname) {
        this.shortname = shortname;
    }

    public Date getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(Date transactionDate) {
        this.transactionDate = transactionDate;
    }

    @Override
    public String toString() {
        return "FundApplyDataStatModel{" +
                "fundcode='" + fundcode + '\'' +
                ", jjgsid='" + jjgsid + '\'' +
                ", busintype='" + busintype + '\'' +
                ", Applyday=" + Applyday +
                ", Applyhour=" + Applyhour +
                ", applyAmount=" + applyAmount +
                ", applynum=" + applynum +
                ", shortname='" + shortname + '\'' +
                ", transactionDate=" + transactionDate +
                '}';
    }
}
