package ttfund.web.fortuneservice.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CfhListDto {

    @ApiModelProperty(value = "_id")
    private String ID;

    @ApiModelProperty(value = "财富号id")
    private String CFHID;

    @ApiModelProperty(value = "财富号名字")
    private String CFHName;

    @ApiModelProperty(value = "状态Status=1或者10，10是测试财富号")
    private Integer Status;

    @ApiModelProperty(value = "权重值")
    private Integer DefaultWeight;

    @ApiModelProperty(value = "开始生效时间")
    private Long WeightStartTimeStamp;

    @ApiModelProperty(value = "结束生效时间")
    private Long WeightEndTimeStamp;
}
