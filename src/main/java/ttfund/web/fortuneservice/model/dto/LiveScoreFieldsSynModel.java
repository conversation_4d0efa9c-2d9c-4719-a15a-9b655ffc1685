package ttfund.web.fortuneservice.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LiveScoreFieldsSynModel {
    private String _id;
    Integer LIVEID;
    String Channel_ID;
    Date STARTTIME;
    Date ENDTIME;
    String TITLE;
    String CFHID;
    String CFHNAME;
    Integer STATUS;
    Integer ISDELETE;
    Integer ISHIDE;
    Double HEAT_DEGREE;
    Double LIKE_NUM;
    Double CLICKNUM;
    Double CLICKUSERNUM;
    Double REPLYUSERNUM;
    Double AVGDURATION;
    Double FUNDCLICKRATIO;
    Double SHARERATIO;
    Double FOLLOWRATIO;
    Double REPLYRATIO;
    Double TOTAL_SCORE;
    Double WATCH_SCORE;
    Double INTER_SCORE;
    Double FUND_SCORE;
    Date LIVESCORE_UPDATETIME ;
    Double FavorCustNum ;
    Double PlanNum ;//创建定投计划数
    Double tinusernum ;//入金人数
    Double tinamt ;//入金金额
    Date LIVESALE_UPDATETIME ;
}
