package ttfund.web.fortuneservice.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 高端理财-定期检测报告内容
 *
 * <AUTHOR>
 * @date 2023/5/6 15:05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RegularInspectionReportContentDto {

    /**
     * 唯一ID
     **/
    private String reportContentId;

    /**
     * 报告ID
     **/
    private String reportId;

    /**
     * 产品代码
     **/
    private String productCode;

    /**
     * 产品名称
     **/
    private String productName;

    /**
     * 基金规模
     **/
    private Double fundSize;

    /**
     * 缺少披露信息类型 （1：季报 / 2：净值 / 3：年报）
     **/
    private Integer missingInformationType;

    /**
     * 创建时间
     **/
    private Date createTime;

    /**
     * 基金是否关停
     */
    private Boolean isClose = Boolean.FALSE;
    /**
     * 基金是否持仓
     */
    private Boolean isHold = Boolean.FALSE;

    /**
     * 成立日期
     */
    private Date stabilshDate;

    /**
     * 管理人
     */
    private String manager;
    /**
     * 最新净值日期
     */
    private Date pDate;
}
