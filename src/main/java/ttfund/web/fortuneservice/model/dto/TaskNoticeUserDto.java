package ttfund.web.fortuneservice.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/1/30 9:24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskNoticeUserDto {

    @JSONField(name = "ID")
    private String id;
    @JSONField(name = "CFHID")
    private String cfhId;
    @JSONField(name = "Name")
    private String name;
    @JSONField(name = "Mobile")
    private String mobile;
    @JSONField(name = "CustomerNo")
    private String customerNo;

}
