package ttfund.web.fortuneservice.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className CFHBaseInfoDto
 * @date 2023/4/14 15:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CFHBaseInfoDto {

    private String ID;
    private String CFHID;
    private String CFHName;
    private String CommpanyCode;//基金公司代码
    private String CommpanyName;//账富号名称
    private String Summary;//财富号简介
    private String HeaderImgPath;//财富号头像
    private Integer Status = 0;//状态
    private Date CreatTime;//创建时间
    private Date UpDataTime;//更新时间
    private String Slogans;//财富号标语
    private Integer Purview;
    private String Operator;
    private Integer PushPurview;
    private String PushCategoryCode;
    private String RoleId;
    private String JianPin;
    private String OrganizationType;
    private String RelatedUid;//通行证Id
}
