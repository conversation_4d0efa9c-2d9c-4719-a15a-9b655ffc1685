package ttfund.web.fortuneservice.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @className CFHProductReadDto
 * @date 2023年7月19日
 * SQL server实体,mongo也用这个
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CFHProductReadDto {

    private String ID;
    private String CFHID;
    /**
     * 展示位置
     */
    private Integer ShowPosition;
    private Integer ProductType;
    private String FundCode;
    private Integer ReadType;
    private String ReadTitle;
    private String ReadDecription;
    private Integer LinkType;
    private String LinkCode;
    private String SpecialUserGroupID;
    private Date StartTime;
    private Date EndTime;
    private Date CreateTime;
    private Date UpdateTime;
    private Integer Status;
    private String VerifyUser;
    private String ApproveDescription;
    private Integer IsShow;
    private String JsonAudio;

    //mongo数据
    private String[] FundCodeArry;
    private String H5LinkUrl;
    private String H5UseLinkUrl;
    private String AppUseLinkUrl;
    private VedioAudioJson JsonAudioModel;
    private int ReportType;

    private Integer WarnNum;


}
