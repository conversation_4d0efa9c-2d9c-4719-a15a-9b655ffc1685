package ttfund.web.fortuneservice.model.dto;

import lombok.Data;
import java.util.List;

/**
 * 多机构月度报告响应DTO
 */
@Data
public class MultiMonthlyReportResponseDTO {

    /**
     * 机构数量
     */
    private Integer institutionCount;

    /**
     * 任务月份
     */
    private String taskMonth;

    /**
     * 整体总结
     */
    private OverallSummary overallSummary;

    /**
     * 行业分析
     */
    private AnalysisSection industryAnalysis;

    /**
     * 非行业分析
     */
    private AnalysisSection nonIndustryAnalysis;

    /**
     * 资产配置分析
     */
    private AnalysisSection assetAllocationAnalysis;

    /**
     * 宏观经济分析
     */
    private AnalysisSection macroEconomicAnalysis;

    /**
     * 生成时间
     */
    private String generatedTime;

    /**
     * 处理时间(毫秒)
     */
    private Long processingTime;

    /**
     * AI模型
     */
    private String aiModel;

    @Data
    public static class OverallSummary {
        /**
         * 整体总结
         */
        private List<String> overallSummary;

        /**
         * 相似点
         */
        private List<SimilarityDifference> similarities;

        /**
         * 差异点
         */
        private List<SimilarityDifference> differences;
    }

    @Data
    public static class AnalysisSection {
        /**
         * 相似点
         */
        private List<SimilarityDifference> similarities;

        /**
         * 差异点
         */
        private List<SimilarityDifference> differences;
    }

    @Data
    public static class SimilarityDifference {
        /**
         * 标题
         */
        private String title;

        /**
         * 内容
         */
        private String content;

        /**
         * 来源引用
         */
        private List<SourceReference> sourceReferences;

        /**
         * 观点列表
         */
        private List<Viewpoint> viewpoints;
    }

    @Data
    public static class SourceReference {
        /**
         * 财富号ID
         */
        private String cfhId;

        /**
         * 题目ID
         */
        private String subjectId;
    }

    @Data
    public static class Viewpoint {
        /**
         * 观点标签
         */
        private String viewpointLabel;

        /**
         * 内容
         */
        private String content;

        /**
         * 来源引用
         */
        private List<SourceReference> sourceReferences;
    }
}
