package ttfund.web.fortuneservice.model.cfhkafka;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2021/12/21 15:17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CFHNoticeModel {
    /**
     * 消息id
     */
    private String noticeid;
    /**
     * 通知消息类型：1.财富号账户（审核、禁言等） 2.财富号文章（审核结果、收到打赏等） 3.任务通知
     */
    private int noticetype;
    /**
     * 通知消息状态
     * （-1 正常 0 图片违规 1 文章图片违规 2 正文涉及敏感词 3 屏蔽作者 4 用户举报文章 5 财富号审核通过通知
     * 6 财富号审核未通过通知 7 财富号加V认证通过通知 8 财富号加V认证未通过通知 9 个人信息修改审核通过通知  10 个人信息修改审核未通过通知
     * 11 RSS源审核通过通知 12 RSS源审核未通过通知 13 第三方供稿审核通过通知 14 第三方供稿审核未通过通知 15 用户举报文章申诉通过通知
     * 16 用户举报文章申诉未通过通知 17 财富号禁言申诉通过通知 18 财富号禁言申诉未通过通知 19 财富号广告通过通知 20 财富号广告未通过通知
     * 21 财富号打赏通过通知 22 财富号打赏未通过通知 23 财富号打赏支付通知 24 财富号原创标签通过通知 25 财富号原创标签未通过通知
     * 26 财富号原创功能开通通知 27 财富号原创功能被关闭通知 28 财富号原创功能被禁用通知）
     */
    private int noticestatus;
    /**
     * 通行证uid
     */
    private String uid;
    /**
     * 财富号作者id
     */
    private String authorid;
    /**
     * 通行证昵称
     */
    private String nickname;
    /**
     * 通知消息标题
     */
    private String title;
    /**
     * 通知消息内容
     */
    private String content;
    /**
     * 消息关联id 如NoticeType=2，SourceId字段值为财富号文章id
     */
    private String sourceid;
    /**
     * 文章id
     */
    private String artcode;
    /**
     * 文章标题
     */
    private String arttitle;
    /**
     * 消息创建时间
     */
    private String createtime;
    /**
     * 消息更新时间
     */
    private String updatetime;
    /**
     * 账户禁言结束时间
     */
    private String silenceendtime;
    /**
     * 1.针对个人推送 2: 全局推送
     */
    private int pushtype;

    private String taskname;
    private String taskendtime;
}
