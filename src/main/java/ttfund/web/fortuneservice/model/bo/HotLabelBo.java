package ttfund.web.fortuneservice.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class HotLabelBo {

    @ApiModelProperty("标签id")
    private String labelId;

    @ApiModelProperty("标签")
    private String label;

    @ApiModelProperty("出现次数")
    private Integer times;

    @ApiModelProperty("最近时间")
    private Long lastTime;

}
