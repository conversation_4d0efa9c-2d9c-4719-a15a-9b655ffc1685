package ttfund.web.fortuneservice.model.bo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className CFHBaseInfoBo
 * @date 2023/4/14 15:16
 */
@Data
public class CFHBaseInfoBo {

    /**
     * id
     */
    private String accountId;
    /**
     * 名称
     */
    private String accountName;

    private String RelatedUid;

    /**
     * 财富号类型
     */
    private String OrganizationType;

    /**
     * 基金公司id，当推送的是基金的数据用这个字段
     */
    private String fundCompanyParam;
    /**
     * 私募旧机构代码，当推送是私募的数据用这个
     */
    private String privateFundParam;
    /**
     * 私募新机构代码，当推送是私募的数据用这个
     */
    private String newPrivateFundParam;

    private String hotType;

    private String email;

    private String spreadId;

    private String pageManagement;

    private String nickName;

    private String banner;

    private String portrait;
    /**
     * 私募财富号
     */
    private String name;

    private String summary;

    private String organizationTag;

    private String bigVip;

    private String accountType;

    private Integer pageState;

    private String accountStatus;

    private String approvalStatus;

    private Integer isDeleted;

    private Date createTime;

    private Date updateTime;
}
