package ttfund.web.fortuneservice.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description : 主题响应成员实体
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @Date: 2023/4/24  11:19
 */
@Data
public class FundThemeModel {
    @ApiModelProperty("主题id")
    private String themeId;

    @ApiModelProperty("主题名称")
    private String themeName;

    public FundThemeModel(String themeId, String themeName) {
        this.themeId = themeId;
        this.themeName = themeName;
    }

    public FundThemeModel() {
    }
}
