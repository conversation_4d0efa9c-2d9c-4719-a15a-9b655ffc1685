package ttfund.web.fortuneservice.model.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> dengchaojun
 * @version : 1.0
 * @email : <EMAIL>
 * @date : 2021-07-27 11:27
 * @description : 天天快评传输对象
 */
@ApiModel("天天快评传输对象")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReviewInfoResponse implements Serializable {

    @ApiModelProperty("评论卡片ID")
    private String id;

    @ApiModelProperty("更新时间-发布时间")
    private String publishTime;

    @ApiModelProperty("财富号id")
    private String cfhId;

    @ApiModelProperty("财富号名称")
    private String cfhName;

    @ApiModelProperty("基金经理id")
    private String mgrId;

    @ApiModelProperty("基金经理名称")
    private String mgrName;

    @ApiModelProperty("快评标题")
    private String title;

    @ApiModelProperty("快评内容")
    private String reviewContent;

    @ApiModelProperty("情绪类型, 0乐观, 1中性, 2谨慎")
    private Integer emotionType;

    @ApiModelProperty("产品类型, 1普通基金, 6组合, 15投顾")
    private Integer productType;

    @ApiModelProperty("产品代码")
    private String productCode;

    /**
     * ',' 分割"
     */
    @ApiModelProperty("产品标签")
    private List<String> productTags;

    @ApiModelProperty("产品类型2, 1普通基金, 6组合, 15投顾")
    private Integer productType2;

    @ApiModelProperty("产品代码")
    private String productCode2;

    /**
     * ',' 分割
     */
    @ApiModelProperty("产品标签2")
    private List<String> productTags2;
    @ApiModelProperty("时间戳")
    private String timepoint;

    /**
     * 产品收益图片地址(仅限组合产品使用)
     */
    private String productSyImg;
    /**
     * 产品收益图片地址(仅限组合产品使用)
     */
    private String productSyImg2;

    @ApiModelProperty("权重")
    private Integer Weight = 0;

    @ApiModelProperty("主题列表")
    private List<FundThemeModel> ThemeLabel;

    @ApiModelProperty("标签类型：0赛道标签，1观点标签")
    private Integer LabelType;
}
