package ttfund.web.fortuneservice.model.bo;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/8/3 14:23
 */
public class AuthorDetailBo {

    /**
     * 作者ID
     */
    private int Account_Id;

    /**
     * Email
     */
    private String Email;

    /**
     * 手机号码
     */
    private String MobilePhone;

    /**
     * 昵称
     */
    private String NickName;

    /**
     * 头像
     */
    private String Portrait;

    /**
     * 简介
     */
    private String Summary;

    /**
     * 机构标识
     */
    private int OrganizationTag;

    /**
     * 机构信息
     */
    private String QualificationInfo;

    /**
     * 是否加V(0未加,1已加)
     */
    private int BigVip;

    /**
     * 关联通行证ID
     */
    private String RelatedUid;

    /**
     * 财富号名称
     */
    private String AccountName;

    /**
     * 财富号类型
     */
    private String AccountType;

    /**
     * 账号状态(0未激活,1已激活,-1已冻结)
     */
    private Integer AccountStatus;

    /**
     * 审核状态(0未审核,1身份审核已批准,2人工审核已批准)
     */
    private int ApprovalStatus;

    /**
     * 文章数
     */
    private long ArticleCount;

    /**
     * 点击人数
     */
    private long PVCount;

    /**
     * 订阅数
     */
    private long SubscriberCount;

    /**
     * 粉丝数
     */
    private long FandsCount;

    /**
     * 创建时间
     */
    private Date CreateTime;

    /**
     * 用户广告状态 null或0. 未开通 1. 已开通 2. 关闭
     */
    private Integer AdStatus;

    private Boolean IsFund;

    public int getAccount_Id() {
        return Account_Id;
    }

    public void setAccount_Id(int account_Id) {
        Account_Id = account_Id;
    }

    public String getEmail() {
        return Email;
    }

    public void setEmail(String email) {
        Email = email;
    }

    public String getMobilePhone() {
        return MobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        MobilePhone = mobilePhone;
    }

    public String getNickName() {
        return NickName;
    }

    public void setNickName(String nickName) {
        NickName = nickName;
    }

    public String getPortrait() {
        return Portrait;
    }

    public void setPortrait(String portrait) {
        Portrait = portrait;
    }

    public String getSummary() {
        return Summary;
    }

    public void setSummary(String summary) {
        Summary = summary;
    }

    public int getOrganizationTag() {
        return OrganizationTag;
    }

    public void setOrganizationTag(int organizationTag) {
        OrganizationTag = organizationTag;
    }

    public String getQualificationInfo() {
        return QualificationInfo;
    }

    public void setQualificationInfo(String qualificationInfo) {
        QualificationInfo = qualificationInfo;
    }

    public int getBigVip() {
        return BigVip;
    }

    public void setBigVip(int bigVip) {
        BigVip = bigVip;
    }

    public String getRelatedUid() {
        return RelatedUid;
    }

    public void setRelatedUid(String relatedUid) {
        RelatedUid = relatedUid;
    }

    public String getAccountName() {
        return AccountName;
    }

    public void setAccountName(String accountName) {
        AccountName = accountName;
    }

    public String getAccountType() {
        return AccountType;
    }

    public void setAccountType(String accountType) {
        AccountType = accountType;
    }

    public Integer getAccountStatus() {
        return AccountStatus;
    }

    public void setAccountStatus(Integer accountStatus) {
        AccountStatus = accountStatus;
    }

    public int getApprovalStatus() {
        return ApprovalStatus;
    }

    public void setApprovalStatus(int approvalStatus) {
        ApprovalStatus = approvalStatus;
    }

    public long getArticleCount() {
        return ArticleCount;
    }

    public void setArticleCount(long articleCount) {
        ArticleCount = articleCount;
    }

    public long getPVCount() {
        return PVCount;
    }

    public void setPVCount(long PVCount) {
        this.PVCount = PVCount;
    }

    public long getSubscriberCount() {
        return SubscriberCount;
    }

    public void setSubscriberCount(long subscriberCount) {
        SubscriberCount = subscriberCount;
    }

    public long getFandsCount() {
        return FandsCount;
    }

    public void setFandsCount(long fandsCount) {
        FandsCount = fandsCount;
    }

    public Date getCreateTime() {
        return CreateTime;
    }

    public void setCreateTime(Date createTime) {
        CreateTime = createTime;
    }

    public Integer getAdStatus() {
        return AdStatus;
    }

    public void setAdStatus(Integer adStatus) {
        AdStatus = adStatus;
    }

    public Boolean getIsFund() {
        return IsFund;
    }

    public void setIsFund(Boolean fund) {
        IsFund = fund;
    }
}
