package ttfund.web.fortuneservice.model.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023年6月1日
 */
@Data
public class ReviewOpinion {
    @ApiModelProperty("标签id")
    private String labelId;
    @ApiModelProperty("标签，名称")
    private String label;

    @ApiModelProperty("乐观")
    private Integer optimism=0;

    @ApiModelProperty("中性")
    private Integer neuter=0;

    @ApiModelProperty("悲观")
    private Integer gloomy=0;
    @ApiModelProperty("总数")
    private Integer total=0;

    public ReviewOpinion(String labelId, String label) {
        this.labelId = labelId;
        this.label = label;
    }

    public ReviewOpinion() {
    }

    public Integer getTotal() {
        return optimism + neuter + gloomy;
    }
}
