package ttfund.web.fortuneservice.model.bo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import ttfund.web.fortuneservice.model.dto.QuickReviewDto;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PopularManagerBo {

    @ApiModelProperty("财富号id")
    @JSONField(name = "CFHID")
    private String CFHID;

    @ApiModelProperty("基金经理id")
    @JSONField(name = "MGRID")
    private String MGRID;

    @ApiModelProperty("基金经理名称")
    @JSONField(name = "MGRName")
    private String MGRName;

    @ApiModelProperty("快评文章列表")
    @JSONField(name = "reviewList")
    private List<QuickReviewDto> reviewList;

    @ApiModelProperty("最新一个快评文章")
    @JSONField(name = "lastTimePoint")
    private Long lastTimePoint;

    @ApiModelProperty("基金经理权重")
    @JSONField(name = "weight")
    private Integer weight;

    @ApiModelProperty("手动权重")
    @JSONField(name = "handWeight")
    private Integer handWeight;

    public PopularManagerBo(String cfhId, String mgrId, String mgrName, List<QuickReviewDto> reviewList, Long lastTimePoint, Integer handWeight) {
        this.CFHID = cfhId;
        this.MGRID = mgrId;
        this.MGRName = mgrName;
        this.reviewList = reviewList;
        this.lastTimePoint = lastTimePoint;
        this.handWeight = handWeight;
    }
}
