import com.ttfund.web.core.constant.CoreConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.log.XxlJobLogger;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.fortuneservice.FortuneServiceApplication;
import ttfund.web.fortuneservice.job.CFHFInfoUpdateJob;
import ttfund.web.fortuneservice.service.CommunityDataService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description TestJob。java
 * @date 2023/8/3 18:17
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = FortuneServiceApplication.class)
public class TestJob {

    @Resource
    CFHFInfoUpdateJob cfhfInfoUpdateJob;

    @Test
    public void testCFHFInfoUpdateJob() throws Exception {
        cfhfInfoUpdateJob.execute(null);
    }
    @Autowired
    CommunityDataService communityDataService;
    @Test
    public void testCommunityDataService(){
        communityDataService.handlerData(null);

    }
}
