package ttfund.web.fortuneservice.service.impl;

import com.mongodb.BasicDBObject;
import com.mongodb.MongoClient;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.UpdateOptions;
import com.mongodb.client.result.UpdateResult;
import com.ttfund.web.base.helper.MongodbHelper;
import org.bson.BsonValue;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import ttfund.web.fortuneservice.config.App;
import ttfund.web.fortuneservice.constant.HqMongodbConstant.*;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.when;

/**
 * ProductReadServiceImpl 的测试用例  100% methods,99% lines covered
 */
@ExtendWith(MockitoExtension.class)
public class ProductReadServiceImplTest {

    @Mock
    private App app;

    @Mock
    private CfhMongodbDao cfhMongodbDao;

    @InjectMocks
    private ProductReadServiceImpl productReadServiceImpl;

    private Date nowDate;

    @BeforeEach
    void setUp() {
        nowDate = new Date();
    }

    @Test
    void testWriteProductReadIntoHq() {

        MongodbHelper mongodbHelper = mock(MongodbHelper.class);
        MongoClient mongoClient = mock(MongoClient.class);
        MongoDatabase database = mock(MongoDatabase.class);
        MongoCollection<Document> collection = mock(MongoCollection.class);
        when(app.getHqMongodbRead()).thenReturn(mongodbHelper);
        when(mongodbHelper.getMongoserver()).thenReturn(mongoClient);
        when(mongoClient.getDatabase(anyString())).thenReturn(database);
        when(database.getCollection(anyString())).thenReturn(collection);
        when(collection.countDocuments(any(Bson.class))).thenReturn(5L);
        UpdateResult result = new UpdateResult() {
            @Override
            public boolean wasAcknowledged() {
                return false;
            }

            @Override
            public long getMatchedCount() {
                return 0;
            }

            @Override
            public boolean isModifiedCountAvailable() {
                return false;
            }

            @Override
            public long getModifiedCount() {
                return 0;
            }

            @Override
            public BsonValue getUpsertedId() {
                return null;
            }
        };
        when(app.getCfhMongodbWrite()).thenReturn(mongodbHelper);
        when(collection.updateOne(any(BasicDBObject.class), any(BasicDBObject.class), any(UpdateOptions.class))).thenReturn(result);

        // 模拟私募公告表和公募公告表的查询结果
        List<Document> privateNoticeList = Arrays.asList(
                new Document(FundNoticeField.F_CODE, "123456,789012,345678")
                        .append(FundNoticeField.FUND_TYPE, "Equity Fund")
                        .append(HqCommonField.EID, "a1b2c3d4e5"),
                new Document(FundNoticeField.F_CODE, "987654,321098,654321")
                        .append(FundNoticeField.FUND_TYPE, "Bond Fund")
                        .append(HqCommonField.EID, "f6g7h8i9j0"),
                new Document(FundNoticeField.F_CODE, "987654,321098,654321")
                        .append(FundNoticeField.FUND_TYPE, "Bond Fund")
                        .append(HqCommonField.EID, "f6g7h8i9j0")
        );

        List<Document> typeNoticeList = Arrays.asList(
                new Document(FundNoticeField.FUND_TYPE, "ETF,REITS")
                        .append(FundNoticeField.F_CODE, "987654,123456"),
                new Document(FundNoticeField.FUND_TYPE, "")
                        .append(FundNoticeField.F_CODE, "123456")
        );

        List<Document> documentList = Arrays.asList(
                new Document(FundNoticeField.F_CODE, "987654")
        );

        Mockito.when(app.getHqMongodbRead().query(anyString(), anyString(), any(BasicDBObject.class), any(BasicDBObject.class), any(BasicDBObject.class), anyInt(), anyInt(), eq(Document.class)))
                .thenReturn(privateNoticeList, privateNoticeList, typeNoticeList,documentList);

        productReadServiceImpl.writeProductReadIntoHq(nowDate);

        // 验证方法调用次数
        verify(app.getHqMongodbRead(), times(3)).query(anyString(), anyString(), any(BasicDBObject.class), any(BasicDBObject.class), any(BasicDBObject.class), anyInt(), anyInt(), eq(Document.class));
    }
}
