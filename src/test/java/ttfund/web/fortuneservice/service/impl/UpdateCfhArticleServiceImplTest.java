package ttfund.web.fortuneservice.service.impl;

import com.ttfund.web.base.helper.DateHelper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.dao.CfhSqlserverDao;
import ttfund.web.fortuneservice.model.dto.CFHArticleDto;
import ttfund.web.fortuneservice.model.dto.CFHInfoDto;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.mockito.Mockito.*;

/**
 * UpdateCfhArticleServiceImpl 的测试用例  100% methods,100% lines covered
 */
@ExtendWith(MockitoExtension.class)
class UpdateCfhArticleServiceImplTest {

    @Mock
    private CfhMongodbDao cfhMongodb;

    @Mock
    private CfhSqlserverDao cfhSqlServer;

    @InjectMocks
    UpdateCfhArticleServiceImpl updateCfhArticleService;

    @Test
    void handCFHArticle() {
        // 模拟场景1 - 主流程
        Date breakPoint = DateHelper.stringToDate2("2022-01-01 00:00:00", DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
        when(cfhMongodb.getCFHGeneralData(anyString(), anyString())).thenReturn("[{\"time\":\"2022-01-01 00:00:00\"}]");
        when(cfhSqlServer.selectCFHInfoByUpdate(eq(breakPoint))).thenReturn(this.getCFHInfoDtoList());

        // 执行测试方法
        updateCfhArticleService.handCFHArticle();

        // 验证结果是否正确
        verify(cfhMongodb, times(1)).getCFHGeneralData(anyString(), anyString());
        verify(cfhSqlServer, times(1)).selectCFHInfoByUpdate(eq(breakPoint));
        verify(cfhSqlServer, times(0)).updateCFHArticle(anyString(), anyInt());
        verify(cfhMongodb, times(0)).updateCFHArticle(anyString(), anyInt());

        // 模拟场景2 - 断点数据异常
        when(cfhMongodb.getCFHGeneralData(anyString(), anyString())).thenReturn(null);
        when(cfhSqlServer.selectCFHInfoByUpdate(any(Date.class))).thenReturn(this.getCFHInfoDtoList());
        CFHArticleDto cfhArticleDto = new CFHArticleDto();
        cfhArticleDto.set_id("1111");
        cfhArticleDto.setAuthorId("2222");
        cfhArticleDto.setIsCfhArticle(false);
//        when(cfhMongodb.getCFHArticleByAuthor(anyString())).thenReturn(cfhArticleDto).thenReturn(null);
        when(cfhSqlServer.updateCFHArticle(anyString(),anyInt())).thenReturn(true);

        // 执行测试方法
        updateCfhArticleService.handCFHArticle();

    }

    private List<CFHInfoDto> getCFHInfoDtoList() {
        CFHInfoDto cfhInfo1 = new CFHInfoDto();
        cfhInfo1.setID("1");
        cfhInfo1.setCFHID("CFHID1");
        cfhInfo1.setCFHName("CFHName1");
        cfhInfo1.setCommpanyCode("CommpanyCode1");
        cfhInfo1.setCommpanyName("CommpanyName1");
        cfhInfo1.setSummary("Summary1");
        cfhInfo1.setHeaderImgPath("HeaderImgPath1");
        cfhInfo1.setStatus(1);
        cfhInfo1.setCreatTime(new Date());
        cfhInfo1.setUpDataTime(new Date());
        cfhInfo1.setSlogans("Slogans1");
        cfhInfo1.setPurview(1);
        cfhInfo1.setOperator("Operator1");
        cfhInfo1.setPushPurview(1);
        cfhInfo1.setPushCategoryCode("PushCategoryCode1");
        cfhInfo1.setRoleId("RoleId1");
        cfhInfo1.setJianPin("JianPin1");
        cfhInfo1.setOrganizationType("OrganizationType1");
        cfhInfo1.setRelatedUid("RelatedUid1");

        CFHInfoDto cfhInfo2 = new CFHInfoDto();
        cfhInfo2.setID("2");
        cfhInfo2.setCFHID("CFHID2");
        cfhInfo2.setCFHName("CFHName2");
        cfhInfo2.setCommpanyCode("CommpanyCode2");
        cfhInfo2.setCommpanyName("CommpanyName2");
        cfhInfo2.setSummary("Summary2");
        cfhInfo2.setHeaderImgPath("HeaderImgPath2");
        cfhInfo2.setStatus(2);
        cfhInfo2.setCreatTime(new Date());
        cfhInfo2.setUpDataTime(new Date());
        cfhInfo2.setSlogans("Slogans2");
        cfhInfo2.setPurview(2);
        cfhInfo2.setOperator("Operator2");
        cfhInfo2.setPushPurview(2);
        cfhInfo2.setPushCategoryCode("PushCategoryCode2");
        cfhInfo2.setRoleId("RoleId2");
        cfhInfo2.setJianPin("JianPin2");
        cfhInfo2.setOrganizationType("OrganizationType2");
        cfhInfo2.setRelatedUid("RelatedUid2");

        return Arrays.asList(cfhInfo1, cfhInfo2);
    }

}