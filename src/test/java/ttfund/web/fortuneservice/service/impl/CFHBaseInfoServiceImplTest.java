package ttfund.web.fortuneservice.service.impl;

import com.alibaba.fastjson.JSON;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
import ttfund.web.fortuneservice.config.CommonConfig;
import ttfund.web.fortuneservice.dao.CfhSqlserverDao;
import ttfund.web.fortuneservice.manager.YuYanApiManager;
import ttfund.web.fortuneservice.model.bo.CFHBaseInfoBo;
import ttfund.web.fortuneservice.model.dto.CFHBaseInfoDto;
import ttfund.web.fortuneservice.model.dto.CFHInfoDto;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * CFHBaseInfoServiceImpl 的测试用例  100% methods,100% lines covered
 */
@RunWith(MockitoJUnitRunner.class)
class CFHBaseInfoServiceImplTest {

    @Mock
    private CfhSqlserverDao sqlServer;

    @Mock
    private CommonConfig commonConfig;

    @Mock
    private YuYanApiManager yuYanApiManager;

    @InjectMocks
    private CFHBaseInfoServiceImpl cfhBaseInfoService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(commonConfig, "defaultHeaderImg", "defaultHeaderImg");
    }

    @Test
    void getCFHBaseInfo() {
        String message = null;
        CFHBaseInfoBo cfhBaseInfo = cfhBaseInfoService.getCFHBaseInfo(message);
        assertNull(cfhBaseInfo);

        message = JSON.toJSONString(this.getCFHBaseInfoBo());
        cfhBaseInfo = cfhBaseInfoService.getCFHBaseInfo(message);
        assertNotNull(cfhBaseInfo);
    }

    @Test
    void testHandCFHBaseInfo() {

        // 模拟场景一 正常情况
        when(sqlServer.selectByCFHId(anyString())).thenReturn(null);
        when(sqlServer.insert(any())).thenReturn(true);
        when(yuYanApiManager.generateNewPage(anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn(true);
        // 调用被测试方法
        int result = cfhBaseInfoService.handCFHBaseInfo(this.getCFHBaseInfoBo());
        // 验证结果
        assertEquals(1, result);
        verify(sqlServer, times(1)).selectByCFHId(anyString());
        verify(sqlServer, times(1)).insert(any());
        verify(yuYanApiManager, times(1)).generateNewPage(anyString(), anyString(), anyString(), anyString(), anyInt());

        // 模拟场景二 存在指定cfhInfoDB
        when((sqlServer.selectByCFHId(anyString()))).thenReturn(this.getCFHBaseInfoDto());
        when(sqlServer.updateById(any(CFHBaseInfoDto.class))).thenReturn(true);
        result = cfhBaseInfoService.handCFHBaseInfo(this.getCFHBaseInfoBo());
        // 验证结果
        assertEquals(1, result);

        // 模拟场景三 cfhBaseInfo异常
        result = cfhBaseInfoService.handCFHBaseInfo(null);
        // 验证结果
        assertEquals(0, result);

        // 模拟场景四 雨燕接口返回错误
        when((sqlServer.selectByCFHId(anyString()))).thenReturn(null);
        when(yuYanApiManager.generateNewPage(anyString(), anyString(), anyString(), anyString(), anyInt())).thenReturn(false);
        result = cfhBaseInfoService.handCFHBaseInfo(this.getCFHBaseInfoBo());
        // 验证结果
        assertEquals(1, result);

        // 模拟场景五 主流程抛出常见异常
        when(yuYanApiManager.generateNewPage(anyString(), anyString(), anyString(), anyString(), anyInt())).thenThrow(NullPointerException.class);
        result = cfhBaseInfoService.handCFHBaseInfo(this.getCFHBaseInfoBo());
        // 验证结果
        assertEquals(0, result);

    }

    CFHBaseInfoBo getCFHBaseInfoBo(){
        CFHBaseInfoBo cfhBaseInfo = new CFHBaseInfoBo();
        cfhBaseInfo.setAccountId("testId");
        cfhBaseInfo.setRelatedUid("testUid");
        cfhBaseInfo.setName("testName");
        cfhBaseInfo.setOrganizationType("testType");
        cfhBaseInfo.setPrivateFundParam("testPrivateFundParam");
        cfhBaseInfo.setFundCompanyParam("testFundCompanyParam");
        cfhBaseInfo.setAccountName("testAccountName");
        cfhBaseInfo.setPortrait("testPortrait");
        cfhBaseInfo.setSummary("testSummary");
        cfhBaseInfo.setBanner("testBanner");
        cfhBaseInfo.setCreateTime(new Date());
        cfhBaseInfo.setUpdateTime(new Date());
        cfhBaseInfo.setPageState(1);
        return cfhBaseInfo;
    }

    CFHBaseInfoDto getCFHBaseInfoDto(){
        CFHBaseInfoDto cfhBaseInfoDto = new CFHBaseInfoDto(
                "1", "testCFHID", "testCFHName", "testCommpanyCode", "testCommpanyName",
                "testSummary", "testHeaderImgPath", 10, new Date(), new Date(),
                "testSlogans", 1, "testOperator", 1, "testPushCategoryCode",
                "testRoleId", "testJianPin", "testOrganizationType", "testRelatedUid"
        );
        return cfhBaseInfoDto;
    }
}
