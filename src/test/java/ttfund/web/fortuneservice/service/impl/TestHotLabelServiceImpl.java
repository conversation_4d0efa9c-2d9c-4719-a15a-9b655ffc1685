package ttfund.web.fortuneservice.service.impl;


import com.alibaba.fastjson.JSON;
import com.mongodb.BasicDBObject;
import com.ttfund.web.base.helper.DateHelper;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import ttfund.web.fortuneservice.config.CommonConfig;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.model.dto.FundLabelDto;
import ttfund.web.fortuneservice.model.dto.FundThemeDto;
import ttfund.web.fortuneservice.model.dto.QuickReviewDto;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

public class TestHotLabelServiceImpl {
    Logger log = LoggerFactory.getLogger(TestHotLabelServiceImpl.class);

    private static final List<String> codeList = Arrays.asList("策略1", "策略2", "策略3");

    @Mock
    private CfhMongodbDao cfhMongodbDao;

    @Spy
    private CommonConfig appConfig;

    @InjectMocks
    private HotLabelServiceImpl service;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        appConfig.activityIdList = Arrays.asList("123ac","123456","123");
    }

    /**
     * 模拟无配置直接退出
     */
    @Test
    public void TestActivityLabelHandler_NO_Config() {
        //模拟无配置直接退出
        appConfig.activityIdList = new ArrayList<>();

        service.activityLabelHandler();

        //验证主题查询次数0
        verify(cfhMongodbDao, times(0)).getQuickReviewTheme();
        //验证快评查询次数0
        verify(cfhMongodbDao, times(0)).getQuickReviewByActivity(anyString());
        //存标签数据
        verify(cfhMongodbDao, times(0)).upsertCFHGeneralData(anyString(), any());
    }
    /**
     * 模拟标签列表查询为空
     */
    @Test
    public void TestActivityLabelHandler_NO_labelList() {
        //模拟标签列表
        when(cfhMongodbDao.getQuickReviewTheme()).thenReturn(Collections.emptyList());

        service.activityLabelHandler();

        //验证主题查询次数0
        verify(cfhMongodbDao, times(1)).getQuickReviewTheme();
        //验证快评查询次数0
        verify(cfhMongodbDao, times(0)).getQuickReviewByActivity(anyString());
        //存标签数据
        verify(cfhMongodbDao, times(0)).upsertCFHGeneralData(anyString(), any());
    }
    /**
     * 模拟标签列表查询为空
     */
    @Test
    public void TestActivityLabelHandler_NO_QuickReview() {
        //模拟标签列表
        when(cfhMongodbDao.getQuickReviewTheme()).thenReturn(getThem());

        //模拟获取不到活动相关的快评
        when(cfhMongodbDao.getQuickReviewByActivity(anyString())).thenReturn(Collections.emptyList());

        service.activityLabelHandler();

        //验证主题查询次数0
        verify(cfhMongodbDao, times(1)).getQuickReviewTheme();
        //验证快评查询次数0
        verify(cfhMongodbDao, times(3)).getQuickReviewByActivity(anyString());
        //存标签数据
        verify(cfhMongodbDao, times(0)).upsertCFHGeneralData(anyString(), any());
    }
    /**
     * 模拟标签列表查询为空
     */
    @Test
    public void TestActivityLabelHandler_success() {
        //模拟标签列表
        when(cfhMongodbDao.getQuickReviewTheme()).thenReturn(getThem());
        //模拟写库
        when(cfhMongodbDao.upsertCFHGeneralData(anyString(), any())).thenReturn(true);

        //模拟获取不到活动相关的快评
        when(cfhMongodbDao.getQuickReviewByActivity(anyString())).thenAnswer(new Answer<List<QuickReviewDto>>() {
            private int count = 0;
            @Override
            public List<QuickReviewDto> answer(InvocationOnMock invocation) throws Throwable {
                if (count == 0) {//"123ac"
                    count++;
                    return getQuickReviewByActivity1();
                } else if (count == 1) {//"123456"
                    count++;
                    return getQuickReviewByActivity1();
                } else {//"123"
                    return Collections.emptyList();
                }
            }

        });

        service.activityLabelHandler();

        //验证主题查询次数0
        verify(cfhMongodbDao, times(1)).getQuickReviewTheme();
        //验证快评查询次数0
        verify(cfhMongodbDao, times(3)).getQuickReviewByActivity(anyString());
        //存标签数据
        int numberOfInvocations = Mockito.mockingDetails(cfhMongodbDao).getInvocations()
                .stream()
                .filter(invocation -> "upsertCFHGeneralData".equals(invocation.getMethod().getName()))
                .collect(Collectors.toList())
                .size();

        log.info("upsertCFHGeneralData 被调用的次数: " + numberOfInvocations);
    }

    private List<FundLabelDto> getThem() {
        List<FundLabelDto> result = new ArrayList<>();
        String str = "[{\"_id\":\"861375\",\"DCCode\":\"BK0984\",\"DCName\":\"华为汽车\",\"IndexCode\":\"861375\",\"IndexName\":\"华为汽车\",\"ParentIndexCode\":\"\",\"Status\":0,\"ThemeName\":\"\",\"UpdateTime\":{\"$date\":\"2022-04-20T05:24:53.685Z\"}},{\"_id\":\"861305\",\"DCCode\":\"BK0906\",\"DCName\":\"流感\",\"IndexCode\":\"861305\",\"IndexName\":\"流感\",\"ParentIndexCode\":\"\",\"Status\":0,\"ThemeName\":\"\"},{\"_id\":\"861371\",\"DCCode\":\"BK0979\",\"DCName\":\"低碳冶金\",\"IndexCode\":\"861371\",\"IndexName\":\"低碳冶金\",\"ParentIndexCode\":\"\",\"Status\":0,\"ThemeName\":\"\"},{\"_id\":\"861423\",\"DCCode\":\"BK1060\",\"DCName\":\"新冠药物\",\"IndexCode\":\"861423\",\"IndexName\":\"新冠药物\",\"ParentIndexCode\":\"\",\"Status\":0,\"ThemeName\":\"\"},{\"_id\":\"861378\",\"DCCode\":\"BK0987\",\"DCName\":\"盐湖提锂\",\"IndexCode\":\"861378\",\"IndexName\":\"盐湖提锂\",\"ParentIndexCode\":\"\",\"Status\":0,\"ThemeName\":\"\"},{\"_id\":\"861429\",\"DCCode\":\"BK1066\",\"DCName\":\"民爆概念\",\"IndexCode\":\"861429\",\"IndexName\":\"民爆概念\",\"ParentIndexCode\":\"\",\"Status\":0,\"ThemeName\":\"\"},{\"_id\":\"861430\",\"DCCode\":\"BK1067\",\"DCName\":\"杭州亚运会\",\"IndexCode\":\"861430\",\"IndexName\":\"杭州亚运会\",\"ParentIndexCode\":\"\",\"Status\":0,\"ThemeName\":\"\"},{\"_id\":\"861432\",\"DCCode\":\"BK1069\",\"DCName\":\"智慧灯杆\",\"IndexCode\":\"861432\",\"IndexName\":\"智慧灯杆\",\"ParentIndexCode\":\"\",\"Status\":0,\"ThemeName\":\"\"},{\"_id\":\"861111\",\"DCCode\":\"BK0635\",\"DCName\":\"中超概念\",\"IndexCode\":\"861111\",\"IndexName\":\"中超概念\",\"ParentIndexCode\":\"\",\"Status\":0,\"ThemeName\":\"\"},{\"_id\":\"861401\",\"DCCode\":\"BK1011\",\"DCName\":\"环氧丙烷\",\"IndexCode\":\"861401\",\"IndexName\":\"环氧丙烷\",\"ParentIndexCode\":\"\",\"Status\":0,\"ThemeName\":\"\"}]";
        result = JSON.parseArray(str, FundLabelDto.class);
        return result;
    }

    private List<QuickReviewDto> getQuickReviewByActivity1() {
        //123ac
        List<QuickReviewDto> result = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            int range = getRandomIntegerInRange(0, 90);
            QuickReviewDto quickReviewDto = new QuickReviewDto();
            quickReviewDto.setLabelType(range >= 45 ? 0 : 1);
            quickReviewDto.setID("id" + i);
            quickReviewDto.setEmotionType(range >= 0 && range < 30 ? 0 : range >= 30 && range < 60 ? 1 : 2);
            List<FundThemeDto> themeList = new ArrayList<>();
            List<FundLabelDto> them = getThem();
            if (range < 10) {
                quickReviewDto.setLabelType(0);
                for (int j = 0; j < 3; j++) {
                    FundThemeDto themeDto = new FundThemeDto();
                    themeDto.setThemeName("自定义名字" + i);
                    themeDto.setThemeId("自定义不存在的名字" + i);
                    themeList.add(themeDto);
                }
            }else {
                for (int j = 0; j < 3; j++) {
                    int index = getRandomIntegerInRange(1, them.size());
                    FundLabelDto labelDto = them.get(index-1);
                    FundThemeDto themeDto = new FundThemeDto();
                    themeDto.setThemeName(labelDto.getIndexName());
                    themeDto.setThemeId(labelDto.getIndexCode());
                    themeList.add(themeDto);
                }
            }
//            if (range >= 50) {
//                quickReviewDto.setThemeLabel(themeList);
//            }
            quickReviewDto.setThemeLabel(themeList);
            result.add(quickReviewDto);
        }
        return result;
    }
    public int getRandomIntegerInRange(int min, int max) {
        Random random = new Random();
        return random.nextInt(max - min + 1) + min;
    }
}

