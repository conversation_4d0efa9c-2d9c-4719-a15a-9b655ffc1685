package ttfund.web.fortuneservice.service.impl;

import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.jupiter.api.Test;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import ttfund.web.fortuneservice.FortuneServiceApplication;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.manager.CFHApiManager;
import ttfund.web.fortuneservice.model.bo.AuthorDetailBo;
import ttfund.web.fortuneservice.service.UpdateCFHFInfoService;

import java.util.*;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;


/**
 * <AUTHOR>
 * @description UpdateCFHFInfoServiceImplTest。java
 * @date 2023/8/8 10:49
 */
@RunWith(MockitoJUnitRunner.class)
@SpringBootTest(classes = FortuneServiceApplication.class)
@ExtendWith(MockitoExtension.class)
class UpdateCFHFInfoServiceImplTest {

    @InjectMocks
    private UpdateCFHFInfoServiceImpl service;

    @Mock
    CFHApiManager cfhApiManager;

    @Mock
    private CfhMongodbDao cfhMongodbDao;


    @Test
    public void testUpdateCFHFInfo() {
        // 准备测试数据
        List<String> allCFHIds = new ArrayList<>();
        allCFHIds.add("testId1");
        allCFHIds.add("testId2");
        when(cfhMongodbDao.getAllCFHIds()).thenReturn(allCFHIds);

        AuthorDetailBo cfhDetail = new AuthorDetailBo();
        cfhDetail.setFandsCount(100);
        cfhDetail.setPVCount(200);
        cfhDetail.setArticleCount(50);
        cfhDetail.setBigVip(1);
        cfhDetail.setIsFund(false);
        when(cfhApiManager.getAuthorDetail(anyString())).thenReturn(cfhDetail);

        // 调用测试方法
        boolean result = service.updateCFHFInfo("testParam");

        // 验证结果
        assertTrue(result);

        // 验证方法的调用次数
        verify(cfhMongodbDao, times(1)).getAllCFHIds();
        verify(cfhApiManager, times(2)).getAuthorDetail(anyString()); // 由于有两个id，所以调用两次

        // 验证方法的参数
        verify(cfhMongodbDao, times(1)).updateCFHInfos(anyList());

        // 异常场景
        when(cfhApiManager.getAuthorDetail(anyString())).thenThrow(NullPointerException.class);
        result = service.updateCFHFInfo("testParam");
    }
}