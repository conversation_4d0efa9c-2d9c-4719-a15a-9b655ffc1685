//package ttfund.web.fortuneservice.service.impl;
//
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//import org.mockito.junit.jupiter.MockitoExtension;
//import ttfund.web.fortuneservice.dao.CfhMongodbDao;
//import ttfund.web.fortuneservice.dao.CfhSqlserverDao;
//import ttfund.web.fortuneservice.model.dto.ResearchTaskConfigDto;
//import ttfund.web.fortuneservice.model.dto.ResearchTaskGroupDto;
//import ttfund.web.fortuneservice.model.dto.ResearchTaskSubjectDto;
//
//import static org.mockito.ArgumentMatchers.anyMap;
//import static org.mockito.Mockito.*;
//
//import java.util.*;
//
///**
// * 自动为机构下发任务及生成题目的实现 的测试用例  100% methods,97% lines covered
// *
// * <AUTHOR>
// * @date 2023/5/11 9:35
// */
//@ExtendWith(MockitoExtension.class)
//class TaskAutoGenerateServiceImplTest {
//
//    @InjectMocks
//    private TaskAutoGenerateServiceImpl taskAutoGenerateService;
//
//    @Mock
//    private CfhSqlserverDao sqlserver;
//
//    @Mock
//    private CfhMongodbDao cfhMongodb;
//
//    @BeforeEach
//    void setUp() {
//        MockitoAnnotations.openMocks(this);
//    }
//
//    @Test
//    void testAutoGenerateService() {
//        // 准备测试数据
//        Map<ResearchTaskConfigDto, ResearchTaskGroupDto> onceTasks = this.getOnceTasks();
//        Map<ResearchTaskConfigDto, ResearchTaskGroupDto> periodTasks = this.getPeriodTasks();
//        List<ResearchTaskSubjectDto> researchTaskSubjectDto = this.getResearchTaskSubjectDto();
//
//        // 设置模拟行为
//        when(sqlserver.selectByTaskCycles(anyBoolean(), any(), any())).thenReturn(onceTasks);
//        when(sqlserver.selectByTaskCycles(anyBoolean(), isNull(), any())).thenReturn(periodTasks);
//        when(sqlserver.selectConfigSubject(any())).thenReturn(researchTaskSubjectDto);
//        when(sqlserver.insertResearchTaskList(anyList())).thenReturn(true);
//        when(sqlserver.insertResearchSubject(anyList())).thenReturn(true);
//        when(cfhMongodb.getCFHGeneralData(anyString(), anyString())).thenReturn("2021-01-01 00:00:00");
//        when(cfhMongodb.upsertCFHGeneralData(anyString(), anyList())).thenReturn(true);
//        when(sqlserver.updatePeriodTaskReleaseTime(anyMap())).thenReturn(true);
//
//        // 调用待测方法
//        boolean result = taskAutoGenerateService.autoGenerateService();
//
//        // 验证结果
//        assert (result);
//
//        // 验证方法调用
//        verify(sqlserver, times(1)).selectByTaskCycles(eq(true), any(), any());
//        verify(sqlserver, times(1)).selectByTaskCycles(eq(false), isNull(), any());
//        verify(sqlserver, times(1)).insertResearchTaskList(anyList());
//        verify(sqlserver, times(1)).insertResearchSubject(anyList());
//        verify(cfhMongodb, times(1)).getCFHGeneralData(anyString(), anyString());
//        verify(cfhMongodb, times(1)).upsertCFHGeneralData(anyString(), anyList());
//        verify(sqlserver, times(1)).updatePeriodTaskReleaseTime(anyMap());
//
//        // 模拟异常情况
//        when(sqlserver.updatePeriodTaskReleaseTime(anyMap())).thenReturn(false);
//        result = taskAutoGenerateService.autoGenerateService();
//        // 验证结果
//        assert (!result);
//
//        when(cfhMongodb.upsertCFHGeneralData(anyString(), anyList())).thenReturn(false);
//        result = taskAutoGenerateService.autoGenerateService();
//        // 验证结果
//        assert (!result);
//
//        when(sqlserver.insertResearchSubject(anyList())).thenReturn(false);
//        result = taskAutoGenerateService.autoGenerateService();
//        // 验证结果
//        assert (!result);
//
//
//        when(sqlserver.insertResearchTaskList(anyList())).thenReturn(false);
//        result = taskAutoGenerateService.autoGenerateService();
//        // 验证结果
//        assert (!result);
//
//        when(sqlserver.selectByTaskCycles(anyBoolean(), any(), any())).thenThrow(NullPointerException.class);
//        result = taskAutoGenerateService.autoGenerateService();
//        // 验证结果
//        assert (!result);
//
//
//
//    }
//
//    private Map<ResearchTaskConfigDto, ResearchTaskGroupDto> getOnceTasks() {
//        ResearchTaskConfigDto configDto0 = new ResearchTaskConfigDto("0", "Sample Research Task", "This is a sample research task content.",
//                "https://www.example.com/taskfile.pdf", 2, 0, "TG1", new Date(), 30, 0, new Date(), new Date());
//        ResearchTaskConfigDto configDto1 = new ResearchTaskConfigDto("0", "Sample Research Task", "This is a sample research task content.",
//                "https://www.example.com/taskfile.pdf", 1, 0, "TG1", new Date(), 30, 0, new Date(), new Date());
//        ResearchTaskGroupDto groupDto = new ResearchTaskGroupDto("TG1", "Sample Research Task Group",
//                "1001,1002,1003", 0, new Date(), new Date());
//        Map<ResearchTaskConfigDto, ResearchTaskGroupDto> onceTasks = new HashMap<>();
//        onceTasks.put(configDto0, groupDto);
//        return onceTasks;
//    }
//
//    private Map<ResearchTaskConfigDto, ResearchTaskGroupDto> getPeriodTasks() {
//        ResearchTaskConfigDto configDto1 = new ResearchTaskConfigDto("1", "Sample Research Task", "This is a sample research task content.",
//                "https://www.example.com/taskfile.pdf", 1, 1, "TG1", new Date(), 30, 0, new Date(), new Date());
//        ResearchTaskConfigDto configDto2 = new ResearchTaskConfigDto("2", "Sample Research Task", "This is a sample research task content.",
//                "https://www.example.com/taskfile.pdf", 2, 2, "TG1", new Date(), 30, 0, new Date(), new Date());
//        ResearchTaskConfigDto configDto3 = new ResearchTaskConfigDto("3", "Sample Research Task", "This is a sample research task content.",
//                "https://www.example.com/taskfile.pdf", 3, 3, "TG1", new Date(), 30, 0, new Date(), new Date());
//        ResearchTaskGroupDto groupDto = new ResearchTaskGroupDto("TG1", "Sample Research Task Group",
//                "1001,1002,1003", 0, new Date(), new Date());
//        Map<ResearchTaskConfigDto, ResearchTaskGroupDto> periodTasks = new HashMap<>();
//        periodTasks.put(configDto1, groupDto);
//        periodTasks.put(configDto2, groupDto);
//        periodTasks.put(configDto3, groupDto);
//        return periodTasks;
//    }
//
//    private List<ResearchTaskSubjectDto> getResearchTaskSubjectDto() {
//        List<ResearchTaskSubjectDto> list = new ArrayList<>();
//        ResearchTaskSubjectDto researchTaskSubjectDto = new ResearchTaskSubjectDto("1", "1", "Cycle1", "Sample Subject Title", "SubjectCode1", 1, "0", 0, new Date(), "John Doe", new Date());
//        list.add(researchTaskSubjectDto);
//        return list;
//    }
//
//}