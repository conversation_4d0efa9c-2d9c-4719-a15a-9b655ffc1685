package ttfund.web.fortuneservice.service.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import ttfund.web.fortuneservice.config.CommonConfig;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.dao.CfhSqlserverDao;
import ttfund.web.fortuneservice.model.dto.CFHInfoDto;
import ttfund.web.fortuneservice.model.dto.CFHLabelDto;

import java.util.ArrayList;
import java.util.List;

/**
 * CFHDataToMongoServiceImpl 的测试用例  100% methods,100% lines covered
 */
@ExtendWith(MockitoExtension.class)
class CFHDataToMongoServiceImplTest {

    @InjectMocks
    private CFHDataToMongoServiceImpl cfhDataToMongoService;

    @Mock
    private CfhSqlserverDao sqlServer;

    @Mock
    private CommonConfig commonConfig;

    @Mock
    private CfhMongodbDao cfhMongodbDao;

    private List<CFHInfoDto> cfhInfoList;
    private List<CFHLabelDto> cfhLabelList;

    @BeforeEach
    void setUp() {
        cfhInfoList = new ArrayList<>();
        CFHInfoDto cfhInfo = new CFHInfoDto();
        cfhInfo.setCFHID("1");
        cfhInfo.setCommpanyCode("001");
        cfhInfo.setHeaderImgPath("http://example.com/img.jpg");
        cfhInfoList.add(cfhInfo);

        cfhLabelList = new ArrayList<>();
        CFHLabelDto cfhLabel = new CFHLabelDto();
        cfhLabel.setCFHID("1");
        cfhLabel.setTitle("title");
        cfhLabel.setUrlType(1);
        cfhLabel.setLinkUrl("http://example.com/link");
        cfhLabelList.add(cfhLabel);

        ReflectionTestUtils.setField(commonConfig, "defaultHeaderImg", "defaultHeaderImg");
    }

    @Test
    void testHandCFHData() {
        Mockito.when(sqlServer.selectAllCFHInfo()).thenReturn(cfhInfoList);
        Mockito.when(sqlServer.selectAllLabel()).thenReturn(cfhLabelList);
        Mockito.when(cfhMongodbDao.saveOrUpdateCFHList(Mockito.anyList())).thenReturn(1);

        cfhDataToMongoService.handCFHData();

        Mockito.verify(sqlServer, Mockito.times(1)).selectAllCFHInfo();
        Mockito.verify(sqlServer, Mockito.times(1)).selectAllLabel();
        Mockito.verify(cfhMongodbDao, Mockito.times(1)).saveOrUpdateCFHList(Mockito.anyList());
    }
}
