//package ttfund.web.fortuneservice.service.impl;
//
//import com.ttfund.web.base.helper.DateHelper;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.runner.RunWith;
//import org.mockito.junit.MockitoJUnitRunner;
//import org.springframework.boot.test.context.SpringBootTest;
//import ttfund.web.fortuneservice.FortuneServiceApplication;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.test.util.ReflectionTestUtils;
//import ttfund.web.fortuneservice.config.CommonConfig;
//import ttfund.web.fortuneservice.dao.CfhMongodbDao;
//import ttfund.web.fortuneservice.dao.VerticaDao;
//import ttfund.web.fortuneservice.manager.DongDongManager;
//import ttfund.web.fortuneservice.model.dto.DaysInfoMongoDto;
//import ttfund.web.fortuneservice.model.dto.FundHighLevelInfo;
//import ttfund.web.fortuneservice.model.dto.FundNetWorthDto;
//import ttfund.web.fortuneservice.model.dto.FundSizeDto;
//
//import java.math.BigDecimal;
//import java.util.*;
//
//import static org.junit.Assert.assertFalse;
//import static org.junit.jupiter.api.Assertions.assertTrue;
//import static org.mockito.Mockito.*;
//
//
///**
// * FundNetWorthInspectionServiceImpl.java
// * 高端产品未按期披露净值检测的实现 的测试用例  100% methods,99% lines covered
// *
// * <AUTHOR>
// * @date 2023/5/11 16:51
// */
//@RunWith(MockitoJUnitRunner.class)
//@SpringBootTest(classes = FortuneServiceApplication.class)
//@ExtendWith(MockitoExtension.class)
//class FundNetWorthInspectionServiceImplTest {
//
//    @InjectMocks
//    private FundNetWorthInspectionServiceImpl fundNetWorthInspectionService;
//
//    @Mock
//    private VerticaDao verticaDao;
//
//    @Mock
//    private CfhMongodbDao cfhMongodbDao;
//
//    @Mock
//    private DongDongManager dongDongManager;
//
//    @BeforeEach
//    public void setUp() {
//        CommonConfig config = new CommonConfig();
//        config.inspectionReportReceiving = Collections.singletonList("220860");
//        ReflectionTestUtils.setField(fundNetWorthInspectionService, "config", config);
//    }
//
//    @Test
//    void testInspectionService() {
//        // 准备测试数据
//        List<FundHighLevelInfo> fundCodeTypes = this.getFundHighLevelInfoList();
//        List<FundSizeDto> fundSizeInfo = this.getFundSizeDtoList();
//        List<FundNetWorthDto> fundNetWorthInfo = this.getFundNetWorthDto();
//
//        // 设置模拟行为  -  1.每月结束后6个工作日（T）
//        List<DaysInfoMongoDto> daysInfoMongoDto6 = this.getDaysInfoMongoDto6();
//        when(verticaDao.getFundCodeType()).thenReturn(fundCodeTypes);
//        when(cfhMongodbDao.getDaysInfoByTimeRange(any(Date.class), any(Date.class))).thenReturn(daysInfoMongoDto6);
//        when(verticaDao.getFundSizeInfo(anyList())).thenReturn(fundSizeInfo);
//        when(verticaDao.getFundNetWorthInfo(anyList())).thenReturn(fundNetWorthInfo);
//        when(cfhMongodbDao.getLastTradeDay(any(Date.class))).thenReturn("9020/9/9 0:00:00");
//        when(cfhMongodbDao.batchSaveRegularReportList(anyList())).thenReturn(1);
//        when(cfhMongodbDao.batchSaveRegularReportContentList(anyList())).thenReturn(2);
//        when(dongDongManager.sendDongDongMessage(anyList(),anyString(),anyString())).thenReturn(2);
//        // 调用测试方法
//        boolean result = fundNetWorthInspectionService.inspectionService(null);
//        // 验证结果和模拟行为
//        assertTrue(result);
//
//        // 设置模拟行为  - 2.每季度结束后11个工作日（T）
//        List<DaysInfoMongoDto> daysInfoMongoDto11 = this.getDaysInfoMongoDto11();
//        when(cfhMongodbDao.getDaysInfoByTimeRange(any(Date.class), any(Date.class))).thenReturn(daysInfoMongoDto11);
//        when(cfhMongodbDao.batchSaveRegularReportContentList(anyList())).thenReturn(3);
//        // 调用测试方法
//        result = fundNetWorthInspectionService.inspectionService("2022-04-11 12:00:00");
//        // 验证结果和模拟行为
//        assertTrue(result);
//
//        // 设置模拟行为  -  3.非交易日
//        when(cfhMongodbDao.getDaysInfoByTimeRange(any(Date.class), any(Date.class))).thenReturn(daysInfoMongoDto6);
//        // 调用测试方法
//        result = fundNetWorthInspectionService.inspectionService("2022-06-07 12:00:00");
//        // 验证结果和模拟行为
//        assertTrue(result);
//
//        // 设置模拟行为  -  4.其他交易日
//        when(cfhMongodbDao.getDaysInfoByTimeRange(any(Date.class), any(Date.class))).thenReturn(daysInfoMongoDto11);
//        // 调用测试方法
//        result = fundNetWorthInspectionService.inspectionService("2022-04-01 12:00:00");
//        // 验证结果和模拟行为
//        assertTrue(result);
//
//        // 设置模拟行为  -  5.异常情况
//        when(cfhMongodbDao.getDaysInfoByTimeRange(any(Date.class), any(Date.class))).thenReturn(daysInfoMongoDto11);
//        when(cfhMongodbDao.batchSaveRegularReportContentList(anyList())).thenReturn(4);
//        result = fundNetWorthInspectionService.inspectionService("2022-04-11 12:00:00");
//        // 验证结果和模拟行为
//        assertFalse(result);
//
//        // 设置模拟行为  -  5.异常情况
//        when(verticaDao.getFundCodeType()).thenThrow(NullPointerException.class);
//        result = fundNetWorthInspectionService.inspectionService("2022-04-11 12:00:00");
//        // 验证结果和模拟行为
//        assertFalse(result);
//    }
//
//    private List<FundSizeDto> getFundSizeDtoList() {
//        List<FundSizeDto> fundSizeDtoList = new ArrayList<>();
//        fundSizeDtoList.add(new FundSizeDto("F123", "Fund A", new BigDecimal("50000001.00")));
//        fundSizeDtoList.add(new FundSizeDto("F456", "Fund B", new BigDecimal("2000000.00")));
//        return fundSizeDtoList;
//    }
//
//    private List<FundHighLevelInfo> getFundHighLevelInfoList() {
//        List<FundHighLevelInfo> list = new ArrayList<>();
//        list.add(new FundHighLevelInfo("F123", 1, "Fund A"));
//        list.add(new FundHighLevelInfo("F789", 1, "Fund A"));
//        list.add(new FundHighLevelInfo("F456", 1, "Fund A"));
//        list.add(new FundHighLevelInfo("F777", 0, "Fund A"));
//        return list;
//    }
//
//    private List<FundNetWorthDto> getFundNetWorthDto() {
//        List<FundNetWorthDto> list = new ArrayList<>();
//        list.add(new FundNetWorthDto("F123", DateHelper.getNowDate()));
//        list.add(new FundNetWorthDto("F789", DateHelper.getNowDate()));
//        return list;
//    }
//
//    private List<DaysInfoMongoDto> getDaysInfoMongoDto6(){
//        List<DaysInfoMongoDto> list = new ArrayList<>();
//        list.add(new DaysInfoMongoDto(DateHelper.stringToDate2(DateHelper.dateToStr(new Date(),DateHelper.FORMAT_YYYY_MM_DD), DateHelper.FORMAT_YYYY_MM_DD),"1"));
//        list.add(new DaysInfoMongoDto(DateHelper.stringToDate2("20220602",DateHelper.FORMAT_YYYYMMDD),"1"));
//        list.add(new DaysInfoMongoDto(DateHelper.stringToDate2("20220603",DateHelper.FORMAT_YYYYMMDD),"1"));
//        list.add(new DaysInfoMongoDto(DateHelper.stringToDate2("20220604",DateHelper.FORMAT_YYYYMMDD),"1"));
//        list.add(new DaysInfoMongoDto(DateHelper.stringToDate2("20220605",DateHelper.FORMAT_YYYYMMDD),"1"));
//        list.add(new DaysInfoMongoDto(DateHelper.stringToDate2("20220606",DateHelper.FORMAT_YYYYMMDD),"1"));
//        list.add(new DaysInfoMongoDto(DateHelper.stringToDate2("20220607",DateHelper.FORMAT_YYYYMMDD),"0"));
//        return list;
//    }
//
//    private List<DaysInfoMongoDto> getDaysInfoMongoDto11(){
//        List<DaysInfoMongoDto> list = new ArrayList<>();
//        list.add(new DaysInfoMongoDto(DateHelper.stringToDate2("20220401",DateHelper.FORMAT_YYYYMMDD),"1"));
//        list.add(new DaysInfoMongoDto(DateHelper.stringToDate2("20220402",DateHelper.FORMAT_YYYYMMDD),"1"));
//        list.add(new DaysInfoMongoDto(DateHelper.stringToDate2("20220403",DateHelper.FORMAT_YYYYMMDD),"1"));
//        list.add(new DaysInfoMongoDto(DateHelper.stringToDate2("20220404",DateHelper.FORMAT_YYYYMMDD),"1"));
//        list.add(new DaysInfoMongoDto(DateHelper.stringToDate2("20220405",DateHelper.FORMAT_YYYYMMDD),"1"));
//        list.add(new DaysInfoMongoDto(DateHelper.stringToDate2("20220406",DateHelper.FORMAT_YYYYMMDD),"1"));
//        list.add(new DaysInfoMongoDto(DateHelper.stringToDate2("20220407",DateHelper.FORMAT_YYYYMMDD),"1"));
//        list.add(new DaysInfoMongoDto(DateHelper.stringToDate2("20220408",DateHelper.FORMAT_YYYYMMDD),"1"));
//        list.add(new DaysInfoMongoDto(DateHelper.stringToDate2("20220409",DateHelper.FORMAT_YYYYMMDD),"1"));
//        list.add(new DaysInfoMongoDto(DateHelper.stringToDate2("202204010",DateHelper.FORMAT_YYYYMMDD),"1"));
//        list.add(new DaysInfoMongoDto(DateHelper.stringToDate2("202204011",DateHelper.FORMAT_YYYYMMDD),"1"));
//        return list;
//    }
//}
