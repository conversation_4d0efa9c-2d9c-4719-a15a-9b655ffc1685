package ttfund.web.fortuneservice.service.impl;

import org.junit.jupiter.api.Test;


import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;
import ttfund.web.fortuneservice.config.CommonConfig;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.manager.HqApiManager;
import ttfund.web.fortuneservice.model.dto.CfhListDto;
import ttfund.web.fortuneservice.model.dto.QuickReviewDto;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.Mockito.*;

/**
 * FundManagerServiceImpl 的测试用例  100% methods,91% lines covered
 */
class FundManagerServiceImplTest {

    @InjectMocks
    private FundManagerServiceImpl fundManagerService;

    @Mock
    private CommonConfig config;

    @Mock
    private HqApiManager hqApiManager;

    @Mock
    private CfhMongodbDao cfhMongodbDao;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(config, "managerDays", 10);
        ReflectionTestUtils.setField(config, "gteNum", 1);
    }

    @Test
    void testFundManagerHandler() {
        // 模拟方法返回值
        when(cfhMongodbDao.getCFHWeight(anyList())).thenReturn(this.getCfhListDtoList());
        when(cfhMongodbDao.getQuickReview(anyLong(), anyLong(), anyInt(), any())).thenReturn(this.getQuickReviewDtoList());
        when(cfhMongodbDao.upsertCFHGeneralData(anyString(), anyList())).thenReturn(true);

        // 调用方法
        fundManagerService.fundManagerHandler();

        // 验证方法调用
        verify(cfhMongodbDao, times(1)).getQuickReview(anyLong(), anyLong(), anyInt(), any());
        verify(cfhMongodbDao, times(1)).upsertCFHGeneralData(anyString(), anyList());

        // 保存失败
        when(cfhMongodbDao.upsertCFHGeneralData(anyString(),any())).thenReturn(false);
        fundManagerService.fundManagerHandler();

    }

    private List<QuickReviewDto> getQuickReviewDtoList(){
        List<QuickReviewDto> quickReviewDtoList = new ArrayList<>();
        QuickReviewDto quickReviewDto = new QuickReviewDto();
        quickReviewDto.setID("1");
        quickReviewDto.setWeight(10);
        quickReviewDto.setCFHID("1001");
        quickReviewDto.setMGRID("456");
        quickReviewDto.setMGRName("John Doe");
        quickReviewDto.setTitle("Quick Review Title");
        quickReviewDto.setReviewContent("Quick review content");
        quickReviewDto.setEmotionType(0);
        quickReviewDto.setProductType(1);
        quickReviewDto.setProductCode("ABC123");
        quickReviewDto.setProductTags("tag1,tag2");
        quickReviewDto.setProductType2(6);
        quickReviewDto.setProductCode2("DEF456");
        quickReviewDto.setProductTags2("tag3,tag4");
        quickReviewDto.setUpdateTime(new Date());
        quickReviewDto.setUpdateDay(20220101);
        quickReviewDto.setTimepoint(1640995200L);
        quickReviewDto.setProductSyImg("http://example.com/image1.jpg");
        quickReviewDto.setProductSyImg2("http://example.com/image2.jpg");
        quickReviewDto.setThemeLabel(new ArrayList<>());
        quickReviewDto.setLabelType(0);
        quickReviewDtoList.add(quickReviewDto);

        QuickReviewDto quickReviewDto1 = new QuickReviewDto();
        quickReviewDto1.setID("2");
        quickReviewDto1.setWeight(10);
        quickReviewDto1.setCFHID("1002");
        quickReviewDto1.setMGRID("456");
        quickReviewDto1.setMGRName("John Doe");
        quickReviewDto1.setTitle("Quick Review Title");
        quickReviewDto1.setReviewContent("Quick review content");
        quickReviewDto1.setEmotionType(0);
        quickReviewDto1.setProductType(1);
        quickReviewDto1.setProductCode("ABC123");
        quickReviewDto1.setProductTags("tag1,tag2");
        quickReviewDto1.setProductType2(6);
        quickReviewDto1.setProductCode2("DEF456");
        quickReviewDto1.setProductTags2("tag3,tag4");
        quickReviewDto1.setUpdateTime(new Date());
        quickReviewDto1.setUpdateDay(20220101);
        quickReviewDto1.setTimepoint(1640995200L);
        quickReviewDto1.setProductSyImg("http://example.com/image1.jpg");
        quickReviewDto1.setProductSyImg2("http://example.com/image2.jpg");
        quickReviewDto1.setThemeLabel(new ArrayList<>());
        quickReviewDto1.setLabelType(0);
        quickReviewDtoList.add(quickReviewDto1);


        return quickReviewDtoList;
    }

    private List<CfhListDto> getCfhListDtoList(){
        List<CfhListDto> cfhList = new ArrayList<>();

        CfhListDto cfh1 = new CfhListDto();
        cfh1.setID("1");
        cfh1.setCFHID("1001");
        cfh1.setCFHName("Test1");
        cfh1.setStatus(1);
        cfh1.setDefaultWeight(10);
        cfh1.setWeightStartTimeStamp(1626109050000L);
        cfh1.setWeightEndTimeStamp(1626109150000L);
        cfhList.add(cfh1);

        CfhListDto cfh2 = new CfhListDto();
        cfh2.setID("2");
        cfh2.setCFHID("1002");
        cfh2.setCFHName("Test2");
        cfh2.setStatus(10);
        cfh2.setDefaultWeight(20);
        cfh2.setWeightStartTimeStamp(1626109150000L);
        cfh2.setWeightEndTimeStamp(1626109250000L);
        cfhList.add(cfh2);

        return cfhList;

    }

}


