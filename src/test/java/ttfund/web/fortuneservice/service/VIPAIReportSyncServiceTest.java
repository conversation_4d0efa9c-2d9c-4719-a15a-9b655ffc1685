package ttfund.web.fortuneservice.service;

import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.fortuneservice.utils.TimeUtil;

import java.util.Date;

/**
 * VIP AI报告同步服务测试类
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class VIPAIReportSyncServiceTest {

    @Autowired
    private VIPAIReportSyncService vipAIReportSyncService;

    @Test
    public void testSyncVIPAIReportToMongoDB() {
        try {
            // 使用7天前的时间作为断点进行测试
//            long sevenDaysAgo = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000L);
//            Date breakpointTime = new Date(sevenDaysAgo);

            Date breakpointTime = vipAIReportSyncService.getLatestUpdateTime();

            XxlJobLogger.log("使用断点时间：{}", TimeUtil.dateToStr(breakpointTime, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS));
            
            log.info("开始测试同步，断点时间：{}", TimeUtil.dateToStr(breakpointTime, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS));
            
            boolean result = vipAIReportSyncService.syncVIPAIReportToMongoDB(breakpointTime);
            
            log.info("同步测试完成，结果：{}", result);
            
        } catch (Exception e) {
            log.error("同步测试失败", e);
        }
    }

    @Test
    public void testGetLatestUpdateTime() {
        try {
            Date latestTime = vipAIReportSyncService.getLatestUpdateTime();
            log.info("最新更新时间：{}", latestTime != null ?
                TimeUtil.dateToStr(latestTime, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS) : "null");
        } catch (Exception e) {
            log.error("获取最新更新时间测试失败", e);
        }
    }

    @Test
    public void testSyncSingleReport() {
        try {
            // 测试同步单个报告，使用更近的时间
            long oneDayAgo = System.currentTimeMillis() - (1 * 24 * 60 * 60 * 1000L);
            Date breakpointTime = new Date(oneDayAgo);

            log.info("开始测试单个报告同步，断点时间：{}", TimeUtil.dateToStr(breakpointTime, TimeUtil.FORMAT_YYYY_MM_DD_HH_MM_SS));

            boolean result = vipAIReportSyncService.syncVIPAIReportToMongoDB(breakpointTime);

            log.info("单个报告同步测试完成，结果：{}", result);

        } catch (Exception e) {
            log.error("单个报告同步测试失败", e);
        }
    }
}
