package ttfund.web.fortuneservice.service;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.fortuneservice.FortuneServiceApplication;
import ttfund.web.fortuneservice.model.dto.MultiMonthlyReportRequestDTO;
import ttfund.web.fortuneservice.model.dto.MultiMonthlyReportResponseDTO;

import java.util.*;

/**
 * VIP多机构月度报告总结服务测试类
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = FortuneServiceApplication.class)
@Slf4j
public class VIPMultiMonthlyReportSummaryServiceTest {

    @Autowired
    private VIPMultiMonthlyReportSummaryService vipMultiMonthlyReportSummaryService;

    @Test
    public void testGenerateMultiMonthlyReportSummary() {
        try {
            log.info("开始测试VIP多机构月度报告总结服务");
            
            // 执行测试
            boolean result = vipMultiMonthlyReportSummaryService.generateMultiMonthlyReportSummary("test");
            
            log.info("测试结果：{}", result);
            
            // 验证结果
            assert result : "多机构月度报告总结生成应该成功";
            
            log.info("VIP多机构月度报告总结服务测试完成");
            
        } catch (Exception e) {
            log.error("测试VIP多机构月度报告总结服务异常", e);
            throw e;
        }
    }

    @Test
    public void testBuildMultiMockRequest() {
        try {
            log.info("开始测试构建多机构模拟请求数据");
            
            // 构建模拟请求数据
            MultiMonthlyReportRequestDTO request = buildMultiMockRequest();
            
            log.info("多机构模拟请求数据：{}", JSON.toJSONString(request, true));
            
            // 验证请求数据
            assert request != null : "请求数据不能为空";
            assert request.getInstitutions() != null && !request.getInstitutions().isEmpty() : "机构列表不能为空";
            assert request.getDeadline() != null : "截止时间不能为空";
            
            for (MultiMonthlyReportRequestDTO.Institution institution : request.getInstitutions()) {
                assert institution.getCfhId() != null : "财富号ID不能为空";
                assert institution.getInstitutionName() != null : "机构名称不能为空";
                assert institution.getTaskMonth() != null : "任务月份不能为空";
            }
            
            log.info("多机构模拟请求数据构建测试完成");
            
        } catch (Exception e) {
            log.error("测试构建多机构模拟请求数据异常", e);
            throw e;
        }
    }

    /**
     * 构建多机构模拟请求数据
     */
    private MultiMonthlyReportRequestDTO buildMultiMockRequest() {
        MultiMonthlyReportRequestDTO request = new MultiMonthlyReportRequestDTO();
        request.setDeadline("2024-12-31");
        
        List<MultiMonthlyReportRequestDTO.Institution> institutions = new ArrayList<>();
        
        // 机构1
        MultiMonthlyReportRequestDTO.Institution institution1 = new MultiMonthlyReportRequestDTO.Institution();
        institution1.setCfhId("CFH001");
        institution1.setInstitutionName("东方财富资产管理");
        institution1.setTaskMonth("2024-12");
        
        // 大类与行业
        List<MultiMonthlyReportRequestDTO.CategoryAndIndustry> categories1 = new ArrayList<>();
        MultiMonthlyReportRequestDTO.CategoryAndIndustry category1 = new MultiMonthlyReportRequestDTO.CategoryAndIndustry();
        category1.setSubjectId("SUBJECT_INDUSTRY_NE_001");
        category1.setCategoryType("股票");
        category1.setIndustryName("新能源汽车");
        category1.setIndustryCode("NE001");
        category1.setHistoricalSummary("2024年新能源汽车行业表现亮眼，政策支持力度加大，技术突破显著。");
        category1.setFutureExpectation("2025年预计行业将继续高速发展，但需关注竞争加剧风险。");
        categories1.add(category1);
        institution1.setCategoryAndIndustries(categories1);
        
        // 资产配置
        List<MultiMonthlyReportRequestDTO.AssetAllocation> assets1 = new ArrayList<>();
        MultiMonthlyReportRequestDTO.AssetAllocation asset1 = new MultiMonthlyReportRequestDTO.AssetAllocation();
        asset1.setSubjectId("SUBJECT_ASSET_EQUITY_001");
        asset1.setAssetType("权益类");
        asset1.setAssetName("A股基金");
        asset1.setLowRiskRatio(20.0);
        asset1.setMediumRiskRatio(50.0);
        asset1.setHighRiskRatio(30.0);
        assets1.add(asset1);
        institution1.setAssetAllocations(assets1);
        
        // 宏观经济
        List<MultiMonthlyReportRequestDTO.MacroEconomic> macros1 = new ArrayList<>();
        MultiMonthlyReportRequestDTO.MacroEconomic macro1 = new MultiMonthlyReportRequestDTO.MacroEconomic();
        macro1.setSubjectId("SUBJECT_MACRO_MONETARY_001");
        macro1.setAspectDetail("货币政策");
        
        List<MultiMonthlyReportRequestDTO.MacroEconomicItem> items1 = new ArrayList<>();
        MultiMonthlyReportRequestDTO.MacroEconomicItem item1 = new MultiMonthlyReportRequestDTO.MacroEconomicItem();
        item1.setSubtitle("稳健货币政策");
        item1.setContent("央行将继续实施稳健的货币政策，保持流动性合理充裕。");
        items1.add(item1);
        macro1.setItems(items1);
        macros1.add(macro1);
        institution1.setMacroEconomics(macros1);
        
        institutions.add(institution1);
        
        // 机构2
        MultiMonthlyReportRequestDTO.Institution institution2 = new MultiMonthlyReportRequestDTO.Institution();
        institution2.setCfhId("CFH002");
        institution2.setInstitutionName("华夏基金管理");
        institution2.setTaskMonth("2024-12");
        
        // 大类与行业
        List<MultiMonthlyReportRequestDTO.CategoryAndIndustry> categories2 = new ArrayList<>();
        MultiMonthlyReportRequestDTO.CategoryAndIndustry category2 = new MultiMonthlyReportRequestDTO.CategoryAndIndustry();
        category2.setSubjectId("SUBJECT_INDUSTRY_NE_001");
        category2.setCategoryType("股票");
        category2.setIndustryName("新能源汽车");
        category2.setIndustryCode("NE001");
        category2.setHistoricalSummary("新能源汽车行业快速发展，但估值偏高，存在调整风险。");
        category2.setFutureExpectation("长期看好行业发展前景，但短期需谨慎，关注估值合理性。");
        categories2.add(category2);
        institution2.setCategoryAndIndustries(categories2);
        
        // 资产配置
        List<MultiMonthlyReportRequestDTO.AssetAllocation> assets2 = new ArrayList<>();
        MultiMonthlyReportRequestDTO.AssetAllocation asset2 = new MultiMonthlyReportRequestDTO.AssetAllocation();
        asset2.setSubjectId("SUBJECT_ASSET_EQUITY_001");
        asset2.setAssetType("权益类");
        asset2.setAssetName("A股基金");
        asset2.setLowRiskRatio(30.0);
        asset2.setMediumRiskRatio(40.0);
        asset2.setHighRiskRatio(30.0);
        assets2.add(asset2);
        institution2.setAssetAllocations(assets2);
        
        // 宏观经济
        List<MultiMonthlyReportRequestDTO.MacroEconomic> macros2 = new ArrayList<>();
        MultiMonthlyReportRequestDTO.MacroEconomic macro2 = new MultiMonthlyReportRequestDTO.MacroEconomic();
        macro2.setSubjectId("SUBJECT_MACRO_MONETARY_001");
        macro2.setAspectDetail("货币政策");
        
        List<MultiMonthlyReportRequestDTO.MacroEconomicItem> items2 = new ArrayList<>();
        MultiMonthlyReportRequestDTO.MacroEconomicItem item2 = new MultiMonthlyReportRequestDTO.MacroEconomicItem();
        item2.setSubtitle("政策预期");
        item2.setContent("货币政策有望保持稳健，但需关注通胀压力对政策的影响。");
        items2.add(item2);
        macro2.setItems(items2);
        macros2.add(macro2);
        institution2.setMacroEconomics(macros2);
        
        institutions.add(institution2);
        
        request.setInstitutions(institutions);
        
        return request;
    }

    @Test
    public void testWithMockDatabaseData() {
        try {
            log.info("开始测试模拟数据库数据");

            // 生成模拟数据库查询结果
            List<Map<String, Object>> mockData = generateMockDatabaseData();

            log.info("生成的模拟数据库数据：");
            for (Map<String, Object> data : mockData) {
                log.info("数据项：{}", JSON.toJSONString(data));
            }

            // 验证数据结构
            assert !mockData.isEmpty() : "模拟数据不能为空";

            // 验证包含多个机构
            Set<String> cfhIds = mockData.stream()
                    .map(data -> (String) data.get("CFHID"))
                    .collect(java.util.stream.Collectors.toSet());

            assert cfhIds.size() >= 2 : "应该包含至少2个机构的数据";

            log.info("发现{}个机构：{}", cfhIds.size(), cfhIds);

            log.info("模拟数据库数据测试完成");

        } catch (Exception e) {
            log.error("测试模拟数据库数据异常", e);
            throw e;
        }
    }

    /**
     * 生成模拟数据库查询结果
     */
    private List<Map<String, Object>> generateMockDatabaseData() {
        List<Map<String, Object>> mockData = new ArrayList<>();

        // 机构1：东方财富资产管理
        String cfhId1 = "CFH001";
        String institutionName1 = "东方财富资产管理";
        String taskConfigId1 = "TASK_CONFIG_001";
        String taskMonth = "2024-12";
        String deadline = "2024-12-31";

        // 机构1 - 行业分析数据
        Map<String, Object> industry1 = new HashMap<>();
        industry1.put("CFHID", cfhId1);
        industry1.put("TaskConfigID", taskConfigId1);
        industry1.put("institutionName", institutionName1);
        industry1.put("taskMonth", taskMonth);
        industry1.put("categoryType", "股票");
        industry1.put("industryName", "新能源汽车");
        industry1.put("industryCode", "NE001");
        industry1.put("historicalSummary", "2024年新能源汽车行业在政策支持和技术进步双重驱动下表现亮眼，主要车企销量创新高，产业链上下游协同发展，电池技术突破带来成本下降。");
        industry1.put("futureExpectation", "2025年预计新能源汽车渗透率将进一步提升，智能化程度不断提高，海外市场拓展加速，但需关注补贴政策退坡和市场竞争加剧的影响。");
        industry1.put("deadline", deadline);
        industry1.put("subjectId", "SUBJECT_INDUSTRY_NE_001");
        mockData.add(industry1);

        // 机构1 - 资产配置数据
        Map<String, Object> asset1 = new HashMap<>();
        asset1.put("CFHID", cfhId1);
        asset1.put("TaskConfigID", taskConfigId1);
        asset1.put("institutionName", institutionName1);
        asset1.put("taskMonth", taskMonth);
        asset1.put("assetType", "权益类");
        asset1.put("assetName", "A股基金");
        asset1.put("industryCode", "EQUITY_001");
        asset1.put("lowRiskRatio", "20.0");
        asset1.put("mediumRiskRatio", "50.0");
        asset1.put("highRiskRatio", "30.0");
        asset1.put("deadline", deadline);
        asset1.put("subjectId", "SUBJECT_ASSET_EQUITY_001");
        mockData.add(asset1);

        // 机构1 - 宏观经济数据
        Map<String, Object> macro1 = new HashMap<>();
        macro1.put("CFHID", cfhId1);
        macro1.put("TaskConfigID", taskConfigId1);
        macro1.put("institutionName", institutionName1);
        macro1.put("taskMonth", taskMonth);
        macro1.put("SubjectType", "货币政策");
        macro1.put("SubjectTitle", "央行政策展望");
        macro1.put("pointPrediction", "{\"detailList\":[{\"content\":\"央行继续实施稳健的货币政策，保持流动性合理充裕，支持实体经济发展。\",\"title\":\"稳健货币政策\"},{\"content\":\"预计利率将保持相对稳定，根据经济形势适度调整，关注通胀预期管理。\",\"title\":\"利率政策预期\"}]}");
        macro1.put("deadline", deadline);
        macro1.put("subjectId", "SUBJECT_MACRO_MONETARY_001");
        mockData.add(macro1);

        // 机构2：华夏基金管理
        String cfhId2 = "CFH002";
        String institutionName2 = "华夏基金管理";
        String taskConfigId2 = "TASK_CONFIG_002";

        // 机构2 - 行业分析数据
        Map<String, Object> industry2 = new HashMap<>();
        industry2.put("CFHID", cfhId2);
        industry2.put("TaskConfigID", taskConfigId2);
        industry2.put("institutionName", institutionName2);
        industry2.put("taskMonth", taskMonth);
        industry2.put("categoryType", "股票");
        industry2.put("industryName", "新能源汽车");
        industry2.put("industryCode", "NE001");
        industry2.put("historicalSummary", "新能源汽车行业快速发展，但估值偏高，存在调整风险，需要关注盈利能力的可持续性，市场竞争日趋激烈。");
        industry2.put("futureExpectation", "长期看好行业发展前景，但短期需谨慎，关注估值合理性和行业整合机会，重点关注具有核心技术优势的龙头企业。");
        industry2.put("deadline", deadline);
        industry2.put("subjectId", "SUBJECT_INDUSTRY_NE_001");
        mockData.add(industry2);

        // 机构2 - 资产配置数据
        Map<String, Object> asset2 = new HashMap<>();
        asset2.put("CFHID", cfhId2);
        asset2.put("TaskConfigID", taskConfigId2);
        asset2.put("institutionName", institutionName2);
        asset2.put("taskMonth", taskMonth);
        asset2.put("assetType", "权益类");
        asset2.put("assetName", "A股基金");
        asset2.put("industryCode", "EQUITY_001");
        asset2.put("lowRiskRatio", "30.0");
        asset2.put("mediumRiskRatio", "40.0");
        asset2.put("highRiskRatio", "30.0");
        asset2.put("deadline", deadline);
        asset2.put("subjectId", "SUBJECT_ASSET_EQUITY_001");
        mockData.add(asset2);

        // 机构2 - 宏观经济数据
        Map<String, Object> macro2 = new HashMap<>();
        macro2.put("CFHID", cfhId2);
        macro2.put("TaskConfigID", taskConfigId2);
        macro2.put("institutionName", institutionName2);
        macro2.put("taskMonth", taskMonth);
        macro2.put("SubjectType", "货币政策");
        macro2.put("SubjectTitle", "政策预期分析");
        macro2.put("pointPrediction", "{\"detailList\":[{\"content\":\"货币政策有望保持稳健，但需关注通胀压力对政策的影响，预计政策将更加灵活。\",\"title\":\"政策预期\"},{\"content\":\"密切关注通胀走势，适时调整政策力度和节奏，平衡增长与稳定的关系。\",\"title\":\"通胀管理\"}]}");
        macro2.put("deadline", deadline);
        macro2.put("subjectId", "SUBJECT_MACRO_MONETARY_001");
        mockData.add(macro2);

        // 机构3：南方基金管理（第三个机构，增加数据丰富度）
        String cfhId3 = "CFH003";
        String institutionName3 = "南方基金管理";
        String taskConfigId3 = "TASK_CONFIG_003";

        // 机构3 - 行业分析数据
        Map<String, Object> industry3 = new HashMap<>();
        industry3.put("CFHID", cfhId3);
        industry3.put("TaskConfigID", taskConfigId3);
        industry3.put("institutionName", institutionName3);
        industry3.put("taskMonth", taskMonth);
        industry3.put("categoryType", "债券");
        industry3.put("industryName", "金融服务");
        industry3.put("industryCode", "FS001");
        industry3.put("historicalSummary", "金融服务行业在利率环境变化中表现稳健，银行业净息差承压但资产质量改善，保险业受益于权益市场回暖。");
        industry3.put("futureExpectation", "预计金融服务业将受益于经济复苏和政策支持，但需关注信用风险和监管政策变化，重点关注数字化转型机会。");
        industry3.put("deadline", deadline);
        industry3.put("subjectId", "SUBJECT_INDUSTRY_FS_001");
        mockData.add(industry3);

        // 机构3 - 资产配置数据
        Map<String, Object> asset3 = new HashMap<>();
        asset3.put("CFHID", cfhId3);
        asset3.put("TaskConfigID", taskConfigId3);
        asset3.put("institutionName", institutionName3);
        asset3.put("taskMonth", taskMonth);
        asset3.put("assetType", "固收类");
        asset3.put("assetName", "债券基金");
        asset3.put("industryCode", "BOND_001");
        asset3.put("lowRiskRatio", "70.0");
        asset3.put("mediumRiskRatio", "25.0");
        asset3.put("highRiskRatio", "5.0");
        asset3.put("deadline", deadline);
        asset3.put("subjectId", "SUBJECT_ASSET_BOND_001");
        mockData.add(asset3);

        // 机构3 - 宏观经济数据
        Map<String, Object> macro3 = new HashMap<>();
        macro3.put("CFHID", cfhId3);
        macro3.put("TaskConfigID", taskConfigId3);
        macro3.put("institutionName", institutionName3);
        macro3.put("taskMonth", taskMonth);
        macro3.put("SubjectType", "财政政策");
        macro3.put("SubjectTitle", "积极财政政策");
        macro3.put("pointPrediction", "{\"detailList\":[{\"content\":\"财政政策更加积极有为，加大对基础设施建设和民生领域的投入，减税降费政策持续推进。\",\"title\":\"积极财政政策\"},{\"content\":\"重点支持科技创新、绿色发展等重点领域，提高财政资金使用效率。\",\"title\":\"重点支持领域\"}]}");
        macro3.put("deadline", deadline);
        macro3.put("subjectId", "SUBJECT_MACRO_FISCAL_001");
        mockData.add(macro3);

        return mockData;
    }
}
