package ttfund.web.fortuneservice.service;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.fortuneservice.FortuneServiceApplication;

/**
 * VIP月度报告总结服务手动测试类
 * 用于手动执行和调试服务
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = FortuneServiceApplication.class)
@Slf4j
public class VIPMonthlyReportSummaryManualTest {

    @Autowired
    private VIPMonthlyReportSummaryService vipMonthlyReportSummaryService;

    /**
     * 手动测试主方法
     * 可以直接运行此方法来测试服务
     */
    @Test
    public void manualTestGenerateMonthlyReportSummary() {
        log.info("=== 开始手动测试VIP月度报告总结服务 ===");
        
        try {
            // 执行服务方法
            log.info("调用generateMonthlyReportSummary方法...");
            boolean result = vipMonthlyReportSummaryService.generateMonthlyReportSummary("manual-test");
            
            log.info("服务执行结果：{}", result);
            
            if (result) {
                log.info("✅ 服务执行成功！");
            } else {
                log.warn("⚠️ 服务执行返回false，可能没有数据需要处理");
            }
            
        } catch (Exception e) {
            log.error("❌ 服务执行异常：", e);
            
            // 分析异常类型
            if (e.getMessage() != null) {
                if (e.getMessage().contains("database") || e.getMessage().contains("SQL")) {
                    log.error("数据库相关异常，请检查数据库连接和表结构");
                } else if (e.getMessage().contains("http") || e.getMessage().contains("connection")) {
                    log.error("网络连接异常，请检查AI接口地址和网络连接");
                } else {
                    log.error("其他异常：{}", e.getMessage());
                }
            }
        }
        
        log.info("=== 手动测试完成 ===");
    }

    /**
     * 测试数据库查询部分
     */
    @Test
    public void testDatabaseQueryOnly() {
        log.info("=== 测试数据库查询部分 ===");
        
        try {
            // 这里可以添加直接的数据库查询测试
            log.info("数据库查询测试开始...");
            
            // 由于无法直接访问私有方法，这里只能通过完整服务调用来测试
            // 在实际使用中，可以将查询方法设为protected或package-private以便测试
            
            log.info("数据库查询测试完成");
            
        } catch (Exception e) {
            log.error("数据库查询测试异常：", e);
        }
    }

    /**
     * 测试配置信息
     */
    @Test
    public void testConfiguration() {
        log.info("=== 测试配置信息 ===");
        
        try {
            // 检查服务是否正确注入
            assert vipMonthlyReportSummaryService != null : "服务未正确注入";
            log.info("✅ 服务注入成功");
            
            // 可以添加更多配置检查
            log.info("配置检查完成");
            
        } catch (Exception e) {
            log.error("配置测试异常：", e);
        }
    }

    /**
     * 测试JSON序列化和反序列化
     */
    @Test
    public void testJsonSerialization() {
        log.info("=== 测试JSON序列化 ===");
        
        try {
            // 测试复杂对象的JSON序列化
            TestData testData = new TestData();
            testData.setName("测试数据");
            testData.setValue(123.45);
            
            String json = JSON.toJSONString(testData);
            log.info("序列化结果：{}", json);
            
            TestData parsed = JSON.parseObject(json, TestData.class);
            log.info("反序列化结果：name={}, value={}", parsed.getName(), parsed.getValue());
            
            assert testData.getName().equals(parsed.getName()) : "名称应该一致";
            assert testData.getValue().equals(parsed.getValue()) : "值应该一致";
            
            log.info("✅ JSON序列化测试通过");
            
        } catch (Exception e) {
            log.error("JSON序列化测试异常：", e);
        }
    }

    /**
     * 性能测试
     */
    @Test
    public void performanceTest() {
        log.info("=== 性能测试 ===");
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 执行服务方法
            boolean result = vipMonthlyReportSummaryService.generateMonthlyReportSummary("performance-test");
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.info("执行结果：{}", result);
            log.info("执行时间：{}ms", duration);
            
            if (duration < 30000) { // 30秒
                log.info("✅ 性能测试通过（执行时间 < 30秒）");
            } else {
                log.warn("⚠️ 执行时间较长：{}ms", duration);
            }
            
        } catch (Exception e) {
            log.error("性能测试异常：", e);
        }
    }

    /**
     * 压力测试（多次调用）
     */
    @Test
    public void stressTest() {
        log.info("=== 压力测试 ===");
        
        int testCount = 3;
        int successCount = 0;
        
        for (int i = 1; i <= testCount; i++) {
            try {
                log.info("第{}次调用开始...", i);
                
                boolean result = vipMonthlyReportSummaryService.generateMonthlyReportSummary("stress-test-" + i);
                
                if (result) {
                    successCount++;
                    log.info("第{}次调用成功", i);
                } else {
                    log.warn("第{}次调用返回false", i);
                }
                
                // 间隔1秒
                Thread.sleep(1000);
                
            } catch (Exception e) {
                log.error("第{}次调用异常：", i, e);
            }
        }
        
        log.info("压力测试完成：{}/{} 次成功", successCount, testCount);
    }

    /**
     * 测试CFHID和TaskConfigID功能
     */
    @Test
    public void testCfhidAndTaskConfigId() {
        log.info("=== 测试CFHID和TaskConfigID功能 ===");

        try {
            // 这里可以添加具体的测试逻辑
            // 验证SQL查询是否包含CFHID和TaskConfigID
            // 验证分组逻辑是否正确
            // 验证保存到数据库时是否正确设置了这些字段

            log.info("CFHID和TaskConfigID功能测试完成");

        } catch (Exception e) {
            log.error("CFHID和TaskConfigID功能测试异常：", e);
        }
    }

    /**
     * 测试数据类
     */
    public static class TestData {
        private String name;
        private Double value;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Double getValue() {
            return value;
        }

        public void setValue(Double value) {
            this.value = value;
        }
    }
}
