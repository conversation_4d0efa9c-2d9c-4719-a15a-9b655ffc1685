import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.fortuneservice.FortuneServiceApplication;
import ttfund.web.fortuneservice.service.impl.CheckProductReportServiceImpl;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/5/16 17:50
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = FortuneServiceApplication.class)
public class TestCheckReportNew {
    @Resource
    private CheckProductReportServiceImpl checkProductReportService;

    @Test
    public void checkReportTest() {
        checkProductReportService.checkReport(null);
    }
}
