import com.ttfund.web.base.helper.DateHelper;
import groovy.transform.SourceURI;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.fortuneservice.FortuneServiceApplication;
import ttfund.web.fortuneservice.manager.DongDongManager;
import ttfund.web.fortuneservice.service.CFHDataToMongoService;
import ttfund.web.fortuneservice.utils.CommonUtil;

import java.util.Calendar;
import java.util.Date;

import javax.annotation.Resource;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = FortuneServiceApplication.class)
public class TestCFHDataToMongoService {

    @Autowired
    private CFHDataToMongoService service;

    @Test
    public void test(){
        service.handCFHData();
    }

    @Resource
    private DongDongManager dongDongManager;

    @Test
    public void dongDongTest (){
        boolean result = dongDongManager.sendDongDongMessage("220089", "测试一下", "[JC_2023Q1ziguan, JC_2022simu, JC_2022ziguan]");
        System.out.println("result = " + result);
    }

    @Test
    public void timeTransTest(){
        Date currentBelongDay = CommonUtil.getCurrentBelongDay(DateHelper.getNowDate());
        System.out.println(currentBelongDay);

        Calendar calendar = Calendar.getInstance();
        Date firstDayOfMonth = CommonUtil.getCurrentBelongDay(CommonUtil.getFirstDayOfMonth(calendar));
        System.out.println("firstDayOfMonth = " + firstDayOfMonth);

        Date lastDayOfMonth = CommonUtil.getCurrentBelongDay(CommonUtil.getLastDayOfMonth(calendar));
        System.out.println("lastDayOfMonth = " + lastDayOfMonth);

        int currentMonth = CommonUtil.getCurrentMonth(calendar);
        System.out.println("currentMonth = " + currentMonth);
    }
}
