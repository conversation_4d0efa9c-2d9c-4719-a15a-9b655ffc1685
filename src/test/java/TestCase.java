import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.fortuneservice.FortuneServiceApplication;
import ttfund.web.fortuneservice.job.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className TestCase
 * @date 2023/4/11 9:13
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = FortuneServiceApplication.class)
public class TestCase {

    @Resource
    private FundManagerJob fundManagerJob;

    @Resource
    private HotLabelJob hotLabelJob;

    @Resource
    private ProductReadJob productReadJob;

    @Resource
    private ResearchTaskAutoGenerateJob researchTaskAutoGenerateJob;

    @Test
    public void t1() throws Exception {
        fundManagerJob.execute(null);
    }

    @Test
    public void t2() throws Exception {
        hotLabelJob.execute(null);
    }

    @Test
    public void t3() throws Exception {
        productReadJob.execute(null);
    }

    @Test
    public void TestResearchJob() throws Exception {
        researchTaskAutoGenerateJob.execute(null);
    }
}
