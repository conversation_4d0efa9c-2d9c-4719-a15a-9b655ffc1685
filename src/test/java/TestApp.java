import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ttfund.web.base.helper.DateHelper;
import com.ttfund.web.base.helper.HttpHelper;
import com.ttfund.web.core.model.ApiResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.Document;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.fortuneservice.FortuneServiceApplication;
import ttfund.web.fortuneservice.config.App;
import ttfund.web.fortuneservice.constant.CFHMongodbConstant;
import ttfund.web.fortuneservice.dao.VerticaDao;
import ttfund.web.fortuneservice.dao.mongo.CFHMongoMapper;
import ttfund.web.fortuneservice.dao.sqlserver.VIPMeetingMapper;
import ttfund.web.fortuneservice.job.CFHTradeDataStatJob;
import ttfund.web.fortuneservice.manager.HqApiManager;
import ttfund.web.fortuneservice.model.dto.*;
import ttfund.web.fortuneservice.service.*;
import ttfund.web.fortuneservice.service.impl.*;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = FortuneServiceApplication.class)
public class TestApp {


    @Autowired
    private FundManagerService fundManagerService;

    @Autowired
    private App app;

    @Autowired
    private HotLabelService hotlabelService;

    @Resource
    private ProductReadService productReadService;

    @Autowired
    HqApiManager hqApiManager;

    @Test
    public void test() throws UnsupportedEncodingException {
        //上一个交易时间跟今天0点时间对比是否相等
        int t = 2;
        Date dtday = DateHelper.stringToDate2("2022-12-06 18:00:01", DateHelper.FORMAT_YYYY_MM_DD_HH_MM_SS);
        Date tradeDayPre = hqApiManager.getTradeDayPre(dtday, t);

        dtday = DateHelper.stringToDate2(DateHelper.dateToStr(dtday, DateHelper.FORMAT_YYYY_MM_DD), DateHelper.FORMAT_YYYY_MM_DD);
        dtday = DateUtils.addSeconds(dtday, 1);
//        Date tradeDayPre1 = hqMongodb.getTradeDayPre(dtday, t);

//        System.out.println(tradeDayPre1);
        System.out.println(tradeDayPre);
    }

    @Test
    public void testFundManagerService() {
        fundManagerService.fundManagerHandler();
    }

    @Test
    public void testLabelService() {
        hotlabelService.hotLabelHandler();

    }

    @Test
    public void testLabelService2() {
        hotlabelService.activityLabelHandler();

    }

    @Test
    public void testCfhMongoDbWrite() {
        Document document = new Document();
        document.append("CFHID", "9300059");
        document.append("CFHName", "天天基金");
        document.append("MGRID", "30544266");
        document.append("MGRName", "尹粒宇");
        document.append("Title", "观点标题1");
        document.append("ReviewContent", "观点内容111");
        document.append("EmotionType", 1);
        document.append("ProductType", 1);
        document.append("ProductCode", "007842");
        document.append("ProductTags", "标签1,标签2");
        document.append("ProductType2", 0);
        document.append("ProductCode2", null);
        document.append("ProductTags2", null);
        document.append("Status", 1);
        document.append("IsDel", 0);
        document.append("CreateTime", "2021-08-02 02:06:31.08");
        document.append("UpdateTime", "2021-08-02 02:06:31.08");
        document.append("UpdateDay", "20210802");
        document.append("timepoint", "1627869991080300059");
        document.append("timepoint", "1627869991080300059");
        document.append("Weight", "100");
        List<FundThemeDto> list = new ArrayList<>();
        list.add(new FundThemeDto("1", "标签1", null, null));
        list.add(new FundThemeDto("2", "标签2", null, null));
        document.append("ThemeLabel", JSON.toJSONString(list));


        app.getCfhMongodbRead().getMongoserver().getDatabase("TTFundCFHDB").getCollection("Tb_CFHQuickReview")
                .insertOne(document);

    }

    @Test
    public void testHttp() {
        ApiResponse<List<FundLabelDto>, Object> apiResult = new ApiResponse<>();
        Gson gson = new Gson();
        String result = HttpHelper.requestGet("null");
        if (StringUtils.isNotEmpty(result)) {
            apiResult = gson.fromJson(result, new TypeToken<ApiResponse<List<FundLabelDto>, Object>>() {
            }.getType());
        }
        List<FundLabelDto> labelList = apiResult.getData();
        for (FundLabelDto label : labelList) {
            String indexCode = label.getIndexCode();

            String result2 = HttpHelper.requestGet("http://localhost:8081/cfh/quickreview/reviewlist?labelId=" + indexCode + "&mgrId=30181039", null, false);
            if (indexCode.equals("801152") || indexCode.equals("861431") || indexCode.equals("801017")) {
                System.out.println(result2);
                System.out.println("---------------------------------------------------------------");
            }
        }
    }

    @Test
    public void t1() {
        productReadService.writeProductReadIntoHq(new Date());
    }

    @Autowired
    CheckProductReportService checkProductReportService;

    @Test
    public void testCheckProduct() {
//        checkProductReportService.checkReport("2024-04-15");//检查q1
//        checkProductReportService.checkReport("2023-07-17");//检查q2
//        checkProductReportService.checkReport("2023-10-23");//检查q3
//        checkProductReportService.checkReport("2024-01-16");//检查q4
        checkProductReportService.checkReportPush();

    }

    @Resource
    private FundNetWorthInspectionService inspectionService;

    @Test
    public void testInspectionService() {
        inspectionService.inspectionService("2023-07-17 00:00:00");
    }

    @Autowired
    UpdateCfhFundThemeService updateCfhFundThemeService;

    @Test
    public void testUpdateTheme() {
        updateCfhFundThemeService.handCFHFundTheme();
    }

    @Resource
    private QuarterlyReportService quarterlyReportService;

    @Test
    public void testAddQuarterlyReport() {
        String value = "{\"FCODE\":\"000001\",\"REPORTDATE\":\"2023-09-30\",\"STYLE\":\"3\",\"WARNNUM\":6,\"CREATETIME\":\"2023-12-11 12:00:00\"}";
        quarterlyReportService.quarterlyReportToProductRead(value);

    }

    @Resource
    private CheckProductReportService checkProductReportServiceImpl;

    @Test
    public void test123() {
        checkProductReportServiceImpl.checkReportPush();
    }

    @Autowired
    VerticaDao verticaDao;

    @Test
    public void testvertica() {
        verticaDao.getCommunityDataDaysOfMouth(new Date());
    }

    @Test
    public void testVertica() {
        String sql = "select 1";
        System.out.println(app.getVerticaRead().executeQuery(sql, null));
    }

    @Resource
    CFHTradeDataDBDropService cfhTradeDataDBDropService;

    @Test
    public void testDropMongoTrade() {
        cfhTradeDataDBDropService.execute();
    }

    @Resource
    CFHTradeDataOnDayRelocateService cfhTradeDataOnDayRelocateService;

    @Test
    public void testCFHTradeDataOnDayRelocateJob() {
        cfhTradeDataOnDayRelocateService.syncBack("20240530");

    }

    @Resource
    CFHTradeDataOnHourRelocateService cfhTradeDataOnHourRelocateService;

    @Test
    public void testCFHTradeDataOnHourRelocateJob() {

        cfhTradeDataOnHourRelocateService.sync();
    }

    @Autowired
    private TradeStatServiceImpl tradeStatService;

    @Test
    public void test222() {
        tradeStatService.cfhTradeRankJob("20240831");
    }


    @Autowired
    private ShortVideoStatService shortVideoStatService;

    @Test
    public void test3() {
        String message = "{\"op_type\":2011,\"op_ctx\":{\"id\":\"4559693\",\"av_info\":{\"id\":\"4559693\",\"name\":\"多家银行被要求商贷利率不得低于公积金\",\"cover\":\"https://emav-cos.lvb.eastmoney.com/roadshow_cover/channel/4559693/6503906F/4559691_w480h853.jpg\",\"cover_view\":{\"cover\":\"https://emav-cos.lvb.eastmoney.com/roadshow_cover/channel/4559693/6503906F/4559691_w480h853.jpg\",\"cover_width\":480,\"cover_height\":853,\"cover_quality\":1,\"cover_md5\":\"30f30ee298bd692be06efefeb6235b8a\",\"cover_operator\":\"\"},\"frame_cover\":\"https://emav-cos.lvb.eastmoney.com/roadshow_cover/channel/4559693/E3F8BF6C/frame_w1080h1920.jpg\",\"frame_cover_view\":{\"cover\":\"https://emav-cos.lvb.eastmoney.com/roadshow_cover/channel/4559693/E3F8BF6C/frame_w1080h1920.jpg\",\"cover_width\":1080,\"cover_height\":1920,\"cover_quality\":0,\"cover_md5\":\"4ae8232d1e4a5a931549212de1e9f61a\",\"cover_operator\":\"uploader\"},\"v_cover\":\"https://emav-cos.lvb.eastmoney.com/roadshow_cover/channel/4559693/F6B68933/v_w1080h1920.jpg\",\"v_cover_view\":{\"cover\":\"https://emav-cos.lvb.eastmoney.com/roadshow_cover/channel/4559693/F6B68933/v_w1080h1920.jpg\",\"cover_width\":1080,\"cover_height\":1920,\"cover_quality\":1,\"cover_md5\":\"22d7d85d9259cc455f441a53eef9a305\",\"cover_operator\":\"\"},\"h_cover\":\"https://emav-cos.lvb.eastmoney.com/roadshow_cover/channel/4559693/8532FDA6/h_w3413h1920.jpg\",\"h_cover_view\":{\"cover\":\"https://emav-cos.lvb.eastmoney.com/roadshow_cover/channel/4559693/8532FDA6/h_w3413h1920.jpg\",\"cover_width\":3413,\"cover_height\":1920,\"cover_quality\":1,\"cover_md5\":\"13f2dea30ea3a635a17300e0bf4ce148\",\"cover_operator\":\"\"},\"cover_md5\":\"30f30ee298bd692be06efefeb6235b8a\",\"frame_cover_md5\":\"4ae8232d1e4a5a931549212de1e9f61a\",\"v_cover_md5\":\"22d7d85d9259cc455f441a53eef9a305\",\"h_cover_md5\":\"13f2dea30ea3a635a17300e0bf4ce148\",\"intro\":\"\",\"create_time\":\"2024-10-24T15:01:06\",\"start_time\":\"2024-10-24T15:01:06\",\"end_time\":\"2024-10-24T15:01:11\",\"status\":3,\"type\":102,\"mode\":1,\"live_type\":100,\"present_type\":1,\"view_type\":0,\"anchor\":{\"emuser_auth\":0,\"emauth_cert_no\":\"\",\"id\":\"2779015946589648\",\"emid\":\"138373512\",\"emuid\":\"6447094368484528\",\"nickname\":\"贝壳财经\",\"emnickname\":\"贝壳财经\",\"emintro\":\"\",\"target_nickname\":\"\",\"target_avatar\":\"\",\"code\":\"\",\"abbr\":\"\",\"auth\":\"\",\"avatar\":\"https://avatar.eastmoney.com/qface/6447094368484528/180\",\"like_count\":297},\"presenters\":[{\"auth_status\":0,\"enroll_status\":0,\"emauth_cert_no\":\"\",\"id\":\"2779015946589648\",\"emid\":\"138373512\",\"emuid\":\"6447094368484528\",\"nickname\":\"贝壳财经\",\"emnickname\":\"贝壳财经\",\"emintro\":\"\",\"target_nickname\":\"贝壳财经\",\"target_avatar\":\"https://avatar.eastmoney.com/qface/6447094368484528/180\",\"alias\":\"贝壳财经\",\"position\":\"\",\"avatar\":\"https://avatar.eastmoney.com/qface/6447094368484528/180\",\"user_type\":1,\"user_login_time\":0,\"user_enter_time\":0,\"enroll\":0}],\"playback\":true,\"share_config\":{\"enable\":true,\"title\":\"多家银行被要求商贷利率不得低于公积金\",\"sub_title\":\"多家银行被要求商贷利率不得低于公积金\",\"desc\":\"分享东方财富视频《多家银行被要求商贷利率不得低于公积金》\",\"link\":\"https://roadshow.lvb.eastmoney.com/LVB/Nav/Room/langke/4559693\",\"em_link\":\"https://roadshow.lvb.eastmoney.com/LVB/Nav/Room/em/4559693\",\"lk_slogan\":\"财经世界精彩瞬间 尽在浪客财经小视频\",\"em_slogan\":\"财经世界精彩瞬间 尽在东方财富财经小视频\",\"img_url\":\"https://emav-cos.lvb.eastmoney.com/roadshow_cover/channel/4559693/A63B96BE/avs_w200h200.jpg\",\"icon_url\":\"https://list.lvb.eastmoney.com/icon/<EMAIL>\",\"em_icon_url\":\"https://list.lvb.eastmoney.com/icon/<EMAIL>\",\"em_share_poster\":\"https://emav-1252033264.cos.ap-chongqing.myqcloud.com/roashow_share/poster/20241024/4559691_em.png\",\"lk_share_poster\":\"https://emav-1252033264.cos.ap-chongqing.myqcloud.com/roashow_share/poster/20241024/4559691_lk.png\",\"share_qrcode\":\"\",\"em_share_qrcode\":\"https://emav-1252033264.cos.ap-chongqing.myqcloud.com/roashow_share/qrcode/20241024/4559691_em.png\",\"lk_share_qrcode\":\"https://emav-1252033264.cos.ap-chongqing.myqcloud.com/roashow_share/qrcode/20241024/4559691_lk.png\",\"em_share_qrcode_wl\":\"https://emav-1252033264.cos.ap-chongqing.myqcloud.com/roashow_share/qrcode/20241024/4559691_em_wl.png\",\"lk_share_qrcode_wl\":\"https://emav-1252033264.cos.ap-chongqing.myqcloud.com/roashow_share/qrcode/20241024/4559691_lk_wl.png\"},\"cfh_art_code\":\"20241024150104323651420\",\"post_id\":\"1474544068\",\"web_dist_type\":2,\"app_dist_type\":1,\"mp_param\":\"pages/shortVideo/shortVideo?channel_id=4559693\",\"ui_style\":2,\"person_time\":0,\"heat_degree\":0,\"heat_degree_switch\":false,\"like_count\":0,\"like_count_switch\":true,\"paid\":false,\"tag_id_set\":[\"a4eb5725978e425d8297bd897e0a58b0\",\"fe38dd9a5540499abb1c09d6c2dca256\",\"41afb65c44f340d9947ef15bb0e476b7\",\"ceec1e2caeba486fb4ec7c76983e099f\",\"9cef3d70b0984d4ea027d580181ba7af\",\"c8cf0284e8034331af45662c7099a2eb\",\"0cbdbc927c5145baa42bd923e9848633\",\"a5a105d382204309bc75ad32be895587\"],\"ref_quote\":[{\"q_market\":116,\"q_code\":\"007455\"},{\"q_market\":116,\"q_code\":\"017072\"},{\"q_market\":116,\"q_code\":\"159745\"},{\"q_market\":90,\"q_code\":\"BK0475\"}],\"anchor_status\":0,\"stream_status\":0,\"activity_configuration\":{},\"source\":100,\"content_source\":31200,\"v-width\":720,\"v-height\":1280,\"duration\":5,\"playback_chapter\":[{\"id\":\"45a0VKpi5qmJQT/CMG56DA==\",\"media_group\":[{\"format\":\"mp4\",\"av_type\":22,\"media\":{\"format\":\"mp4\",\"size\":535562,\"duration\":5,\"url\":\"http://1500000598.vod2.myqcloud.com/438174e6vodtranscq1500000598/47a7a4091397757895896751883/v.f30.mp4\",\"width\":720,\"height\":1280}},{\"format\":\"hls\",\"av_type\":23,\"media\":{\"format\":\"hls\",\"size\":532091,\"duration\":5,\"url\":\"http://1500000598.vod2.myqcloud.com/438174e6vodtranscq1500000598/47a7a4091397757895896751883/v.f230.m3u8\",\"width\":720,\"height\":1280}}]}],\"formal\":true,\"list_state\":0,\"ad\":0,\"audit_status\":1,\"foreshow_begin_time\":\"2024-10-24T15:01:06\",\"foreshow_end_time\":\"2024-10-24T15:01:11\",\"live_begin_time\":\"2024-10-24T15:01:06\",\"live_end_time\":\"2024-10-24T15:01:11\"},\"report_id\":\"\",\"report_detail\":{\"live_summary\":{\"channel_id\":\"4563217\",\"channel_name\":\"首席连线丨广发证券宫帅：金价连创新高，还有多少向上空间\",\"start_time\":\"2024-10-28 15:59:11\",\"end_time\":\"2024-10-28 17:12:44\",\"duration\":\"74\",\"view_count_guba\":\"24\",\"comment_count_guba\":\"\",\"like_count_guba\":\"66\",\"share_count_guba\":\"\"},\"edw_delta\":{\"vd_max_play_dur\":0.0,\"vd_follow_num\":1,\"vd_pv_num\":0,\"vd_play_dur\":0.0,\"vd_homepage_num\":1,\"vd_finished_num\":5,\"vd_favorite_num\":1,\"vd_comment_num\":1,\"vd_repost_num\":1,\"vd_like_num\":1,\"vd_finished_fiveseconds\":1,\"vd_finished_tenseconds\":1,\"vd_finished_thirtyseconds\":1}}},\"mq_message_source\":0,\"timestamp\":1730106928339,\"server_ip\":\"************\",\"server_name\":\"WA-EM\",\"remote_ip\":null,\"remote_port\":null}";
        shortVideoStatService.handler(message);
    }

    @Autowired
    private CFHScoreService service;

    @Test
    public void testSyncCfhScore() {
        service.syncCFHScore(null);
    }

    @Autowired
    TradeDateService tradeDateService;

    @Test
    public void testtradeDateService() {
        tradeDateService.syncTradeDate("true");
    }

    @Resource
    private CFHBaseInfoService cfhBaseInfoService;

    @Test
    public void testcfhBaseInfoService() {
        cfhBaseInfoService.syncCFHAdminUser("");
    }

    @Autowired
    private MessageService messageService;

    @Test
    public void testDelKafka() {
        messageService.deleteMessage();
    }

    @Autowired
    private CFHTradeDataStatJob cfhTradeDataStatJob;

    @Test
    public void testKafka() {
        try {
            cfhTradeDataStatJob.execute(null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Autowired
    VIPMeetingServiceImpl vipMeetingService;
    @Autowired
    private VIPMeetingMapper meetingMapper;

    @Autowired
    private CFHMongoMapper cfhMongoMapper;

//    @Test
//    public void testVIPMeeting() {
//        String str = "[{\"topic\":\"股市\",\"viewpoint\":\"中国资产性价比高，形成上行趋势。\",\"codes\":{\"FUND\":[],\"INDEX\":[],\"SECTOR\":[]}},{\"topic\":\"科技\",\"viewpoint\":\"科技成长板块成为市场进攻主线，受益于流动性充裕和政策支持。\",\"codes\":{\"FUND\":[],\"INDEX\":[],\"SECTOR\":[\"BK000391\"]}},{\"topic\":\"ETF\",\"viewpoint\":\"推荐使用ETF和联接基金，从配置视角把握行情。\",\"codes\":{\"FUND\":[],\"INDEX\":[],\"SECTOR\":[]}},{\"topic\":\"A500\",\"viewpoint\":\"中证A500指数受关注，适合作为配置核心资产的工具。\",\"codes\":{\"FUND\":[],\"INDEX\":[\"000510\"],\"SECTOR\":[]}}]";
//        List<MeetingSummaryPoint> viewpointList = JSON.parseArray(str, MeetingSummaryPoint.class);
//        //写库
//        List<MeetingSummaryMongo> mongoList = new ArrayList<>();
//        for (MeetingSummaryPoint point : viewpointList) {
//            mongoList.add(new MeetingSummaryMongo(point, meeting));
//        }
//        cfhMongoMapper.insertOrUpdateById(mongoList, CFHMongodbConstant.TB_VIP_MEETING_SUMMARY_LIB);
//    }

    @Autowired
    TaskAutoGenerateService taskAutoGenerateService;
    @Test
    public void testVIPMeeting() {
        taskAutoGenerateService.autoGenerateVIPService();
    }

}
