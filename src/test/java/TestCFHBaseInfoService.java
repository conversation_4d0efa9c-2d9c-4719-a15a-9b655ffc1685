import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.fortuneservice.FortuneServiceApplication;
import ttfund.web.fortuneservice.model.bo.CFHBaseInfoBo;
import ttfund.web.fortuneservice.model.dto.CFHInfoDto;
import ttfund.web.fortuneservice.service.CFHBaseInfoService;
import ttfund.web.fortuneservice.service.impl.CFHBaseInfoServiceImpl;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = FortuneServiceApplication.class)
public class TestCFHBaseInfoService {

    @Autowired
    private CFHBaseInfoServiceImpl service;

    @Test
    public void test(){
        String str = "{\"AccountId\":*********,\"AccountName\":\"人才ETF1223\",\"AccountStatus\":1,\"AccountType\":\"沪深\",\"Banner\":\"\",\"BigVip\":0,\"CreateTime\":\"2022-11-17T10:19:59\",\"Email\":\"\",\"Hottype\":0,\"IsDeleted\":0,\"Name\":\"\",\"NewPrivateFundParam\":\"***********\",\"NickName\":\"人才ETF\",\"OrganizationTag\":0,\"OrganizationType\":\"001001\",\"PageManagement\":\"0\",\"PageState\":\"0\",\"Portrait\":\"http://avator.eastmoney.com/qface/****************/120\",\"PrivateFundParam\":\"********\",\"RelatedUid\":\"****************\",\"SpreadId\":\"\",\"Summary\":\"这个人很懒，什么都没有留下\",\"UpdateTime\":\"2023-04-06T16:52:59\",\"approvalstatus\":3}";
        CFHBaseInfoBo cfhBaseInfo = service.getCFHBaseInfo(str);
        service.handCFHBaseInfo(cfhBaseInfo);

    }
    @Test
    public void testERROR(){
        String str = "{\"AccountName\":\"人才ETF\",\"AccountStatus\":1,\"AccountType\":\"沪深\",\"Banner\":\"\",\"BigVip\":0,\"CreateTime\":\"2022-11-17T10:19:59\",\"Email\":\"\",\"Hottype\":0,\"IsDeleted\":0,\"Name\":\"\",\"NewPrivateFundParam\":\"***********\",\"NickName\":\"人才ETF\",\"OrganizationTag\":0,\"OrganizationType\":\"001001\",\"PageManagement\":\"0\",\"PageState\":\"0\",\"Portrait\":\"http://avator.eastmoney.com/qface/****************/120\",\"PrivateFundParam\":\"********\",\"RelatedUid\":\"****************\",\"SpreadId\":\"\",\"Summary\":\"这个人很懒，什么都没有留下\",\"UpdateTime\":\"2023-04-06T16:52:59\",\"approvalstatus\":3}";
        CFHBaseInfoBo cfhBaseInfo = service.getCFHBaseInfo(str);
        service.handCFHBaseInfo(cfhBaseInfo);
    }

    @Test
    public void testInsertOrUpdateCfhUser(){
        CFHInfoDto cfhInfoDto = new CFHInfoDto();
        cfhInfoDto.setID("100031");
        cfhInfoDto.setCFHID("100031");
        cfhInfoDto.setRelatedUid("************");
        service.insertOrUpdateCfhUser(cfhInfoDto);
    }


    @Test
    public void testCfhUser(){
        String str = "{\"accountId\":\"795219\",\"accountName\":\"ETF和LOF圈\",\"accountStatus\":\"1\",\"accountType\":\"基金\",\"approvalStatus\":\"7\",\"banner\":\"ETF投资，指选国泰\",\"bigVip\":\"1\",\"createTime\":*************,\"email\":\"\",\"fundCompanyParam\":\"********\",\"hotType\":\"0\",\"isDeleted\":0,\"name\":\"国泰基金管理有限公司\",\"nickName\":\"ETF和LOF圈\",\"organizationTag\":\"1\",\"organizationType\":\"003002\",\"pageManagement\":\"2\",\"pageState\":1,\"portrait\":\"http://avator.eastmoney.com/qface/****************/120\",\"relatedUid\":\"123213\",\"spreadId\":\"\",\"summary\":\"ETF投资，指选国泰\",\"updateTime\":*************}";
        CFHBaseInfoBo cfhBaseInfo = service.getCFHBaseInfo(str);
        service.handCFHBaseInfo(cfhBaseInfo);
    }
}
