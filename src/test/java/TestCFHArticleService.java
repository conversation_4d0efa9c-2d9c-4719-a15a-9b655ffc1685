import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import ttfund.web.fortuneservice.FortuneServiceApplication;
import ttfund.web.fortuneservice.dao.CfhMongodbDao;
import ttfund.web.fortuneservice.service.UpdateCfhArticleService;

import java.util.Arrays;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = FortuneServiceApplication.class)
public class TestCFHArticleService {

    @Autowired
    private UpdateCfhArticleService service;

    @Autowired
    private CfhMongodbDao mongodb;

    @Test
    public void test(){
        //修改断点时间
        //存新断点
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("time", "2020-04-10 14:41:38");
        mongodb.upsertCFHGeneralData("ttfund.web.fortuneservice.handler.impl.UpdateCfhArticleServiceImpl:handCFHArticle", Arrays.asList(jsonObject));
        service.handCFHArticle();

    }
}
