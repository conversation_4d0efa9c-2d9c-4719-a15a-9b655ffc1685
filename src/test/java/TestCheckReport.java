//import org.apache.commons.lang3.StringUtils;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.invocation.InvocationOnMock;
//import org.mockito.stubbing.Answer;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//import ttfund.web.fortuneservice.FortuneServiceApplication;
//import ttfund.web.fortuneservice.config.CommonConfig;
//import ttfund.web.fortuneservice.dao.impl.CfhMongodbDaoImpl;
//import ttfund.web.fortuneservice.dao.impl.VerticaDaoImpl;
//import ttfund.web.fortuneservice.manager.DongDongManager;
//import ttfund.web.fortuneservice.model.dto.FundHighLevelInfo;
//import ttfund.web.fortuneservice.model.dto.FundSizeDto;
//import ttfund.web.fortuneservice.service.impl.CheckProductReportServiceImpl;
//import ttfund.web.fortuneservice.utils.CommonUtil;
//
//import java.math.BigDecimal;
//import java.util.*;
//
//
///**
// * <AUTHOR>
// * @date 2023/5/15 16:03
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = FortuneServiceApplication.class)
//public class TestCheckReport {
//
//    @InjectMocks
//    private CheckProductReportServiceImpl checkProductReportServiceImpl;
//
//    @Mock
//    private CfhMongodbDaoImpl cfhMongodbDao;
//
//    @Mock
//    private CommonConfig commonConfig;
//
//    @Mock
//    private VerticaDaoImpl verticaDao;
//
//    @Mock
//    private DongDongManager dongDongManager;
//
//    private int count = 5;
//
//    @Test
//    public void testCHeckReport(){
//
//        Mockito.when(cfhMongodbDao.getSpecialDayNumOfMonth(Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(10L);
//        Mockito.when(verticaDao.getFundCodeType()).thenReturn(getFundCodeList());
//        Mockito.when(cfhMongodbDao.getReportHistoryIdList()).thenAnswer(new Answer<Map<String, List<String>>>() {
//            @Override
//            public Map<String, List<String>> answer(InvocationOnMock invocation) throws Throwable {
//                    Map<String, List<String>> result = new HashMap<>();
//                    for (int i = 0; i < count; i++) {
//                        String code = String.format("SC273%s", i);
//                        List<String> list = Arrays.asList(produceNeedReportName(code, 2022, -1, 3), produceNeedReportName(code, 2023, 4, 1));
//                        result.put(code, list);
//                    }
//                    return result;
//                }
//        });
//        Mockito.when(verticaDao.getFundSizeInfo(Mockito.anyList())).thenReturn(getFundSizeList());
//        Mockito.when(cfhMongodbDao.batchSaveRegularReportList(Mockito.anyList())).thenReturn(4);
//        Mockito.when(cfhMongodbDao.batchSaveRegularReportContentList(Mockito.anyList())).thenReturn(10);
//        Mockito.when(dongDongManager.sendDongDongMessage(Mockito.anyList(), Mockito.anyString(), Mockito.anyString())).thenReturn(4);
//        checkProductReportServiceImpl.checkReport("2023-04-17");
//    }
//
//    private List<FundSizeDto> getFundSizeList() {
//        List<FundSizeDto> result = new ArrayList<>();
//        for (int i = 0; i < count; i++) {
//            FundSizeDto info = new FundSizeDto(String.format("SC273%2d",i), String.format("基金名称%s-测试", i), new BigDecimal(1000*i));
//            result.add(info);
//        }
//        return result;
//
//    }
//
//    private List<FundHighLevelInfo> getFundCodeList() {
//        List<FundHighLevelInfo> result = new ArrayList<>();
//        for (int i = 0; i < count; i++) {
//            FundHighLevelInfo info = new FundHighLevelInfo(String.format("SC273%2d",i), i%2, String.format("基金名称%s-测试", i));
//            result.add(info);
//        }
//        return result;
//    }
//
//    private Map<String, List<String>> getMap() {
//        Map<String, List<String>> result = new HashMap<>();
//        for (int i = 0; i < count; i++) {
//            String code = String.format("SC273%s", i);
//            List<String> list = Arrays.asList(produceNeedReportName(code, 2022, -1, 3), produceNeedReportName(code, 2023, 4, 1));
//            result.put(code, list);
//        }
//        return result;
//    }
//
//    /**
//     * 根据时间生成产品应该生成的报告名称 季度：生成CODE_
//     *
//     * @param code         产品id
//     * @param year         年份
//     * @param currentMonth 月份
//     * @param type         类型 1 季度  3年度
//     * @return map{k:产品id,v:产品检查记录名称}
//     */
//    private String produceNeedReportName(String code, int year, int currentMonth, int type) {
//        StringBuilder caseString = new StringBuilder("%s_%s");
//        int yearNew = year;
//        if (type == 1) {
//            int lastQuarterNum = CommonUtil.getLastQuarterNum(currentMonth);
//            caseString.append("Q").append(lastQuarterNum);
//            if (lastQuarterNum == 4) {
//                yearNew = year - 1;
//            }
//        }
//        return StringUtils.upperCase(String.format(caseString.toString(), code, yearNew));
//    }
//}
